// 用户相关的 API 接口
import request from '@/utils/request'

// 用户信息接口
export interface User {
  id: string
  createdAt: string
  displayName: string
  email: string
  addresses: any[]
  avatar: string
  level: number
  pointsBalance: number
  totalCashRecharged: number
  totalPointsSpent: number
  totalFusion: number
  clientSeed: string
  referred_by: string
  total_point_refered: number
  stripe_account_id: string
  totalAchievements: number
}

// 用户列表响应接口
export interface UserListResponse {
  users: User[]
  pagination: {
    total_items: number
    total_pages: number
    current_page: number
    per_page: number
  }
  filters: {
    sort_by: string
    sort_order: string
    search_query: string
  }
}

// 获取用户列表参数接口
export interface GetUserListParams {
  page: number
  per_page: number
  sort_by?: 'pointsBalance' | 'totalCashRecharged' | 'totalPointsSpent' | 'createdAt' | 'displayName' | 'level'
  sort_order?: 'asc' | 'desc'
  search_query?: string
}

// 获取用户列表
export function getUserList(params: GetUserListParams) {
  return request<UserListResponse>({
    url: '/users/',
    method: 'get',
    params
  })
}

// 获取用户详情
export function getUserDetail(userId: string) {
  return request<User>({
    url: `/users/${userId}`,
    method: 'get'
  })
}

// 检查用户是否为管理员
export function checkUserIsAdmin(userId: string) {
  return request<{ isAdmin: boolean }>({
    url: `/users/${userId}/is-admin`,
    method: 'get'
  })
}