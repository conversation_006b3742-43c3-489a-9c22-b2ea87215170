'use client'
import Link from 'next/link'
import Image from 'next/image'
import { useState, useEffect, useRef, createContext, useContext } from 'react'
import { usePathname, useRouter } from 'next/navigation'
import { useAuthStore } from '@/store/authStore'
import { isAuthenticated, getCurrentUserId } from '@/lib/authUtils'
import { userApi } from '@/lib/userApi'
import { packsApi, CollectionMetadata } from '@/lib/packsApi'
import { paymentApi } from '@/lib/paymentApi'
import PointsTopUpModal from '../PointsTopUpModal'
import toast from 'react-hot-toast'

// Create a Context to share selected collection globally
export interface CollectionContextType {
  selectedCollectionId: string;
  setSelectedCollectionId: (id: string) => void;
  collections: CollectionMetadata[];
  loadingCollections: boolean;
}

export const CollectionContext = createContext<CollectionContextType | null>(null);

// Custom Hook to get selected collection in other components
export const useCollection = () => {
  const context = useContext(CollectionContext);
  if (!context) {
    throw new Error('useCollection must be used within a CollectionProvider');
  }
  return context;
}

// Split Navbar component into two parts: CollectionProvider and actual navbar
export const CollectionProvider = ({ children }: { children: React.ReactNode }) => {
  const [collections, setCollections] = useState<CollectionMetadata[]>([])
  const [selectedCollectionId, setSelectedCollectionId] = useState<string>('')
  const [loadingCollections, setLoadingCollections] = useState(true)
  const pathname = usePathname()
  
  // Get collection metadata
  useEffect(() => {
    const fetchCollections = async () => {
      try {
        setLoadingCollections(true)
        const collectionsData = await packsApi.getCollectionMetadata()
        setCollections(collectionsData)
        
        // Get URL parameters
        const urlParams = new URLSearchParams(window.location.search)
        const collectionParam = urlParams.get('collection')
        
        // If URL has collection parameter and it's not undefined, set it as selected
        if (collectionParam && collectionParam !== 'undefined') {
          setSelectedCollectionId(collectionParam)
        } 
        // Otherwise, if there's collection data, default to pokemon
        else if (collectionsData.length > 0) {
          const pokemonCollection = collectionsData.find(c => c.id === 'pokemon')
          if (pokemonCollection) {
            setSelectedCollectionId('pokemon')
          } else {
            setSelectedCollectionId(collectionsData[0].id)
          }
        }
      } catch (error) {
        console.error('Failed to get collection metadata:', error)
      } finally {
        setLoadingCollections(false)
      }
    }

    fetchCollections()
  }, [])
  
  // This part has been moved to CollectionProvider component

  // Values provided to Context
  const collectionContextValue = {
    selectedCollectionId,
    setSelectedCollectionId,
    collections,
    loadingCollections
  }
  
  return (
    <CollectionContext.Provider value={collectionContextValue}>
      {children}
    </CollectionContext.Provider>
  )
}

const Navbar = () => {
  const router = useRouter()
  const pathname = usePathname()
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false)
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  const [isCategoryMenuOpen, setIsCategoryMenuOpen] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState("/header/collection-1.webp")
  const [isTopUpModalOpen, setIsTopUpModalOpen] = useState(false)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const { openLoginModal, openRegisterModal, userInfo, setUserInfo } = useAuthStore()

  const navRef = useRef<HTMLDivElement>(null)
  
  // Use CollectionContext
  const { collections, selectedCollectionId, setSelectedCollectionId, loadingCollections } = useCollection()
  
  // Refresh user profile
  const handleRefreshPoints = async () => {
    const userId = getCurrentUserId()
    if (!userId || isRefreshing) return
    
    try {
      setIsRefreshing(true)
      const updatedUserInfo = await userApi.getUserInfo()
      setUserInfo(updatedUserInfo)
    } catch (error) {
      console.error('Failed to refresh user info:', error)
    } finally {
      setIsRefreshing(false)
    }
  }
  
  // Handle Stripe Connect click
  const handleStripeConnect = async () => {
    try {
      // First check Stripe Connect status
      const status = await paymentApi.getStripeConnectStatus()
      
      if (status.status === 'not_connected') {
        // If not connected, create new Stripe Connect account
        const result = await paymentApi.createStripeConnect()
        if (result.onboarding_url) {
          window.open(result.onboarding_url, '_blank')
        }
      } else if (status.status === 'incomplete') {
        // If not completed, get onboarding link again
        const result = await paymentApi.createStripeConnect()
        if (result.onboarding_url) {
          window.open(result.onboarding_url, '_blank')
        }
      } else {
        // If completed, get dashboard link
        const result = await paymentApi.getStripeDashboardLink()
        if (result.login_url) {
          window.open(result.login_url, '_blank')
        }
      }
      
      // Close dropdown menu
      setIsUserMenuOpen(false)
    } catch (error) {
      console.error('Failed to handle Stripe Connect:', error)
      toast.error('Unable to open Stripe Connect, please try again later')
    }
  }
  
  useEffect(() => {
    // Check if user is logged in
    setIsLoggedIn(isAuthenticated())
    
    // If user is logged in but no user info, try to get user info
    const userId = getCurrentUserId()
    if (userId && !userInfo) {
      handleRefreshPoints()
    }
  }, [userInfo])
  
  // Set icon based on selectedCollectionId
  useEffect(() => {
    if (selectedCollectionId && collections.length > 0) {
      // Map collection ID to specific image
      const getCollectionImage = (collectionId: string) => {
        switch (collectionId) {
          case 'one_piece':
            return '/header/collection-3.webp'
          case 'pokemon':
            return '/header/collection-2.webp'
          case 'magic':
            return '/header/collection-4.webp'
          case 'yogiho':
            return '/header/collection-1.webp'
          default:
            // For other collections, use index mapping
            const collectionIndex = collections.findIndex(c => c.id === collectionId)
            return collectionIndex !== -1 ? `/header/collection-${collectionIndex + 1}.webp` : '/header/collection-1.webp'
        }
      }
      
      setSelectedCategory(getCollectionImage(selectedCollectionId))
    }
  }, [selectedCollectionId, collections])
  
  // Click outside to close dropdown menus
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (isCategoryMenuOpen && !target.closest('.category-menu-container')) {
        setIsCategoryMenuOpen(false);
      }
      if (isUserMenuOpen && !target.closest('.user-menu-container')) {
        setIsUserMenuOpen(false);
      }
      if (isMenuOpen && !target.closest('.mobile-menu-container') && !target.closest('.mobile-menu-button')) {
        setIsMenuOpen(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isCategoryMenuOpen, isUserMenuOpen, isMenuOpen]);

  // When selected collection changes and on homepage, update URL parameters
  useEffect(() => {
    if (selectedCollectionId && pathname === '/') {
      // Update URL parameters without reloading page
      const url = new URL(window.location.href)
      url.searchParams.set('collection', selectedCollectionId)
      window.history.pushState({}, '', url)
    }
  }, [selectedCollectionId, pathname])

  return (
    <>
      <PointsTopUpModal 
        isOpen={isTopUpModalOpen} 
        onClose={() => setIsTopUpModalOpen(false)}
        onSuccess={handleRefreshPoints}
      />
      <nav 
        ref={navRef} 
        className="fixed top-0 left-0 right-0 md:sticky md:left-auto md:right-auto z-50 bg-[#2A2B3D] py-2 border-b border-gray-700">
        <div className="w-full px-0">
          <div className="flex justify-between items-center px-4">
          {/* Logo and main navigation */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center pl-0">
              <div className="bg-blue-600 text-white font-bold text-xl px-4 py-1 rounded-md">LOGO</div>
            </Link>
            
            {/* Collection dropdown menu */}
            <div className="relative ml-2 category-menu-container">
              <button 
                className="flex items-center p-2 bg-[#1E1F2E] hover:bg-[#3F3F5F] rounded-md transition-colors"
                onClick={() => setIsCategoryMenuOpen(!isCategoryMenuOpen)}
              >
                <Image src={selectedCategory} alt="Categories" width={24} height={24} className="w-6 h-6" />
                <svg 
                  className={`w-4 h-4 ml-1 text-gray-400 transition-transform ${isCategoryMenuOpen ? 'rotate-180' : ''}`} 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24" 
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              
              {/* Dropdown menu */}
              {isCategoryMenuOpen && (
                <div className="absolute left-0 top-[calc(100%+8px)] w-[49px] bg-[#2A2B3D] border border-purple-500/20 rounded-[10px] shadow-xl overflow-hidden z-[100]">
                  <div className="py-1 flex flex-col items-center">
                    {/* Generate collection buttons dynamically based on metadata */}
                    {loadingCollections ? (
                      // Loading state
                      <div className="flex justify-center items-center w-full py-4 text-sm text-gray-400">
                        <svg className="animate-spin h-4 w-4 mr-2" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                      </div>
                    ) : collections.length > 0 ? (
                      // Show collection buttons
                      collections.map((collection, index) => {
                        // Map collection ID to specific image
                        const getCollectionImage = (collectionId: string) => {
                          switch (collectionId) {
                            case 'one_piece':
                              return '/header/collection-3.webp'
                            case 'pokemon':
                              return '/header/collection-2.webp'
                            case 'magic':
                              return '/header/collection-4.webp'
                            case 'yogiho':
                              return '/header/collection-1.webp'
                            default:
                              // For other collections, use index mapping
                              return `/header/collection-${index + 1}.webp`
                          }
                        }
                        const iconPath = getCollectionImage(collection.id);
                        return (
                          <button 
                            key={collection.id}
                            className="flex justify-center items-center w-full py-[8.5px] text-sm text-gray-300 hover:bg-purple-500/20 transition-colors relative group"
                            onClick={() => {
                              setSelectedCategory(iconPath);
                              setSelectedCollectionId(collection.id);
                              setIsCategoryMenuOpen(false);
                              
                              // If not on homepage, navigate to homepage with selected collection ID
                              if (pathname !== '/') {
                                router.push(`/?collection=${collection.id}`);
                              }
                              // If on homepage, URL params update is handled by CollectionProvider's useEffect
                            }}
                          >
                            <Image src={iconPath} alt={collection.name} width={24} height={24} className="w-6 h-6" style={{ height: 'auto' }} />
                            {/* Name shown on hover */}
                            <div className="absolute right-full mr-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                              {collection.name}
                            </div>
                          </button>
                        );
                      })
                    ) : (
                      // No collection data
                      <div className="flex justify-center items-center w-full py-4 text-sm text-gray-400">
                        No collections
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
            
            {/* Main navigation links - Desktop */}
            <div className="hidden md:flex items-center ml-8 space-x-3">
              <Link href="/" className="flex items-center px-3 py-2 text-gray-300 hover:text-white border-b-2 border-transparent hover:border-purple-500 transition-colors">
                <Image src="/header/home.webp" alt="Home" width={18} height={18} className="w-[18px] h-[18px]" />
              </Link>
              
              {/* Wrap synthesis, marketplace, achievement, rank in a div */}
              <div className="flex items-center mx-4 bg-[rgba(74,62,119,0.2)] rounded-md px-2 space-x-3">
                <Link href="/synthesis" className="flex items-center px-3 py-2 text-gray-300 hover:text-white border-b-2 border-transparent hover:border-purple-500 transition-colors">
                  <Image src="/header/synthesis.webp" alt="Fusion" width={18} height={18} className="w-[18px] h-[18px]" />
                  <span className="ml-1">Fusion</span>
                </Link>
                <Link href="/marketplace" className="flex items-center px-3 py-2 text-gray-300 hover:text-white border-b-2 border-transparent hover:border-purple-500 transition-colors">
                  <Image src="/header/Marketplace.webp" alt="Marketplace" width={18} height={18} className="w-[18px] h-[18px]" />
                  <span className="ml-1">Marketplace</span>
                </Link>
                <Link href="/achievements" className="flex items-center px-3 py-2 text-gray-300 hover:text-white border-b-2 border-transparent hover:border-purple-500 transition-colors">
                  <Image src="/header/achievement.webp" alt="Achievement" width={18} height={18} className="w-[18px] h-[18px]" />
                  <span className="ml-1">Achievement</span>
                </Link>
                <Link href="/rank" className="flex items-center px-3 py-2 text-gray-300 hover:text-white border-b-2 border-transparent hover:border-purple-500 transition-colors">
                  <Image src="/header/rankIcon.webp" alt="Rank" width={18} height={18} className="w-[18px] h-[18px]" />
                  <span className="ml-1">Rank</span>
                </Link>
              </div>
            </div>
          </div>

          {/* Right side features */}
          <div className="flex items-center space-x-4 pr-0">
            {isLoggedIn ? (
              // Logged in state - Show user info and balance
              <div className="hidden md:flex items-center space-x-4">
                {/* Balance display and refresh button */}
                <div className="flex items-center space-x-2">
                  {/* Balance display - Click to open top-up modal */}
                  <div 
                    className="flex items-center text-yellow-400 bg-[#1E1F2E] px-3 py-1 rounded-full cursor-pointer hover:bg-[#2A2B3D] transition-colors"
                    onClick={() => setIsTopUpModalOpen(true)}
                    title="Click to top up"
                  >
                    <Image src="/marketplace/coin.png" alt="Points" width={16} height={16} className="mr-1" />
                    <span>{userInfo?.pointsBalance || 0.00}</span>
                  </div>
                  
                  {/* Refresh points button */}
                   <button 
                     className={`flex items-center justify-center w-8 h-8 text-gray-400 hover:text-white bg-[#1E1F2E] hover:bg-[#2A2B3D] rounded-full transition-colors ${isRefreshing ? 'cursor-not-allowed opacity-50' : ''}`}
                     onClick={handleRefreshPoints}
                     disabled={isRefreshing}
                     title={isRefreshing ? 'Refreshing...' : 'Refresh user info'}
                   >
                     <svg xmlns="http://www.w3.org/2000/svg" className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                       <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                     </svg>
                   </button>
                </div>
                
                {/* User avatar and dropdown menu */}
                <div className="relative user-menu-container">
                  <button 
                    onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                    className="flex items-center space-x-2 text-white hover:text-[#8B5CF6] transition-colors"
                  >
                    <div className="flex items-center">
                      <div className="w-8 h-8 rounded-full overflow-hidden bg-gray-700 flex items-center justify-center border-2 border-purple-500">
                        {userInfo?.avatar ? (
                          <img src={userInfo.avatar} alt="User" className="w-full h-full object-cover" />
                        ) : (
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-300" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                          </svg>
                        )}
                      </div>
                      <span className="ml-2">{userInfo?.displayName || 'User'}</span>
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </button>
                  
                  {/* User dropdown menu */}
                  {isUserMenuOpen && (
                    <div className="absolute right-0 top-[calc(100%+8px)] w-56 bg-[#2A2B3D] rounded-lg shadow-xl border border-purple-500/20 py-2 z-[100] max-[768px]:right-[-1rem] max-[768px]:w-[calc(100vw-2rem)] max-[768px]:max-w-[300px]">
                      <div className="px-4 py-3 border-b border-gray-600">
                        <p className="text-white font-semibold text-sm">{userInfo?.displayName || 'User'}</p>
                        <p className="text-gray-400 text-xs">{userInfo?.email || '<EMAIL>'}</p>
                      </div>
                      
                      <div className="py-2">
                        <Link href="/user" className="flex items-center px-4 py-2 text-sm text-gray-300 hover:bg-[#3F3F5F] hover:text-white transition-all duration-200 font-medium" onClick={() => setIsUserMenuOpen(false)}>
                          <Image src="/users/setting.png" alt="Profile" width={16} height={16} className="mr-3" />
                          Profile
                        </Link>
                        <Link href="/user/address" className="flex items-center px-4 py-2 text-sm text-gray-300 hover:bg-[#3F3F5F] hover:text-white transition-all duration-200 font-medium" onClick={() => setIsUserMenuOpen(false)}>
                          <Image src="/users/box.png" alt="Addresses" width={16} height={16} className="mr-3" />
                          Addresses
                        </Link>
                        <Link href="/inventory" className="flex items-center px-4 py-2 text-sm text-gray-300 hover:bg-[#3F3F5F] hover:text-white transition-all duration-200 font-medium" onClick={() => setIsUserMenuOpen(false)}>
                          <Image src="/users/inventory.png" alt="Inventory" width={16} height={16} className="mr-3" />
                          Inventory
                        </Link>
                        <Link href="/listed" className="flex items-center px-4 py-2 text-sm text-gray-300 hover:bg-[#3F3F5F] hover:text-white transition-all duration-200 font-medium" onClick={() => setIsUserMenuOpen(false)}>
                          <Image src="/users/listing.png" alt="My Listing" width={16} height={16} className="mr-3" />
                          My Listing
                        </Link>
                        <Link href="/user/withdrawals" className="flex items-center px-4 py-2 text-sm text-gray-300 hover:bg-[#3F3F5F] hover:text-white transition-all duration-200 font-medium" onClick={() => setIsUserMenuOpen(false)}>
                          <Image src="/users/withdrawals.png" alt="Withdrawals" width={16} height={16} className="mr-3" />
                          Withdrawals
                        </Link>
                        <Link href="/user/affiliate" className="flex items-center px-4 py-2 text-sm text-gray-300 hover:bg-[#3F3F5F] hover:text-white transition-all duration-200 font-medium" onClick={() => setIsUserMenuOpen(false)}>
                          <Image src="/users/affiliate.png" alt="Affiliate" width={16} height={16} className="mr-3" />
                          Affiliate
                        </Link>
                        <Link href="/history" className="flex items-center px-4 py-2 text-sm text-gray-300 hover:bg-[#3F3F5F] hover:text-white transition-all duration-200 font-medium" onClick={() => setIsUserMenuOpen(false)}>
                          <Image src="/users/history.png" alt="History" width={16} height={16} className="mr-3" />
                          History
                        </Link>
                        <Link href="/support" className="flex items-center px-4 py-2 text-sm text-gray-300 hover:bg-[#3F3F5F] hover:text-white transition-all duration-200 font-medium" onClick={() => setIsUserMenuOpen(false)}>
                          <Image src="/users/support.png" alt="Support" width={16} height={16} className="mr-3" />
                          Support
                        </Link>
                      </div>
                      
                      <div className="border-t border-gray-600 py-2">
                        <button 
                          onClick={() => {
                            handleStripeConnect();
                            setIsUserMenuOpen(false);
                          }}
                          className="flex items-center w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-[#3F3F5F] hover:text-white transition-all duration-200 font-medium"
                        >
                          <Image src="/payment/stripe.png" alt="Stripe Connect" width={16} height={16} className="mr-3" />
                          Stripe Connect
                        </button>
                        
                        <button 
                          onClick={async () => {
                            const { logout } = useAuthStore.getState();
                            
                            // Sign out from Firebase
                            try {
                              const { signOut } = await import('firebase/auth');
                              const { auth } = await import('@/lib/firebase');
                              await signOut(auth);
                            } catch (error) {
                              console.error('Firebase signout error:', error);
                            }
                            
                            // Clear auth store
                            logout();
                            
                            // Clear all session storage
                            sessionStorage.clear();
                            
                            // Clear specific localStorage items related to auth
                            localStorage.removeItem('auth-storage');
                            
                            setIsUserMenuOpen(false);
                            
                            // Optionally reload the page to ensure clean state
                            window.location.href = '/';
                          }}
                          className="flex items-center w-full text-left px-4 py-2 text-sm text-red-400 hover:bg-red-500/10 hover:text-red-300 transition-all duration-200 font-medium mt-1"
                        >
                          <Image src="/users/out.png" alt="Sign Out" width={16} height={16} className="mr-3" />
                          Sign Out
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ) : (
              // Not logged in - Show login/register buttons
              <div className="hidden md:flex items-center space-x-2">
                <button 
                  onClick={openLoginModal}
                  className="px-4 py-2 text-white hover:text-[#8B5CF6] transition-colors"
                >
                  Sign in
                </button>
                <button 
                  onClick={openRegisterModal}
                  className="px-4 py-2 bg-[#8B5CF6] text-white rounded-md hover:bg-[#7C3AED] transition-colors"
                >
                  Sign Up
                </button>
              </div>
            )}

            {/* Mobile points display and hamburger menu */}
            <div className="md:hidden flex items-center space-x-2">
              {/* Points display for mobile - shown when logged in */}
              {isLoggedIn && (
                <div 
                  className="flex items-center text-yellow-400 bg-[#1E1F2E] px-2 py-1 rounded-full cursor-pointer hover:bg-[#2A2B3D] transition-colors text-sm"
                  onClick={() => setIsTopUpModalOpen(true)}
                  title="Click to top up"
                >
                  <Image src="/marketplace/coin.png" alt="Points" width={12} height={12} className="mr-1" />
                  <span className="font-medium">{userInfo?.pointsBalance || 0.00}</span>
                </div>
              )}
              
              {/* Hamburger menu button */}
              <button 
                className="text-gray-300 hover:text-white focus:outline-none mobile-menu-button" 
                onClick={() => setIsMenuOpen(!isMenuOpen)}
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  {isMenuOpen ? (
                    <Image src="/icons/close.png" alt="Close" width={24} height={24} />
                  ) : (
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                  )}
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
      </nav>
      
      {/* Mobile menu */}
      {isMenuOpen && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 z-40 md:hidden bg-black/40"
            onClick={() => setIsMenuOpen(false)}
          />
          {/* Panel */}
<div className="fixed md:hidden" style={{ top: 'var(--navbar-height-mobile)', left: 0, right: 0, zIndex: 50 }}>
            cdiv className="pb-4 border-t border-gray-700 mobile-menu-container bg-[#2A2B3D] mx-4 rounded-lg shadow-lg overflow-y-auto" style={{ maxHeight: 'calc(100vh - var(--navbar-height-mobile))' }}e
              <div className="flex flex-col space-y-3 p-4">
                <Link href="/" className="text-gray-300 hover:text-white hover:bg-[#3F3F5F] py-3 px-3 rounded-md flex items-center transition-all duration-200" onClick={() => setIsMenuOpen(false)}>
                  <Image src="/header/home.webp" alt="Home" width={18} height={18} className="w-[18px] h-[18px]" />
                  <span className="ml-3 font-medium">Home</span>
                </Link>
                
                {/* Mobile menu synthesis, marketplace, achievement, rank group */}
                <div className="bg[rgba(74,62,119,0.3)] rounded-lg py-2 px-2 space-y-2 border border-purple-500/20">
                  <Link href="/synthesis" className="text-gray-300 hover:text-white hover:bg-[#3F3F5F] py-3 px-3 rounded-md flex items-center transition-all duration-200" onClick={() => setIsMenuOpen(false)}>
                    <Image src="/header/synthesis.webp" alt="Fusion" width={18} height={18} className="w-[18px] h-[18px]" />
                    <span className="ml-3 font-medium">Fusion</span>
                  </Link>
                  <Link href="/marketplace" className="text-gray-300 hover:text-white hover:bg-[#3F3F5F] py-3 px-3 rounded-md flex items-center transition-all duration-200" onClick={() => setIsMenuOpen(false)}>
                    <Image src="/header/Marketplace.webp" alt="Marketplace" width={18} height={18} className="w-[18px] h-[18px]" />
                    <span className="ml-3 font-medium">Marketplace</span>
                  </Link>
                  <Link href="/achievements" className="text-gray-300 hover:text-white hover:bg-[#3F3F5F] py-3 px-3 rounded-md flex items-center transition-all duration-200" onClick={() => setIsMenuOpen(false)}>
                    <Image src="/header/achievement.webp" alt="Achievement" width={18} height={18} className="w-[18px] h-[18px]" />
                    <span className="ml-3 font-medium">Achievement</span>
                  </Link>
                  <Link href="/rank" className="text-gray-300 hover:text-white hover:bg-[#3F3F5F] py-3 px-3 rounded-md flex items-center transition-all duration-200" onClick={() => setIsMenuOpen(false)}>
                    <Image src="/header/rankIcon.webp" alt="Rank" width={18} height={18} className="w-[18px] h-[18px]" />
                    <span className="ml-3 font-medium">Rank</span>
                  </Link>
                </div>
              </div>
              {isLoggedIn ? (
                // Logged in state - Mobile user menu
                <div className="flex flex-col space-y-2 mt-4 p-4 bg-[rgba(74,62,119,0.1)] rounded-lg border border-purple-500/20">
                  <div className="flex items-center justify-between px-3 py-3 bg-[rgba(74,62,119,0.3)] rounded-lg border border-purple-500/30">
                    <div className="flex items-center">
                      <div className="w-10 h-10 rounded-full overflow-hidden bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center mr-3 border-2 border-purple-400 shadow-lg">
                        {userInfo?.avatar ? (
                          <img src={userInfo.avatar} alt="User" className="w-full h-full object-cover" />
                        ) : (
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                          </svg>
                        )}
                      </div>
                      <div>
                        <div className="text-white font-semibold">{userInfo?.displayName || 'User'}</div>
                        <div className="text-yellow-400 text-sm flex items-center">
                          <div 
                            className="cursor-pointer hover:text-yellow-300 transition-colors mr-2 flex items-center"
                            onClick={() => setIsTopUpModalOpen(true)}
                            title="Click to top up"
                          >
                            <Image src="/marketplace/coin.png" alt="Points" width={16} height={16} className="mr-1" />
                            <span className="font-medium">{userInfo?.pointsBalance || 0.00}</span>
                          </div>
                          <button 
                            onClick={handleRefreshPoints}
                            disabled={isRefreshing}
                            className="ml-1 p-1 hover:bg-gray-600 rounded-full transition-colors"
                            title="Refresh user info"
                          >
                            <svg 
                              xmlns="http://www.w3.org/2000/svg" 
                              className={`h-3 w-3 ${isRefreshing ? 'animate-spin' : ''}`} 
                              fill="none" 
                              viewBox="0 0 24 24" 
                              stroke="currentColor"
                            >
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <Link href="/user" className="flex items-center px-3 py-3 text-gray-300 hover:bg-[#3F3F5F] hover:text-white rounded-md transition-all duration-200 font-medium" onClick={() => setIsMenuOpen(false)}>
                    <Image src="/users/setting.png" alt="Profile" width={18} height={18} className="mr-3" style={{ width: 'auto', height: 'auto' }} />
                    Profile
                  </Link>
                  <Link href="/user/address" className="flex items-center px-3 py-3 text-gray-300 hover:bg-[#3F3F5F] hover:text-white rounded-md transition-all duration-200 font-medium" onClick={() => setIsMenuOpen(false)}>
                    <Image src="/users/box.png" alt="Addresses" width={18} height={18} className="mr-3" style={{ width: 'auto', height: 'auto' }} />
                    Addresses
                  </Link>
                  <Link href="/inventory" className="flex items-center px-3 py-3 text-gray-300 hover:bg-[#3F3F5F] hover:text-white rounded-md transition-all duration-200 font-medium" onClick={() => setIsMenuOpen(false)}>
                    <Image src="/users/inventory.png" alt="Inventory" width={18} height={18} className="mr-3" style={{ width: 'auto', height: 'auto' }} />
                    Inventory
                  </Link>
                  <Link href="/listed" className="flex items-center px-3 py-3 text-gray-300 hover:bg-[#3F3F5F] hover:text-white rounded-md transition-all duration-200 font-medium" onClick={() => setIsMenuOpen(false)}>
                    <Image src="/users/listing.png" alt="My Listing" width={18} height={18} className="mr-3" style={{ width: 'auto', height: 'auto' }} />
                    My Listing
                  </Link>
                  <Link href="/user/withdrawals" className="flex items-center px-3 py-3 text-gray-300 hover:bg-[#3F3F5F] hover:text-white rounded-md transition-all duration-200 font-medium" onClick={() => setIsMenuOpen(false)}>
                    <Image src="/users/withdrawals.png" alt="Withdrawals" width={18} height={18} className="mr-3" style={{ width: 'auto', height: 'auto' }} />
                    Withdrawals
                  </Link>
                  <Link href="/user/affiliate" className="flex items-center px-3 py-3 text-gray-300 hover:bg-[#3F3F5F] hover:text-white rounded-md transition-all duration-200 font-medium" onClick={() => setIsMenuOpen(false)}>
                    <Image src="/users/affiliate.png" alt="Affiliate" width={18} height={18} className="mr-3" style={{ width: 'auto', height: 'auto' }} />
                    Affiliate
                  </Link>
                  
                  <div className="border-t border-gray-600 pt-2 mt-2">
                    <button 
                      onClick={() => {
                        handleStripeConnect();
                        setIsMenuOpen(false);
                      }}
                      className="flex items-center px-3 py-3 text-gray-300 hover:bg-[#3F3F5F] hover:text-white rounded-md w-full text-left transition-all duration-200 font-medium"
                    >
                      <Image src="/payment/stripe.png" alt="Stripe Connect" width={18} height={18} className="mr-3" style={{ width: 'auto', height: 'auto' }} />
                      Stripe Connect
                    </button>
                    <Link href="/history" className="flex items-center px-3 py-3 text-gray-300 hover:bg-[#3F3F5F] hover:text-white rounded-md transition-all duration-200 font-medium" onClick={() => setIsMenuOpen(false)}>
                      <Image src="/users/history.png" alt="History" width={18} height={18} className="mr-3" style={{ width: 'auto', height: 'auto' }} />
                      History
                    </Link>
                    <Link href="/support" className="flex items-center px-3 py-3 text-gray-300 hover:bg-[#3F3F5F] hover:text-white rounded-md transition-all duration-200 font-medium" onClick={() => setIsMenuOpen(false)}>
                      <Image src="/users/support.png" alt="Support" width={18} height={18} className="mr-3" style={{ width: 'auto', height: 'auto' }} />
                      Support
                    </Link>
                    <button 
                      onClick={async () => {
                        const { logout } = useAuthStore.getState();
                        
                        // Sign out from Firebase
                        try {
                          const { signOut } = await import('firebase/auth');
                          const { auth } = await import('@/lib/firebase');
                          await signOut(auth);
                        } catch (error) {
                          console.error('Firebase signout error:', error);
                        }
                        
                        // Clear auth store
                        logout();
                        
                        // Clear all session storage
                        sessionStorage.clear();
                        
                        // Clear specific localStorage items related to auth
                        localStorage.removeItem('auth-storage');
                        
                        setIsMenuOpen(false);
                        
                        // Optionally reload the page to ensure clean state
                        window.location.href = '/';
                      }}
                      className="flex items-center px-3 py-3 text-red-400 hover:bg-red-500/10 hover:text-red-300 rounded-md w-full text-left transition-all duration-200 font-medium mt-2"
                    >
                      <Image src="/users/out.png" alt="Sign Out" width={18} height={18} className="mr-3" style={{ width: 'auto', height: 'auto' }} />
                      Sign Out
                    </button>
                  </div>
                </div>
              ) : (
                // Not logged in - Mobile login/register buttons
                <div className="flex flex-col space-y-3 mt-4 p-4 bg-[rgba(74,62,119,0.1)] rounded-lg border border-purple-500/20">
                  <button 
                    onClick={() => {
                      openLoginModal();
                      setIsMenuOpen(false);
                    }}
                    className="text-gray-300 hover:text-white hover:bg-[#3F3F5F] py-3 px-4 text-center rounded-md transition-all duration-200 font-medium"
                  >
                    Sign in
                  </button>
                  <button 
                    onClick={() => {
                      openRegisterModal();
                      setIsMenuOpen(false);
                    }}
                    className="bg-gradient-to-r from-[#8B5CF6] to-[#7C3AED] text-white py-3 px-4 rounded-lg hover:from-[#7C3AED] hover:to-[#6D28D9] transition-all duration-200 text-center font-semibold shadow-lg"
                  >
                    Sign Up
                  </button>
                </div>
              )}
            </div>
          </div>
        </>
      )}
        
    </>
  )
}

export default Navbar