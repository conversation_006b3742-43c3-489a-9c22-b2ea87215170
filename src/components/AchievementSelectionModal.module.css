.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
  padding: 1rem;
}

.modal {
  background: linear-gradient(135deg, #1A1B2C 0%, #2A2B3D 100%);
  border-radius: 20px;
  width: 100%;
  max-width: 72rem;
  max-height: 90vh;
  overflow: hidden;
  border: 2px solid #8868FF;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid #3F3F5F;
}

.title {
  font-size: 1.5rem;
  font-weight: bold;
  color: white;
}

.closeButton {
  cursor: pointer;
  background: none;
  border: none;
  padding: 0;
  font-size: 20px;
  color: #fff;
}

.closeButton:hover {
  color: white;
  background-color: #5A5B6F;
}

.content {
  display: flex;
  height: calc(90vh - 120px);
}

.preview {
  width: 33.333333%;
  padding: 1.5rem;
  border-right: 1px solid #3F3F5F;
}

.previewHeader {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.previewIcon {
  width: 1.5rem;
  height: 1.5rem;
  background-color: #8B5CF6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.75rem;
}

.previewTitle {
  font-size: 1.125rem;
  font-weight: 600;
  color: white;
}

.previewCard {
  background-color: #1A1B2E;
  border-radius: 0.75rem;
  padding: 1rem;
  height: 400px;
  display: flex;
  flex-direction: column;
}

.achievementsList {
  flex: 1;
  padding: 1.5rem;
}

.searchContainer {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.searchInput {
  flex: 1;
  position: relative;
}

.searchInput input {
  width: 100%;
  padding: 0.5rem 1rem;
  background-color: #1A1B2E;
  border: 1px solid #3F3F5F;
  border-radius: 0.5rem;
  color: white;
  transition: border-color 0.2s;
}

.searchInput input::placeholder {
  color: #9CA3AF;
}

.searchInput input:focus {
  outline: none;
  border-color: #8B5CF6;
}

.filterSelect {
  padding: 0.5rem 1rem;
  background-color: #1A1B2E;
  border: 1px solid #3F3F5F;
  border-radius: 0.5rem;
  color: white;
}

.filterSelect:focus {
  outline: none;
  border-color: #8B5CF6;
}

.achievementsGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  max-height: 500px;
  overflow-y: auto;
}

.achievementCard {
  background-color: #1A1B2E;
  border-radius: 0.5rem;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.2s;
  border: 2px solid transparent;
}

.achievementCard:hover {
  background-color: #252640;
}

.achievementCard.selected {
  border-color: #8B5CF6;
}

.achievementCard.completed {
  box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.3);
}

.achievementIcon {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 0.75rem;
  background: linear-gradient(135deg, #8B5CF6, #EC4899);
}

.achievementName {
  color: white;
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
  text-align: center;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.achievementDescription {
  color: #9CA3AF;
  font-size: 0.75rem;
  margin-bottom: 0.5rem;
  text-align: center;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.progressBar {
  width: 100%;
  background-color: #374151;
  border-radius: 9999px;
  height: 0.375rem;
  margin-bottom: 0.5rem;
}

.progressFill {
  background-color: #FBBF24;
  height: 0.375rem;
  border-radius: 9999px;
  transition: width 0.3s;
}

.statusText {
  font-size: 0.75rem;
  text-align: center;
}

.statusCompleted {
  color: #10B981;
  font-weight: 600;
}

.statusProgress {
  color: #FBBF24;
}

.confirmButton {
  width: 100%;
  margin-top: 1rem;
  padding: 0.75rem 1rem;
  background-color: #8B5CF6;
  color: white;
  border-radius: 0.5rem;
  border: none;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.confirmButton:hover:not(:disabled) {
  background-color: #7C3AED;
}

.confirmButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.emptyState {
  text-align: center;
  padding: 3rem 0;
}

.emptyIcon {
  width: 4rem;
  height: 4rem;
  border-radius: 50%;
  background-color: #3F3F5F;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
}

.emptyText {
  color: #9CA3AF;
}

@media (max-width: 768px) {
  .content {
    flex-direction: column;
    height: auto;
  }
  
  .preview {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid #3F3F5F;
  }
  
  .achievementsGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .achievementsGrid {
    grid-template-columns: 1fr;
  }
  
  .searchContainer {
    flex-direction: column;
  }
}