# 移动端 Redeem Points 弹窗测试指南

## 测试环境设置

### 1. 浏览器开发者工具测试

#### Chrome/Edge 测试步骤：
1. 打开 http://localhost:3000/inventory
2. 按 F12 打开开发者工具
3. 点击设备切换按钮（📱图标）或按 Ctrl+Shift+M
4. 选择移动设备模拟：
   - iPhone 12 Pro (390x844)
   - iPhone SE (375x667)
   - Samsung Galaxy S20 Ultra (412x915)
   - iPad (768x1024)

#### Firefox 测试步骤：
1. 打开 http://localhost:3000/inventory
2. 按 F12 打开开发者工具
3. 点击响应式设计模式按钮或按 Ctrl+Shift+M
4. 设置视口尺寸为移动设备

### 2. 真实设备测试

#### 移动设备访问：
1. 确保移动设备与开发机在同一网络
2. 获取开发机IP地址（如：*************）
3. 在移动设备浏览器访问：http://[IP地址]:3000/inventory
4. 例如：http://*************:3000/inventory

## 测试场景

### 场景1：基本功能测试

#### 桌面端测试（屏幕宽度 ≥ 768px）：
1. 选择一些卡片
2. 点击 "Redeem points" 按钮
3. 验证显示传统的桌面端弹窗
4. 检查布局是否正常

#### 移动端测试（屏幕宽度 < 768px）：
1. 选择一些卡片
2. 点击 "Redeem points" 按钮
3. 验证显示新的移动端弹窗
4. 检查是否为全屏显示
5. 验证底部滑入动画

### 场景2：移动端交互测试

#### 步骤导航测试：
1. 打开移动端弹窗
2. 验证显示两个步骤："Select Cards" 和 "Confirm"
3. 点击步骤标签，验证可以切换
4. 检查当前步骤的高亮显示

#### 卡片选择测试：
1. 在 "Select Cards" 步骤中
2. 验证卡片列表显示正常
3. 测试数量输入框：
   - 输入有效数量
   - 输入超出范围的数量
   - 输入非数字字符
4. 验证积分实时计算
5. 检查过期卡片的禁用状态

#### 确认步骤测试：
1. 点击 "Continue to Confirm" 按钮
2. 验证切换到确认步骤
3. 检查操作摘要显示
4. 验证总积分计算正确
5. 检查警告信息显示
6. 测试 "Back" 和 "Destroy Cards" 按钮

### 场景3：响应式切换测试

#### 动态切换测试：
1. 在桌面端打开弹窗
2. 调整浏览器窗口大小至移动端尺寸
3. 关闭弹窗重新打开
4. 验证显示移动端版本
5. 再次调整至桌面端尺寸
6. 验证切换回桌面端版本

### 场景4：错误处理测试

#### 错误消息测试：
1. 不选择任何卡片点击确认
2. 验证错误消息显示
3. 检查错误消息自动消失（3秒后）
4. 测试手动关闭错误消息

## 预期结果

### 移动端弹窗特征：
✅ 全屏显示（w-full h-full）
✅ 底部滑入动画
✅ 紫色渐变背景
✅ 步骤导航栏
✅ 紧凑的卡片布局
✅ 触摸友好的按钮大小
✅ 顶部错误提示

### 桌面端弹窗特征：
✅ 居中模态框
✅ 半透明背景遮罩
✅ 固定宽度布局
✅ 传统的表格式卡片显示

## 常见问题排查

### 问题1：移动端弹窗未显示
**可能原因：**
- 屏幕宽度检测错误
- JavaScript 错误
- 组件导入问题

**排查步骤：**
1. 检查浏览器控制台错误
2. 验证屏幕宽度值
3. 检查组件导入路径

### 问题2：动画效果异常
**可能原因：**
- framer-motion 依赖缺失
- CSS 冲突
- 性能问题

**排查步骤：**
1. 检查 framer-motion 是否正确安装
2. 验证 CSS 样式是否冲突
3. 测试不同设备性能

### 问题3：响应式切换不生效
**可能原因：**
- 窗口大小监听器未正确设置
- 状态更新延迟
- 缓存问题

**排查步骤：**
1. 检查 resize 事件监听器
2. 验证状态更新逻辑
3. 清除浏览器缓存

## 性能测试

### 动画性能：
1. 使用 Chrome DevTools Performance 面板
2. 录制弹窗打开/关闭动画
3. 检查 FPS 是否稳定在 60fps
4. 验证无明显的布局抖动

### 内存使用：
1. 多次打开/关闭弹窗
2. 检查内存是否正确释放
3. 验证无内存泄漏

## 兼容性测试

### 浏览器兼容性：
- ✅ Chrome 90+
- ✅ Firefox 88+
- ✅ Safari 14+
- ✅ Edge 90+

### 设备兼容性：
- ✅ iPhone (iOS 14+)
- ✅ Android (Chrome 90+)
- ✅ iPad (iPadOS 14+)
- ✅ 各种屏幕尺寸

## 测试检查清单

### 基础功能 ✓
- [ ] 桌面端弹窗正常显示
- [ ] 移动端弹窗正常显示
- [ ] 响应式切换正常
- [ ] 卡片选择功能正常
- [ ] 数量输入功能正常
- [ ] 积分计算正确
- [ ] 确认功能正常

### 用户体验 ✓
- [ ] 动画流畅
- [ ] 触摸交互友好
- [ ] 步骤导航清晰
- [ ] 错误提示明确
- [ ] 视觉效果一致

### 技术质量 ✓
- [ ] 无控制台错误
- [ ] 性能表现良好
- [ ] 代码结构清晰
- [ ] 组件复用性好

## 反馈收集

测试完成后，请记录：
1. 发现的问题和建议
2. 用户体验评价
3. 性能表现评估
4. 改进建议

这将帮助我们持续优化移动端用户体验。