.modalOverlay {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 50;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

/* Prevent background scroll when modal is open */
body.modal-open {
  overflow: hidden;
}

.modalContainer {
  background: linear-gradient(0deg, #0F111C 0%, #1F2235 99%);
  border-radius: 20px;
  border: 2px solid #8B5CF6;
  width: 100%;
  max-width: 480px;
  position: relative;
  overflow: hidden;
}

.closeButton {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  color: white;
  transition: color 0.2s;
  z-index: 20;
  background: rgba(0,0,0,0.25);
  border: 1px solid rgba(255,255,255,0.25);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 9999px;
}

.closeButton:hover {
  color: #D1D5DB;
}

.modalHeader {
  padding: 1.5rem 1.5rem 1rem;
}

.modalTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: white;
  margin: 0;
}

.modalSubtitle {
  font-size: 0.875rem;
  color: #9CA3AF;
  margin-top: 0.25rem;
}

.modalBody {
  padding: 0 1.5rem 0.5rem;
}

.infoSection {
  margin-bottom: 1rem;
}

.boxSection {
  margin-bottom: 1rem;
}

.outcomeSection {
  margin-bottom: 1rem;
}

.infoLabel {
  font-size: 0.875rem;
  color: #9CA3AF;
  margin-bottom: 0.25rem;
}

.infoContent {
  display: flex;
  align-items: center;
}

.boxContent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: rgba(42, 43, 61, 0.6);
  border-radius: 8px;
  padding: 1rem;
}

.boxInfo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.boxText {
  color: white;
  font-size: 0.875rem;
}

.boxPrice {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.coinIcon {
  width: 1rem;
  height: 1rem;
}

.priceText {
  color: #FFD700;
  font-weight: 600;
  font-size: 0.875rem;
}

.outcomeContent {
  background-color: rgba(42, 43, 61, 0.6);
  border-radius: 8px;
  padding: 1rem;
}

.outcomeCard {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.cardImage {
  width: 3rem;
  height: 3rem;
  border-radius: 6px;
  object-fit: cover;
}

.cardInfo {
  flex: 1;
}

.cardName {
  color: white;
  font-weight: 600;
  font-size: 0.875rem;
  margin: 0;
}

.cardDescription {
  color: #9CA3AF;
  font-size: 0.75rem;
  margin: 0.25rem 0;
}

.cardId {
  color: #9CA3AF;
  font-size: 0.75rem;
  margin: 0;
}

.infoText {
  color: white;
  font-size: 0.875rem;
  margin-right: 0.5rem;
}

.infoLink {
  color: #8B5CF6;
  font-size: 0.875rem;
  text-decoration: none;
}

.infoLink:hover {
  color: #7C3AED;
}

.iconContainer {
  width: 2rem;
  height: 2rem;
  background-color: #8B5CF6;
  border-radius: 0.375rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.5rem;
}

.icon {
  width: 1.25rem;
  height: 1.25rem;
}

.infoGrid {
  display: grid;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
  width: 100%;
}

.infoItem {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-width: 0;
}

.infoValue {
  display: flex;
  align-items: center;
  position: relative;
  width: 100%;
  overflow: visible;
}

.infoValueText {
  color: white;
  font-size: 0.875rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex-grow: 1;
  padding-right: 1.75rem;
  width: 100%;
  max-width: 100%;
}

.copyButton {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(139, 92, 246, 0.25);
  border: 1px solid #8B5CF6;
  color: #F5F3FF;
  cursor: pointer;
  padding: 0.25rem 0.4rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: color 0.2s, background 0.2s, border-color 0.2s, transform 0.15s ease;
}

.copyButton:hover {
  color: white;
  background: rgba(139, 92, 246, 0.4);
  border-color: #A78BFA;
  transform: translateY(-50%) scale(1.05);
}

.copyIcon {
  width: 1rem;
  height: 1rem;
}

.copySuccess {
  position: absolute;
  top: -1.5rem;
  right: 0;
  background-color: #10B981;
  color: white;
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  opacity: 0;
  transform: translateY(0.5rem);
  transition: opacity 0.2s, transform 0.2s;
  pointer-events: none;
  z-index: 15;
}

.copySuccessVisible {
  opacity: 1;
  transform: translateY(0);
}

.modalFooter {
  background-color: #191A27;
  padding: 1rem 1.5rem;
  font-size: 0.75rem;
  color: #9CA3AF;
}

/* 移动端适配 */
@media (max-width: 640px) {
  .modalContainer {
    max-width: 100%;
    margin: 0 1rem;
    border-radius: 0.5rem;
  }
  
  .modalHeader {
    padding: 1.25rem 1.25rem 0.75rem;
  }
  
  .modalBody {
    padding: 0 1.25rem 0.5rem;
  }
  
  .infoGrid {
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 0.75rem;
    width: 100%;
  }
  
  .modalFooter {
    padding: 0.75rem 1.25rem;
  }
  
  .iconContainer {
    width: 1.75rem;
    height: 1.75rem;
  }
  
  .icon {
    width: 1rem;
    height: 1rem;
  }
  
  .infoValueText {
    font-size: 0.75rem;
    padding-right: 1.5rem;
  }
  
  .copyButton {
    padding: 0.15rem;
  }
  
  .copyIcon {
    width: 0.875rem;
    height: 0.875rem;
  }
  
  .copySuccess {
    font-size: 0.7rem;
    padding: 0.15rem 0.35rem;
    top: -1.25rem;
  }
}
