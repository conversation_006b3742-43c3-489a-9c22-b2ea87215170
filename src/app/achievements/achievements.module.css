.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  padding: 20px;
  padding-top: 32px;
  color: white;
}

@media (min-width: 640px) {
  .container {
    padding-top: 20px;
  }
}

.groupedContainer {
  display: flex;
  flex-direction: column;
  gap: 28px;
}

.achievementGroup {
  display: flex;
  flex-direction: column;
  gap: 14px;
}

.groupHeader {
  cursor: pointer;
  user-select: none;
  transition: all 0.3s ease;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
}

.groupHeader:hover {
  background: rgba(255, 255, 255, 0.07);
  border-color: rgba(255, 255, 255, 0.2);
}

.groupTitle {
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
  padding-bottom: 6px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.15);
  margin-bottom: 6px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.dropdownArrow {
  font-size: 16px;
  transition: transform 0.2s ease;
  display: inline-block;
}

.groupCount {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.6);
  font-weight: 400;
  margin-left: auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  flex-wrap: wrap;
  gap: 20px;
}

.controlsContainer {
  display: flex;
  align-items: center;
  gap: 30px;
  flex-wrap: wrap;
}

.expandControls {
  display: flex;
  gap: 10px;
}

.expandButton {
  padding: 10px 18px;
  border: none;
  border-radius: 999px;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.9), rgba(118, 75, 162, 0.9));
  color: white;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: transform 0.2s ease, box-shadow 0.2s ease, opacity 0.2s ease;
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.35);
}

.expandButton:hover {
  transform: translateY(-1px);
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.45);
}

.expandButton:active {
  transform: translateY(0);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.35);
}


.filterTabs {
  display: flex;
  gap: 10px;
}

.tab {
  padding: 10px 20px;
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 999px;
  background: rgba(255, 255, 255, 0.06);
  color: #eaeaea;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 600;
  backdrop-filter: blur(6px);
}

.tab:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(255, 255, 255, 0.25);
}

.tab:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.activeTab {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-color: transparent;
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.45);
}


.achievementsGrid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 12px;
  margin-bottom: 20px;
}

.achievementCard {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 10px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  cursor: pointer; /* 添加点击指针 */
  display: flex;
  flex-direction: column;
  align-items: center;
}

.achievementCard:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.08); /* 悬停时背景变亮 */
}

.achievementCard:active {
  transform: translateY(-2px); /* 点击时的反馈 */
}

.achieved {
  border-color: #4ade80;
  background: rgba(74, 222, 128, 0.1);
}

.notAchieved {
  opacity: 0.7;
}

.achievementIcon {
  position: relative;
  width: 40px;
  height: 40px;
  margin: 0 auto 8px;
}

.iconImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 12px;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

.notAchieved .iconImage {
  filter: grayscale(100%) drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

.achievedBadge {
  position: absolute;
  top: -3px;
  right: -3px;
  width: 16px;
  height: 16px;
  background: #4ade80;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: bold;
  color: white;
  box-shadow: 0 2px 8px rgba(74, 222, 128, 0.4);
}

.achievementInfo {
  text-align: center;
}

.achievementName {
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 4px;
  color: white;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: center;
  word-break: break-word;
  min-height: 28px;
}

.achievementDescription {
  display: none; /* Hide description for compact view */
}

.progressContainer {
  margin-bottom: 6px;
  width: 100%;
}

.progressBar {
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 3px;
}

.progressFill {
  height: 100%;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  transition: width 0.3s ease;
}

.achieved .progressFill {
  background: linear-gradient(90deg, #4ade80 0%, #22c55e 100%);
}

.progressText {
  font-size: 10px;
  color: #b0b0b0;
}

.rewardInfo {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
  padding: 6px 10px;
  background: rgba(139, 92, 246, 0.15);
  border: 1px solid rgba(139, 92, 246, 0.3);
  border-radius: 6px;
  font-size: 12px;
}

.rewardLabel {
  color: #a0a0a0;
  font-weight: 500;
}

.rewardItem {
  color: #ffd700;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
}

.rewardItem::before {
  content: '🎁';
  font-size: 14px;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  margin-top: 30px;
}

.pageButton {
  padding: 10px 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.pageButton:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.2);
}

.pageButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pageInfo {
  color: #b0b0b0;
  font-size: 14px;
}

.loading, .error, .noData {
  grid-column: 1 / -1;
  text-align: center;
  padding: 40px;
  color: #b0b0b0;
  font-size: 16px;
}

.error {
  color: #ef4444;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .achievementsGrid {
    grid-template-columns: repeat(5, 1fr);
  }
}

@media (max-width: 1200px) {
  .achievementsGrid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 992px) {
  .achievementsGrid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .container {
    padding: 15px;
  }
  
  .header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filterTabs {
    justify-content: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    gap: 0;
  }
  
  .tab {
    border-radius: 0;
    background: transparent;
    border-bottom: 2px solid transparent;
    padding: 12px 24px;
    position: relative;
    flex: 1;
    text-align: center;
  }
  
  .tab:hover {
    background: rgba(255, 255, 255, 0.05);
  }
  
  .activeTab {
    background: transparent;
    box-shadow: none;
    border-bottom: 2px solid #667eea;
    color: #667eea;
  }
  
  .sortContainer {
    justify-content: center;
  }
  
  .achievementsGrid {
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
  }
  
  .achievementCard {
    padding: 10px;
  }
}

@media (max-width: 576px) {
  .achievementsGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }
  
  .achievementCard {
    padding: 12px;
  }
}

@media (max-width: 480px) {
  .tab {
    padding: 10px 16px;
    font-size: 14px;
  }
  
  .achievementsGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }
  
  .achievementName {
    font-size: 10px;
  }
  
  .achievementIcon {
    width: 35px;
    height: 35px;
  }
}