.container {
  min-height: 100vh;
  padding: 32px 20px 40px;
  background: linear-gradient(135deg, #1a1a2e 0%, #0f0f1e 100%);
}

.content {
  max-width: 800px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  padding: 40px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 2rem;
  text-align: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section {
  margin-bottom: 2.5rem;
}

.subtitle {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #fff;
}

.intro {
  font-size: 1.125rem;
  line-height: 1.8;
  color: #e0e0e0;
  text-align: center;
  margin-bottom: 2rem;
}

.text {
  font-size: 1rem;
  line-height: 1.7;
  color: #d0d0d0;
  margin-bottom: 1rem;
}

.list {
  list-style: none;
  padding: 0;
  margin: 1rem 0;
}

.list li {
  padding-left: 1.5rem;
  margin-bottom: 0.75rem;
  position: relative;
  color: #d0d0d0;
  line-height: 1.7;
}

.list li::before {
  content: "•";
  position: absolute;
  left: 0;
  color: #667eea;
  font-weight: bold;
}

.list li strong {
  color: #fff;
  font-weight: 600;
}

.orderedList {
  counter-reset: item;
  list-style: none;
  padding: 0;
  margin: 1rem 0;
}

.orderedList li {
  counter-increment: item;
  padding-left: 2rem;
  margin-bottom: 1rem;
  position: relative;
  color: #d0d0d0;
  line-height: 1.7;
}

.orderedList li::before {
  content: counter(item) ".";
  position: absolute;
  left: 0;
  color: #667eea;
  font-weight: bold;
}

.orderedList li strong {
  color: #fff;
  font-weight: 600;
}

.seedExplanation {
  display: grid;
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.seedItem {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.seedItem:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(102, 126, 234, 0.3);
  transform: translateY(-2px);
}

.seedItem h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #667eea;
}

.seedItem p {
  font-size: 0.95rem;
  line-height: 1.6;
  color: #d0d0d0;
  margin: 0;
}

.text code {
  background: rgba(102, 126, 234, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  color: #a78bfa;
}

.link {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.link:hover {
  color: #764ba2;
  text-decoration: underline;
}

@media (max-width: 768px) {
  .container {
    padding: 60px 15px 30px;
  }
  
  .content {
    padding: 30px 20px;
  }
  
  .title {
    font-size: 2rem;
  }
  
  .subtitle {
    font-size: 1.25rem;
  }
}