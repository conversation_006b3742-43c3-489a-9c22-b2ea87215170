/* 合成页面样式 - 使用Tailwind CSS内联样式替代 */
/* 保留动画相关样式 */

/* 合成过程动画 */
.synthesisProcess {
  position: relative;
  width: 100px;
  height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.synthesisSpinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  border-top-color: #8B5CF6;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.synthesisFlash {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: white;
  opacity: 0;
  border-radius: 50%;
  animation: flash 2s ease-out;
}

@keyframes flash {
  0% { opacity: 0; transform: scale(0.5); }
  10% { opacity: 0.8; transform: scale(1.2); }
  20% { opacity: 0; transform: scale(1.5); }
  100% { opacity: 0; transform: scale(2); }
}

/* 移动端响应式设计 */
@media (max-width: 768px) {
  /* 普通合成 - 移动端优化 */
  .normalSynthesisMobile {
    flex-direction: row;
    justify-content: center;
    align-items: flex-start;
    gap: 2rem;
    padding: 1rem;
  }
  
  .normalSynthesisMobile .cardSelectionRow {
    display: flex;
    justify-content: space-between;
    gap: 1rem;
    width: 100%;
  }
  
  .normalSynthesisMobile .cardSlot {
    width: 140px;
    height: 196px;
    flex-shrink: 0;
  }
  
  .normalSynthesisMobile .synthesisCenter {
    display: none;
  }
  
  .normalSynthesisMobile .resultPreview {
    width: 200px;
    height: 280px;
  }
  
  .normalSynthesisMobile .synthesisArrows {
    display: none;
  }
  
  .normalSynthesisMobile .synthesisButton {
    width: 100%;
    max-width: 280px;
    font-size: 1rem;
    padding: 0.75rem 1.5rem;
  }
  
  /* 特殊合成 - 移动端优化 */
  .specialSynthesisMobile {
    padding: 1rem;
  }
  
  .specialSynthesisMobile .filterSection {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .specialSynthesisMobile .collectionTabs {
    overflow-x: auto;
    flex-wrap: nowrap;
    gap: 0.5rem;
  }
  
  .specialSynthesisMobile .collectionTab {
    flex-shrink: 0;
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
  }
  
  .specialSynthesisMobile .searchSection {
    width: 100%;
  }
  
  .specialSynthesisMobile .searchInput {
    width: 100%;
    font-size: 1rem;
  }
  
  .specialSynthesisMobile .recipeGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    justify-content: center;
    padding: 0 1rem;
    margin: 0 auto;
    max-width: 400px;
  }
  
  .specialSynthesisMobile .recipeCard {
    padding: 1rem;
  }
  
  .specialSynthesisMobile .recipeTitle {
    font-size: 0.875rem;
    line-height: 1.2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  /* 移动端所有文本单行超出隐藏 */
  .specialSynthesisMobile .recipeCard * {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  /* 移动端卡包标题按钮布局 */
  .specialSynthesisMobile button {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    max-width: 100%;
    overflow: hidden;
    text-align: left;
  }
  
  /* 卡包名称部分（除了span和svg的内容）限制宽度 */
  .specialSynthesisMobile button {
    justify-content: flex-start;
  }
  
  /* 为卡包名称文本设置样式类 */
  .specialSynthesisMobile .pack-name,
  .specialSynthesisMobile .packName {
    flex: 1;
    white-space: normal;
    word-wrap: break-word;
    min-width: 0;
  }
  
  /* 配方数量文本保持原样显示但更小 */
  .specialSynthesisMobile button span {
    flex-shrink: 0;
    white-space: nowrap;
    font-size: 0.65rem;
  }
  
  /* 移动端卡牌容器添加padding */
  .specialSynthesisMobile .recipeCard {
    padding: 1rem;
    margin: 0.5rem;
  }
  
  .specialSynthesisMobile .recipeImage {
    width: 60px;
    height: 60px;
  }
  
  /* 配方详情页面 - 移动端优化 */
  .recipeDetailMobile {
    padding: 1rem;
  }
  
  .recipeDetailMobile .resultCard {
    width: 200px;
    height: 280px;
    margin: 0 auto;
  }
  
  .recipeDetailMobile .ingredientsGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin: 1.5rem 0;
  }
  
  .recipeDetailMobile .ingredientCard {
    padding: 0.75rem;
  }
  
  .recipeDetailMobile .ingredientImage {
    width: 50px;
    height: 50px;
  }
  
  .recipeDetailMobile .buttonGroup {
    flex-direction: column;
    gap: 1rem;
    width: 100%;
  }
  
  .recipeDetailMobile .actionButton {
    width: 100%;
    font-size: 1rem;
    padding: 0.75rem 1.5rem;
  }
  
  /* 合成结果页面 - 移动端优化 */
  .synthesisResultMobile {
    padding: 1rem;
  }
  
  .synthesisResultMobile .resultCard {
    width: 240px;
    height: 336px;
    margin: 0 auto;
  }
  
  .synthesisResultMobile .resultActions {
    flex-direction: column;
    gap: 1rem;
    width: 100%;
    max-width: 320px;
    margin: 0 auto;
  }
  
  .synthesisResultMobile .resultButton {
    width: 100%;
    font-size: 0.875rem;
    padding: 0.75rem 1rem;
  }
}

/* 小屏幕设备进一步优化 */
@media (max-width: 480px) {
  .normalSynthesisMobile .cardSlot {
    width: 120px;
    height: 168px;
  }
  
  .normalSynthesisMobile .resultPreview {
    width: 180px;
    height: 252px;
  }
  
  .specialSynthesisMobile .recipeGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
  
  .recipeDetailMobile .ingredientsGrid {
    grid-template-columns: 1fr;
  }
  
  .synthesisResultMobile .resultCard {
    width: 200px;
    height: 280px;
  }
}

/* 桌面端样式 */
.recipeGrid {
  display: grid;
  gap: 1rem;
  justify-content: center;
  align-items: start;
}

.recipeGrid > div {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 200px;
  margin: 0 auto;
}

/* 统一卡牌尺寸 */
.recipeCard {
  width: 180px;
  min-height: 280px;
  display: flex;
  flex-direction: column;
}

/* NOTE: Previously this file forced sizes on any `div` directly under `.relative` inside `.recipeCard`.
   That selector was too broad and caused images to be squeezed on mobile. We remove those overrides
   and rely on explicit Tailwind sizing in the components. */

/* 移动端响应式 */
@media (max-width: 768px) {
  .recipeCard {
    width: 160px;
    min-height: 260px;
  }
}

@media (max-width: 480px) {
  .recipeCard {
    width: 140px;
    min-height: 240px;
  }
}
