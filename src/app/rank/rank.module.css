.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  padding-top: 32px;
  color: #fff;
}

@media (min-width: 640px) {
  .container {
    padding-top: 20px;
  }
}

.loading,
.error,
.noData {
  text-align: center;
  padding: 40px;
  font-size: 1.2rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  margin: 20px 0;
}

.error {
  color: #ff6b6b;
}

.noData,
.noDataRow {
  color: #aaa;
}

.noDataRow {
  text-align: center;
  padding: 20px;
  grid-column: 1 / -1;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40px;
}

.rankingTitle {
  display: flex;
  align-items: center;
  gap: 20px;
}

.rankingTitle h1 {
  font-size: 2.5rem;
  color: #fff;
}

.endTime {
  color: #7873a3;
  font-size: 1.1rem;
}

.statsSection {
  background: rgba(27, 27, 39, 0.5);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 40px;
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-top: 20px;
}

.statCard {
  display: flex;
  flex-direction: column;
  background: rgba(37, 37, 52, 0.5);
  padding: 15px;
  border-radius: 8px;
}

.statCard label {
  color: #7873a3;
  margin-bottom: 5px;
}

.topPlayers {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-bottom: 40px;
}

.playerCard {
  position: relative;
  background: rgba(27, 27, 39, 0.5);
  border-radius: 12px;
  padding: 20px;
  text-align: center;
}

.firstPlace { order: 2; transform: scale(1.1); }
.secondPlace { order: 1; }
.thirdPlace { order: 3; }

.crown {
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
}

.avatar {
  margin: 20px auto;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  border-radius: 50%;
}

.avatar img {
  width: 60px !important;
  height: 60px !important;
  border-radius: 50% !important;
  object-fit: cover;
}

.avatar img {
  width: 60px !important;
  height: 60px !important;
  border-radius: 50% !important;
  object-fit: cover;
}

.playerInfo h3 {
  margin: 10px 0;
  font-size: 1.2rem;
}

.playerInfo p {
  color: #ffd700;
  font-size: 1.4rem;
}

.playerInfo p span {
  font-size: 0.9rem;
  margin-left: 5px;
}

.prize {
  margin-top: 10px;
  color: #7873a3;
}

.rankingTable {
  background: rgba(27, 27, 39, 0.5);
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 20px;
}

.tableHeader {
  display: grid;
  grid-template-columns: 0.5fr 2fr 1fr 1fr;
  padding: 15px;
  background: rgba(37, 37, 52, 0.5);
  color: #7873a3;
}

.tableHeader > div:last-child {
  text-align: center;
}

.tableRow {
  display: grid;
  grid-template-columns: 0.5fr 2fr 1fr 1fr;
  padding: 15px;
  align-items: center;
  border-bottom: 1px solid rgba(120, 115, 163, 0.1);
}

.player {
  display: flex;
  align-items: center;
  gap: 10px;
}

.player img {
  width: 40px !important;
  height: 40px !important;
  border-radius: 50% !important;
  object-fit: cover;
}

.player img {
  width: 40px !important;
  height: 40px !important;
  border-radius: 50% !important;
  object-fit: cover;
}

.points span {
  color: #7873a3;
  margin-left: 5px;
  font-size: 0.9em;
}

/* Infinite scroll styles */
.loadMoreContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
  min-height: 100px;
}

.loadingMore {
  color: #7873a3;
  font-size: 1.1rem;
  animation: pulse 1.5s ease-in-out infinite;
}

.scrollHint {
  color: #7873a3;
  font-size: 1rem;
  opacity: 0.7;
}

.endMessage {
  text-align: center;
  color: #7873a3;
  padding: 30px 20px;
  font-size: 1rem;
  border-top: 1px solid rgba(120, 115, 163, 0.2);
  margin-top: 20px;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

/* 添加新的样式 */
.tabContainer {
  display: flex;
  width: 100%;
  margin-bottom: 40px;
  border-radius: 8px;
  overflow: hidden;
}

.tab {
  flex: 1;
  padding: 15px;
  background: rgba(27, 27, 39, 0.5);
  border: none;
  color: #7873a3;
  cursor: pointer;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  text-align: center;
}

.tab:hover {
  background: rgba(37, 37, 52, 0.7);
}

.activeTab {
  background: #7873a3;
  color: #fff;
}

.statsBar {
  background: rgba(37, 37, 52, 0.5);
  padding: 15px;
  border-radius: 8px;
  color: #7873a3;
}

.rankCards {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin: 30px 0;
}

.rankCard {
  display: grid;
  grid-template-columns: auto 1fr auto auto;
  align-items: center;
  gap: 20px;
  background: rgba(27, 27, 39, 0.5);
  padding: 15px;
  border-radius: 12px;
}

.badges {
  display: flex;
  gap: 5px;
  margin-top: 5px;
}

.grade {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 5px;
}

/* Hide mobile-only pseudo-elements on desktop */
.tableRow .points::before,
.pointsTableRow .grade::before,
.rankTableRow .grade::before {
  display: none;
}

.grade span {
  color: #7873a3;
}

/* Fix alignment for prize column in points ranking */
.pointsTableRow .grade {
  align-items: center;
}

.pointsTableRow .grade span {
  display: flex;
  align-items: center;
  gap: 5px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .container {
    padding: 15px;
  }

  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
    margin-bottom: 30px;
  }

  .rankingTitle h1 {
    font-size: 2rem;
  }

  .endTime {
    font-size: 1rem;
  }

  /* 统计信息优化 */
  .statsGrid {
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
  }

  .statCard {
    flex-direction: column;
    text-align: center;
    padding: 15px 10px;
  }

  .statCard label {
    margin-bottom: 5px;
    font-size: 0.9rem;
  }

  .statCard span {
    font-size: 1.1rem;
    font-weight: bold;
  }

  /* 前三名玩家布局优化 - 一行展示 */
  .topPlayers {
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
    margin-bottom: 25px;
  }

  .firstPlace,
  .secondPlace,
  .thirdPlace {
    order: unset;
    transform: none;
  }

  .playerCard {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 12px 8px;
    gap: 8px;
    border-radius: 12px;
    background: rgba(27, 27, 39, 0.6);
  }

  .firstPlace .crown {
    background: #ffd700;
    color: #000;
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
  }

  .secondPlace .crown {
    background: #c0c0c0;
    color: #000;
    box-shadow: 0 0 8px rgba(192, 192, 192, 0.3);
  }

  .thirdPlace .crown {
    background: #cd7f32;
    color: #fff;
    box-shadow: 0 0 6px rgba(205, 127, 50, 0.3);
  }

  .crown {
    position: static;
    transform: none;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.8rem;
    flex-shrink: 0;
    margin-bottom: 4px;
  }

  .avatar {
    margin: 0;
    flex-shrink: 0;
  }

  .avatar {
    margin-bottom: 6px;
  }

  .avatar img {
    width: 40px !important;
    height: 40px !important;
  }

  .playerInfo {
    flex: 1;
    min-width: 0;
    width: 100%;
  }

  .playerInfo h3 {
    margin: 0 0 3px 0;
    font-size: 0.85rem;
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.2;
  }

  .playerInfo p {
    margin: 0;
    font-size: 0.9rem;
    color: #ffd700;
    font-weight: 500;
    line-height: 1.2;
  }

  .playerInfo p span {
    font-size: 0.7rem;
    color: #7873a3;
    margin-left: 2px;
    display: block;
  }

  /* 排名表格优化 - 一行显示 */
  .tableHeader {
    display: none;
  }

  .tableRow {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 16px;
    gap: 12px;
    border-bottom: 1px solid rgba(120, 115, 163, 0.15);
    background: rgba(27, 27, 39, 0.3);
    margin-bottom: 8px;
    border-radius: 12px;
    position: relative;
  }

  .tableRow .rank {
    background: linear-gradient(135deg, rgba(120, 115, 163, 0.3), rgba(120, 115, 163, 0.5));
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    color: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    min-width: 40px;
    text-align: center;
    flex-shrink: 0;
  }

  .tableRow .player {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
    min-width: 0;
  }

  .tableRow .player img {
    width: 45px !important;
    height: 45px !important;
    border-radius: 50%;
    border: 2px solid rgba(120, 115, 163, 0.3);
  }

  .tableRow .player span {
    font-size: 1.1rem;
    font-weight: 600;
    color: #fff;
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .tableRow .points {
    display: flex;
    flex-direction: column;
    align-items: center;
    background: linear-gradient(135deg, rgba(37, 37, 52, 0.4), rgba(37, 37, 52, 0.6));
    padding: 8px 12px;
    border-radius: 8px;
    border: 1px solid rgba(120, 115, 163, 0.1);
    min-width: 80px;
    flex-shrink: 0;
  }

  .tableRow .points::before {
    content: "Points";
    color: #7873a3;
    font-size: 0.75rem;
    font-weight: 500;
    margin-bottom: 2px;
    display: block;
  }

  .tableRow .points span {
    color: #ffd700;
    font-weight: 600;
    font-size: 0.9rem;
  }

  .tableRow .grade {
    display: flex;
    flex-direction: column;
    align-items: center;
    background: linear-gradient(135deg, rgba(37, 37, 52, 0.4), rgba(37, 37, 52, 0.6));
    padding: 8px 12px;
    border-radius: 8px;
    border: 1px solid rgba(120, 115, 163, 0.1);
    min-width: 60px;
    flex-shrink: 0;
  }

  .pointsTableRow .grade::before {
    content: "Prize";
    color: #7873a3;
    font-size: 0.75rem;
    font-weight: 500;
    margin-bottom: 2px;
    display: block;
  }

  .rankTableRow .grade::before {
    content: "Level";
    color: #7873a3;
    font-size: 0.75rem;
    font-weight: 500;
    margin-bottom: 2px;
    display: block;
  }

  .tableRow .grade span {
    color: #fff;
    font-weight: 600;
    font-size: 0.9rem;
  }

  .grade {
    align-items: flex-start;
  }

  .badges {
    flex-wrap: wrap;
  }

  /* Infinite scroll mobile styles */
  .loadMoreContainer {
    padding: 30px 15px;
    min-height: 80px;
  }

  .loadingMore {
    font-size: 1rem;
  }

  .scrollHint {
    font-size: 0.9rem;
  }

  .endMessage {
    padding: 20px 15px;
    font-size: 0.9rem;
  }

  /* Tab容器优化 - 保持一行布局 */
  .tabContainer {
    margin-bottom: 30px;
  }

  .tab {
    padding: 12px 8px;
    font-size: 1rem;
  }

  .rankCard {
    grid-template-columns: auto 1fr;
    grid-template-rows: auto auto;
    gap: 10px;
  }

  .rankCard .points,
  .rankCard .reward {
    grid-column: 2;
    justify-self: end;
  }
}


/* 小屏幕设备进一步优化 */
@media (max-width: 480px) {
  .container {
    padding: 10px;
  }

  .rankingTitle h1 {
    font-size: 1.8rem;
  }

  /* 小屏幕统计信息优化 */
  .statsGrid {
    gap: 8px;
  }

  .statCard {
    padding: 12px 8px;
  }

  .statCard label {
    font-size: 0.8rem;
  }

  .statCard span {
    font-size: 1rem;
  }

  .tab {
    padding: 10px 6px;
    font-size: 0.9rem;
  }

  .topPlayers {
    gap: 6px;
  }

  .playerCard {
    padding: 10px 6px;
    gap: 6px;
  }

  .crown {
    width: 24px;
    height: 24px;
    font-size: 0.7rem;
    margin-bottom: 3px;
  }

  .avatar {
    margin-bottom: 4px;
  }

  .avatar img {
    width: 35px !important;
    height: 35px !important;
  }

  .playerInfo h3 {
    font-size: 0.75rem;
    margin-bottom: 2px;
  }

  .playerInfo p {
    font-size: 0.8rem;
  }

  .playerInfo p span {
    font-size: 0.65rem;
  }

  .avatar img {
    width: 50px !important;
    height: 50px !important;
  }

  .playerInfo h3 {
    font-size: 1rem;
  }

  .playerInfo p {
    font-size: 1.1rem;
  }

  .tableRow {
    padding: 12px 8px;
    gap: 8px;
  }

  .tableRow .rank {
    padding: 6px 8px;
    font-size: 0.75rem;
    min-width: 35px;
  }

  .tableRow .player {
    gap: 8px;
  }

  .tableRow .player img {
    width: 35px !important;
    height: 35px !important;
  }

  .tableRow .player span {
    font-size: 0.85rem;
  }

  .tableRow .points,
  .tableRow .grade {
    padding: 6px 8px;
    min-width: 60px;
  }

  .tableRow .points {
    min-width: 65px;
  }

  .tableRow .grade {
    min-width: 50px;
  }

  .tableRow .points::before,
  .tableRow .grade::before {
    font-size: 0.65rem;
    margin-bottom: 1px;
  }

  .tableRow .points span,
  .tableRow .grade span {
    font-size: 0.8rem;
  }

  .loadMoreContainer {
    padding: 25px 10px;
  }

  .endMessage {
    padding: 15px 10px;
    font-size: 0.85rem;
  }
}