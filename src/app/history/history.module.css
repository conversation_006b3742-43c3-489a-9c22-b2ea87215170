.historyContainer {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  background-color: #1E1F2E;
  border-radius: 0.5rem;
  padding: 1rem;
  color: white;
}

@media (min-width: 640px) {
  .historyContainer {
    padding: 1.5rem;
  }
}

/* 隐藏滚动条样式 */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.tabButton {
  display: flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  transition: all 0.2s ease;
  min-width: fit-content;
  white-space: nowrap;
}

@media (min-width: 640px) {
  .tabButton {
    padding: 0.5rem 1rem;
  }
}

.tabButtonActive {
  background-color: #8B5CF6;
}

.tabButtonInactive {
  background-color: #2A2B3D;
}

.tabButtonInactive:hover {
  background-color: #3F3F5F;
}

.tableContainer {
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.historyTable {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0 8px;
}

@media (max-width: 640px) {
  .historyTable {
    min-width: 640px;
  }
}

.historyTable th {
  text-align: left;
  padding: 0.5rem 0.75rem;
  color: #9CA3AF;
  font-size: 0.75rem;
  background: transparent;
}

.historyTable td {
  padding: 0.75rem;
  border: none;
}

@media (min-width: 640px) {
  .historyTable th {
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
  }
  
  .historyTable td {
    padding: 1rem;
  }
}

.historyRow {
  transition: all 0.2s ease;
  border-radius: 8px;
}

.historyRow:nth-child(odd) {
  background-color: rgba(42, 43, 61, 0.6);
}

.historyRow:nth-child(even) {
  background-color: rgba(63, 63, 95, 0.4);
}

.historyRow:hover {
  background-color: rgba(139, 92, 246, 0.2) !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.15);
}

.cardImage {
  width: 2.5rem;
  height: 3.5rem;
  background-color: #3F3F5F;
  border-radius: 0.25rem;
  overflow: hidden;
}

.idTimeContainer {
  display: flex;
  flex-direction: column;
}

.idText {
  color: #D1D5DB;
}

.timeText {
  color: #9CA3AF;
  font-size: 0.875rem;
}

.pointsAmount {
  display: flex;
  align-items: center;
}

.starIcon {
  color: #FBBF24;
  margin-right: 0.5rem;
}

.viewButton {
  background-color: #8B5CF6;
  color: white;
  padding: 0.25rem 1rem;
  border-radius: 0.375rem;
  transition: all 0.2s ease;
}

.viewButton:hover {
  background-color: #7C3AED;
  transform: translateY(-1px);
}

.paginationContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1.5rem;
  font-size: 0.875rem;
  flex-wrap: wrap;
  gap: 0.5rem;
}

@media (max-width: 640px) {
  .paginationContainer {
    font-size: 0.75rem;
    gap: 0.25rem;
  }
}

.paginationInfo {
  color: #9CA3AF;
}

@media (max-width: 640px) {
  .paginationInfo {
    font-size: 12px;
  }
}

.paginationControls {
  display: flex;
  gap: 0.5rem;
}

@media (max-width: 640px) {
  .paginationControls {
    gap: 0.25rem;
  }
}

.paginationButton {
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #2A2B3D;
  border-radius: 0.375rem;
  transition: all 0.2s ease;
}

@media (max-width: 640px) {
  .paginationButton {
    width: 1.75rem;
    height: 1.75rem;
    font-size: 0.75rem;
  }
}

.paginationButton:hover {
  background-color: #3F3F5F;
}

.paginationButtonActive {
  background-color: #8B5CF6;
}

.paginationInput {
  width: 2rem;
  height: 2rem;
  background-color: #2A2B3D;
  border-radius: 0.375rem;
  text-align: center;
  color: white;
  border: none;
}

@media (max-width: 640px) {
  .paginationInput {
    width: 1.75rem;
    height: 1.75rem;
    font-size: 0.75rem;
  }
}

.paginationInput:focus {
  outline: 2px solid #8B5CF6;
}

.confirmButton {
  background-color: #8B5CF6;
  color: white;
  padding: 0.25rem 1rem;
  border-radius: 0.375rem;
  transition: all 0.2s ease;
}

.confirmButton:hover {
  background-color: #7C3AED;
}

/* 总充值金额显示区域 */
.totalRechargeContainer {
  margin-bottom: 1rem;
  padding: 1rem;
  background: linear-gradient(135deg, #2A2B3D 0%, #1E1F2E 100%);
  border-radius: 0.5rem;
  border: 1px solid #8B5CF6;
}

@media (max-width: 640px) {
  .totalRechargeContainer {
    padding: 0.75rem;
    margin-bottom: 0.75rem;
  }
}

.totalRechargeAmount {
  color: #FFFFFF;
  font-size: 1.125rem;
  font-weight: 600;
}

@media (max-width: 640px) {
  .totalRechargeAmount {
    font-size: 1rem;
  }
}

.totalRechargeValue {
  color: #FBBF24;
  font-weight: 700;
}

@media (max-width: 640px) {
  .totalRechargeValue {
    font-size: 1.25rem;
  }
}

/* 充值记录样式 */
.rechargeAmount {
  display: flex;
  align-items: center;
  font-weight: 600;
}

.rechargeAmountValue {
  color: #FFFFFF;
  font-weight: 600;
}

.pointsGranted {
  color: #FBBF24;
  font-weight: 600;
}

/* 禁用状态的分页按钮 */
.paginationButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: #1A1B2E;
}

.paginationButton:disabled:hover {
  background-color: #1A1B2E;
  transform: none;
  scale: 1;
}

/* 加载状态 */
.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem 0;
}

.loadingText {
  color: #FFFFFF;
  font-size: 1rem;
}

/* 空状态 */
.emptyState {
  text-align: center;
  padding: 2rem 0;
  color: #9CA3AF;
}

/* Hide card name column on small screens */
@media (max-width: 640px) {
  .cardNameCol {
    display: none;
  }
}

/* 数字输入框样式优化 */
.paginationInput::-webkit-outer-spin-button,
.paginationInput::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.paginationInput[type=number] {
  -moz-appearance: textfield;
}

.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 登录提示样式 */
.loginPrompt {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  color: #9ca3af;
}

.loginPrompt p {
  font-size: 18px;
  margin-bottom: 20px;
  color: #d1d5db;
}

.loginButton {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.loginButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}