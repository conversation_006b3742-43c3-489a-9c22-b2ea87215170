@tailwind base;
@tailwind components;
@tailwind utilities;

/* Animation delay utilities */
@layer utilities {
  .animation-delay-200 {
    animation-delay: 200ms;
  }
  .animation-delay-400 {
    animation-delay: 400ms;
  }
}

:root {
  --background: #ffffff;
  --foreground: #171717;
  --navbar-height-mobile: 56px;
  --navbar-height-desktop: 64px;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

/* Utility classes based on navbar height */
:root {
  /* Fallbacks for calc usage */
  --navbar-offset-mobile: var(--navbar-height-mobile);
  --navbar-offset-desktop: var(--navbar-height-desktop);
}

.pt-navbar {
  padding-top: var(--navbar-offset-mobile);
}

@media (min-width: 768px) {
  .pt-navbar {
    padding-top: var(--navbar-offset-desktop);
  }
}

html {
  overflow-y: scroll;
  overflow-x: hidden;
  /* 隐藏滚动条但保持滚动功能 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

html::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}

body {
  /* 隐藏滚动条但保持滚动功能 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

body::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}

/* 防止小屏幕出现水平滚动条 */
@media (max-width: 640px) {
  body {
    overflow-x: hidden;
  }
  
  * {
    max-width: 100%;
  }
  
  /* Ensure sticky positioning works on mobile */
  .sticky {
    position: -webkit-sticky !important;
    position: sticky !important;
  }
  
  /* 手机端筛选和搜索框优化 */
  select {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
  }
  
  /* 确保输入框在手机端有合适的字体大小，防止缩放 */
  input[type="text"], select {
    font-size: 16px;
  }
  
  /* 手机端按钮触摸优化 */
  button, select {
    min-height: 44px;
  }
  
  /* Mobile dropdown fixes */
  .category-menu-container,
  .user-menu-container {
    position: relative !important;
  }
  
  /* Fix modal scrolling on mobile */
  body.modal-open {
    position: fixed;
    width: 100%;
    overflow: hidden;
  }
}

body {
  color: var(--foreground);
  font-family: 'Source Han Sans SC', Arial, Helvetica, sans-serif;
}

.bodyBackground {
  background: url('/background.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  width: 100%;
  min-height: calc(100vh - var(--navbar-height-desktop) - 88px); /* 减去导航栏高度和页脚高度 */
}

body {
  background: var(--background);
}

.bodyBackground {
  background: url('/background.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

/* 自定义滚动条样式 */
.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thumb-gray-600::-webkit-scrollbar-thumb {
  background-color: #4b5563;
  border-radius: 0.375rem;
}

.scrollbar-track-transparent::-webkit-scrollbar-track {
  background-color: transparent;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: #4b5563;
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: #6b7280;
}

/* Line clamp utility */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Hide scrollbar but keep functionality */
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}