.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  padding: 20px;
  padding-top: 32px;
  color: white;
}

@media (min-width: 640px) {
  .container {
    padding-top: 20px;
  }
}

.backButtonContainer {
  margin-bottom: 20px;
}

.backButton {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 12px;
  color: white;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.backButton:hover {
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(139, 92, 246, 0.4);
  transform: translateX(-2px);
}

.backButton:active {
  transform: translateX(0);
}

.backArrow {
  font-size: 1.2rem;
  transition: transform 0.3s ease;
}

.backButton:hover .backArrow {
  transform: translateX(-3px);
}

.header {
  text-align: center;
  margin-bottom: 40px;
}

.title {
  font-size: 2.5rem;
  font-weight: bold;
  background: linear-gradient(89deg, #BDA9FF 0%, #F4F1FF 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 10px;
}

.subtitle {
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.1rem;
  margin-bottom: 30px;
}

.section {
  margin-bottom: 50px;
}

.sectionHeader {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 25px;
}

.sectionIcon {
  font-size: 2rem;
}

.sectionTitle {
  font-size: 1.8rem;
  font-weight: bold;
  color: #ffffff;
}

.sectionDescription {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1rem;
  margin-left: auto;
  font-style: italic;
}

.eventsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 25px;
  margin-bottom: 30px;
}

.eventCard {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 15px;
  padding: 25px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.eventCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(139, 92, 246, 0.2);
  border-color: rgba(139, 92, 246, 0.4);
  background: rgba(255, 255, 255, 0.12);
}

.eventCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #8B5CF6, #A855F7, #C084FC);
}

.eventTitle {
  font-size: 1.3rem;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.eventEmoji {
  font-size: 1.5rem;
}

.eventDescription {
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  margin-bottom: 15px;
}

.eventDuration {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #A855F7;
  font-weight: 600;
  font-size: 0.9rem;
  margin-bottom: 15px;
}

.eventRules {
  background: rgba(139, 92, 246, 0.1);
  border: 1px solid rgba(139, 92, 246, 0.3);
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 15px;
}

.rulesTitle {
  color: #C084FC;
  font-weight: bold;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.rulesList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.ruleItem {
  color: rgba(255, 255, 255, 0.9);
  padding: 5px 0;
  display: flex;
  align-items: flex-start;
  gap: 10px;
  line-height: 1.5;
}

.ruleItem::before {
  content: '•';
  color: #8B5CF6;
  font-weight: bold;
  font-size: 1.2rem;
  flex-shrink: 0;
  margin-top: -2px;
}

.eventReward {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.15), rgba(16, 185, 129, 0.15));
  border: 1px solid rgba(34, 197, 94, 0.3);
  border-radius: 10px;
  padding: 12px 15px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.rewardIcon {
  font-size: 1.2rem;
}

.rewardText {
  color: #10B981;
  font-weight: bold;
}

.ongoingBadge {
  position: absolute;
  top: 15px;
  right: 15px;
  background: linear-gradient(135deg, #10B981, #059669);
  color: white;
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: bold;
  box-shadow: 0 2px 10px rgba(16, 185, 129, 0.3);
}

.limitedBadge {
  position: absolute;
  top: 15px;
  right: 15px;
  background: linear-gradient(135deg, #EF4444, #DC2626);
  color: white;
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: bold;
  box-shadow: 0 2px 10px rgba(239, 68, 68, 0.3);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

.pioneerCard {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 193, 7, 0.1));
  border: 1px solid rgba(255, 215, 0, 0.4);
}

.pioneerCard::before {
  background: linear-gradient(90deg, #FFD700, #FFC107, #FF8F00);
}

.affiliateCard {
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.1), rgba(255, 77, 77, 0.1));
  border: 1px solid rgba(255, 107, 107, 0.4);
}

.affiliateCard::before {
  background: linear-gradient(90deg, #FF6B6B, #FF4D4D, #FF3333);
}

.challengeCard {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
  border: 1px solid rgba(99, 102, 241, 0.4);
}

.challengeCard::before {
  background: linear-gradient(90deg, #6366F1, #8B5CF6, #A855F7);
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 15px;
  }
  
  .title {
    font-size: 2rem;
  }
  
  .eventsGrid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .eventCard {
    padding: 20px;
  }
  
  .sectionHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .sectionDescription {
    margin-left: 0;
  }
}

@media (max-width: 480px) {
  .eventTitle {
    font-size: 1.1rem;
  }
  
  .eventCard {
    padding: 15px;
  }
  
  .title {
    font-size: 1.8rem;
  }
}
