/**
 * Server-side safe video cache stubs
 * 
 * This file provides stub implementations for video cache functions
 * that are safe to use during server-side rendering and static generation.
 */

// Export stub functions that are safe for server-side rendering
export const preloadDrawAnimation = async (onProgress?: (progress: number) => void): Promise<void> => {
  // No-op on server side
}

export const getDrawAnimationUrl = async (onProgress?: (progress: number) => void): Promise<string> => {
  // Return direct URL on server side
  return 'https://draw.zapull.fun/draw_animate1.mp4'
}

export const isDrawAnimationCached = async (): Promise<boolean> => {
  // Not cached on server side
  return false
}

// Stub video cache object
export const videoCache = null

export default videoCache
