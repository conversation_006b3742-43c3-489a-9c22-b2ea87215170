import axios from 'axios';
import { auth } from '@/lib/firebase';

const ACHIEVEMENT_CHECK_URL = process.env.NEXT_PUBLIC_ACHIEVEMENT_CHECK_URL || 'https://check-achievements-351785787544.us-central1.run.app';

export interface AchievementCheckResponse {
  message: string;
  awarded: Array<{
    id: string;
    name: string;
    description: string;
    reward: Array<{
      type: string;
      amount?: number;
      emblemId?: string;
      url?: string;
    }>;
  }>;
  addedPoints?: number;
}

/**
 * Check achievements for a user based on the specified achievement types
 * @param types Object with achievement types to check (e.g., { draw_by_rarity: true })
 * @returns Promise with the awarded achievements and added points
 */
export const checkAchievements = async (types: Record<string, boolean>): Promise<AchievementCheckResponse> => {
  try {
    const user = auth.currentUser;
    if (!user) {
      throw new Error('User not authenticated');
    }

    const idToken = await user.getIdToken();
    
    const response = await axios.post<AchievementCheckResponse>(
      ACHIEVEMENT_CHECK_URL,
      {
        types
      },
      {
        headers: {
          'Authorization': `Bearer ${idToken}`,
          'Content-Type': 'application/json'
        }
      }
    );

    return response.data;
  } catch (error: any) {
    // If user is not authenticated (e.g., auth state not ready), return empty result silently
    if (error?.message && /User not authenticated/i.test(error.message)) {
      return { message: 'auth-not-ready', awarded: [], addedPoints: 0 }
    }
    console.error('Error checking achievements:', error);
    throw error;
  }
};

/**
 * Check achievements after drawing cards
 */
export const checkDrawAchievements = async (): Promise<AchievementCheckResponse> => {
  return checkAchievements({
    draw_by_rarity: true,
    level_reached: true
  });
};

/**
 * Check achievements after fusion/synthesis
 */
export const checkFusionAchievements = async (): Promise<AchievementCheckResponse> => {
  return checkAchievements({
    fusion_reached: true
  });
};

/**
 * Check achievements after buying a card (marketplace)
 */
export const checkBuyDealAchievements = async (): Promise<AchievementCheckResponse> => {
  return checkAchievements({
    buy_deal_reached: true
  });
};

/**
 * Check achievements after selling a card (marketplace)
 */
export const checkSellDealAchievements = async (): Promise<AchievementCheckResponse> => {
  return checkAchievements({
    sell_deal_reached: true
  });
};

/**
 * Check level-related achievements
 */
export const checkLevelAchievements = async (): Promise<AchievementCheckResponse> => {
  return checkAchievements({
    level_reached: true
  });
};