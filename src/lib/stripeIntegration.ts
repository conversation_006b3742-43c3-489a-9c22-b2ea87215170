import { loadStripe, Stripe, StripeElements } from '@stripe/stripe-js';
import { paymentApi } from './paymentApi';
import { getCurrentUserId } from './authUtils';

// Stripe公钥（由 Next.js 在构建时内联）
const STRIPE_PUBLISHABLE_KEY = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY;

// Stripe实例
let stripePromise: Promise<Stripe | null>;

// 获取Stripe实例
export const getStripe = () => {
  if (!stripePromise) {
    if (!STRIPE_PUBLISHABLE_KEY) {
      console.error('NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY is not set');
      stripePromise = Promise.resolve(null);
    } else {
      stripePromise = loadStripe(STRIPE_PUBLISHABLE_KEY);
    }
  }
  return stripePromise;
};

// 支付处理选项
export interface PaymentOptions {
  amount: number;           // 美元金额
  referCode?: string;       // 推荐码
  returnUrl?: string;       // 支付完成后的返回URL
  payment_method_id?: string; // 可选：使用已保存的支付方式ID
  onSuccess?: () => void;   // 成功回调
  onError?: (error: string) => void; // 错误回调
}

// 市场交易支付选项
export interface MarketplacePaymentOptions {
  listingId: string;
  buyerAddressId: string;
  offerId?: string;
  returnUrl?: string;
  onSuccess?: () => void;
  onError?: (error: string) => void;
}

// 支付方式管理选项
export interface PaymentMethodOptions {
  setAsDefault?: boolean;
  onSuccess?: () => void;
  onError?: (error: string) => void;
}

/**
 * 创建并处理用户充值支付
 */
export const processRechargePayment = async (options: PaymentOptions): Promise<{ id: string; client_secret: string; amount: number; currency: string; status: string }> => {
  try {
    const userId = getCurrentUserId();
    if (!userId) {
      throw new Error('用户未登录');
    }

    // 1. 创建支付意图
    const paymentIntent = await paymentApi.createPaymentIntent({
      amount: Math.round(options.amount * 100), // 转换为美分
      currency: 'usd',
      refer_code: options.referCode,
      payment_method_id: options.payment_method_id
    });

    // 2. Initialize Stripe
    const stripe = await getStripe();
    if (!stripe) {
      throw new Error('Stripe初始化失败');
    }

    // 3. 返回支付意图的客户端密钥，供前端使用
    // 前端应该使用这个client_secret配合Stripe Elements完成支付
    if (options.onSuccess) {
      options.onSuccess();
    }
    
    // 返回支付意图信息供前端使用
    return paymentIntent;

  } catch (error) {
    console.error('充值支付处理失败:', error);
    if (options.onError) {
      options.onError(error instanceof Error ? error.message : '支付处理失败');
    }
    throw error;
  }
};

/**
 * Create and process marketplace payment
 */
export const processMarketplacePayment = async (options: MarketplacePaymentOptions): Promise<{ id: string; client_secret: string; amount: number; currency: string; status: string }> => {
  try {
    const userId = getCurrentUserId();
    if (!userId) {
      throw new Error('用户未登录');
    }

    // 1. Create marketplace payment intent
    const paymentIntent = await paymentApi.createMarketplacePaymentIntent({
      listing_id: options.listingId,
      buyer_address_id: options.buyerAddressId,
      offer_id: options.offerId
    });

    // 2. Initialize Stripe
    const stripe = await getStripe();
    if (!stripe) {
      throw new Error('Stripe初始化失败');
    }

    // 3. 返回支付意图的客户端密钥，供前端使用
    // 前端应该使用这个client_secret配合Stripe Elements完成支付
    if (options.onSuccess) {
      options.onSuccess();
    }
    
    // 返回支付意图信息供前端使用
    return paymentIntent;

  } catch (error) {
    console.error('Failed to process marketplace payment:', error);
    if (options.onError) {
      options.onError(error instanceof Error ? error.message : '支付处理失败');
    }
    throw error;
  }
};

/**
 * Process payment using Stripe Elements (for custom payment forms)
 */
export const processPaymentWithElements = async (
  elements: StripeElements,
  clientSecret: string,
  returnUrl: string
): Promise<{ error?: Error }> => {
  const stripe = await getStripe();
  if (!stripe) {
    throw new Error('Stripe初始化失败');
  }

  return await stripe.confirmPayment({
    elements,
    confirmParams: {
      return_url: returnUrl,
    },
  });
};

/**
 * Add payment method
 */
export const addPaymentMethod = async (
  paymentMethodId: string,
  options: PaymentMethodOptions = {}
): Promise<void> => {
  try {
    const userId = getCurrentUserId();
    if (!userId) {
      throw new Error('用户未登录');
    }

    await paymentApi.addPaymentMethod({
      payment_method_id: paymentMethodId,
      set_as_default: options.setAsDefault || false
    });

    if (options.onSuccess) {
      options.onSuccess();
    }

  } catch (error) {
    console.error('Failed to add payment method:', error);
    if (options.onError) {
      options.onError(error instanceof Error ? error.message : 'Failed to add payment method');
    }
    throw error;
  }
};

/**
 * Get user payment methods list
 */
export const getUserPaymentMethods = async () => {
  const userId = getCurrentUserId();
  if (!userId) {
    throw new Error('用户未登录');
  }

  return await paymentApi.getPaymentMethods();
};

/**
 * Delete payment method
 */
export const removePaymentMethod = async (paymentMethodId: string): Promise<void> => {
  const userId = getCurrentUserId();
  if (!userId) {
    throw new Error('用户未登录');
  }

  return await paymentApi.deletePaymentMethod(paymentMethodId);
};

/**
 * Get user recharge history
 */
export const getUserRechargeHistory = async () => {
  const userId = getCurrentUserId();
  if (!userId) {
    throw new Error('用户未登录');
  }

  return await paymentApi.getRechargeHistory();
};

/**
 * Check payment status
 */
export const checkPaymentStatus = async (paymentIntentId: string) => {
  return await paymentApi.checkPaymentStatus(paymentIntentId);
};

/**
 * Handle payment completion callback
 */
export const handlePaymentReturn = async (): Promise<{
  status: 'succeeded' | 'processing' | 'requires_payment_method' | 'unknown';
  message: string;
}> => {
  const urlParams = new URLSearchParams(window.location.search);
  const clientSecret = urlParams.get('payment_intent_client_secret');

  if (!clientSecret) {
    return {
      status: 'unknown',
      message: 'Payment information not found'
    };
  }

  try {
    const stripe = await getStripe();
    if (!stripe) {
      throw new Error('Stripe初始化失败');
    }

    const { paymentIntent } = await stripe.retrievePaymentIntent(clientSecret);

    switch (paymentIntent.status) {
      case 'succeeded':
        return {
          status: 'succeeded',
          message: 'Payment successful! Points have been added to your account.'
        };
      case 'processing':
        return {
          status: 'processing',
          message: 'Payment is processing, please wait...'
        };
      case 'requires_payment_method':
        return {
          status: 'requires_payment_method',
          message: 'Payment failed, please try again.'
        };
      default:
        return {
          status: 'unknown',
          message: 'Unknown payment status, please contact customer service.'
        };
    }
  } catch (error) {
    console.error('Failed to check payment status:', error);
    return {
      status: 'unknown',
      message: 'Failed to check payment status, please contact customer service.'
    };
  }
};

/**
 * Amount formatting utility
 */
export const formatAmount = (cents: number): string => {
  return `$${(cents / 100).toFixed(2)}`;
};

/**
 * Convert USD to cents
 */
export const dollarsToCents = (dollars: number): number => {
  return Math.round(dollars * 100);
};

/**
 * Convert cents to USD
 */
export const centsToDollars = (cents: number): number => {
  return cents / 100;
};

const stripeIntegration = {
  getStripe,
  processRechargePayment,
  processMarketplacePayment,
  processPaymentWithElements,
  addPaymentMethod,
  getUserPaymentMethods,
  removePaymentMethod,
  getUserRechargeHistory,
  checkPaymentStatus,
  handlePaymentReturn,
  formatAmount,
  dollarsToCents,
  centsToDollars
};

export { stripeIntegration as default };