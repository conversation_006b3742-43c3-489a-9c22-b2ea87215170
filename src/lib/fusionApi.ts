// Fusion API integration functions
import axios from 'axios';
import { getAuthHeaders } from './authUtils';

const BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://user-backend-351785787544.us-central1.run.app/users/api/v1';

// Types for fusion API responses
export interface FusionIngredient {
  card_id: string;
  card_collection_id: string;
  quantity: number;
  card_reference?: string;
  card_name?: string;
  image_url?: string;
  point_worth?: number;
  probability?: number;
  // User-specific fields
  user_quantity?: number;
  has_enough?: boolean;
}

export interface FusionRecipe {
  result_card_id: string;
  card_collection_id: string;
  card_reference: string;
  pack_id: string;
  pack_collection_id: string;
  ingredients: {
    card_collection_id: string;
    card_id: string;
    card_reference: string;
    quantity: number;
    image_url?: string;
    owned_quantity?: number;
  }[];
  cards_needed: number;
  total_cards_needed: number;
  // Additional fields for display
  result_card_name?: string;
  result_card_image?: string;
  point_worth?: number;
  // New nested card details structure
  result_card_details?: {
    card_name: string;
    image_url: string;
    point_worth: number;
    rarity: number;
  };
}

export interface FusionRecipeWithUserInfo {
  result_card_id: string;
  card_collection_id: string;
  card_reference?: string;
  pack_id: string;
  pack_collection_id: string;
  ingredients: FusionIngredient[];
  result_card_name?: string;
  result_card_image_url?: string;
  result_card_point_worth?: number;
  created_at?: string;
  // User-specific fields
  cards_needed?: number;
  total_cards_needed?: number;
  can_perform_fusion?: boolean;
}

export interface FusionRecipesResponse {
  collections: {
    collection_id: string;
    packs: {
      pack_id: string;
      pack_collection_id: string;
      cards: FusionRecipe[];
      cards_count: number;
    }[];
    packs_count: number;
  }[];
  pagination: {
    total_items: number;
    total_pages: number;
    current_page: number;
    per_page: number;
  };
  filters: {
    sort_by: string;
    sort_order: string;
    search_query: string | null;
    pack: string | null;
  };
}

export interface FusionResult {
  success: boolean;
  message: string;
  result_card?: {
    card_reference: string;
    card_name: string;
    date_got: string;
    id: string;
    image_url: string;
    point_worth: number;
    quantity: number;
    rarity: number;
    locked_quantity: number;
    expireAt: string;
    buybackexpiresAt: string;
    request_date: string;
    subcollection_name: string;
  };
}

export interface MissingCard {
  card_collection_id: string;
  card_id: string;
  required_quantity: number;
  user_quantity: number;
  card_name: string;
  image_url: string;
}

export interface MissingCardsResponse {
  recipes: {
    recipe_id: string;
    recipe_name: string;
    result_card_name: string;
    result_card_image: string;
    missing_cards: MissingCard[];
    has_all_cards: boolean;
  }[];
}

/**
 * Get a single fusion recipe with user card information
 */
export async function getFusionRecipeWithUserInfo(params: {
  pack_collection_id: string;
  pack_id: string;
  result_card_id: string;
}): Promise<FusionRecipeWithUserInfo> {
  const { pack_collection_id, pack_id, result_card_id } = params;
  const authHeaders = await getAuthHeaders();
  
  const response = await axios.get(
    `${BASE_URL}/fusion/fusion_recipes/${pack_collection_id}/${pack_id}/cards/${result_card_id}`,
    {
      headers: authHeaders,
    }
  );
  
  return response.data;
}

/**
 * Get all fusion recipes with optional filtering
 */
export async function getFusionRecipes(params: {
  collection_id?: string;
  pack?: string;
  page?: number;
  per_page?: number;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
  search_query?: string;
}): Promise<FusionRecipesResponse> {
  const authHeaders = await getAuthHeaders();
  
  const response = await axios.get(`${BASE_URL}/fusion/all-fusion-recipes`, {
    params,
    headers: authHeaders,
  });
  
  return response.data;
}

/**
 * Get all packs in a collection that have fusion recipes
 */
export async function getFusionPacks(params: {
  collection_id: string;
}) {
  const { collection_id } = params;
  const authHeaders = await getAuthHeaders();
  
  // Backend now returns all active packs with fusion for a collection in one shot
  const response = await axios.get(`${BASE_URL}/fusion/all-fusion-recipes/collections/${collection_id}/packs`, {
    headers: authHeaders,
  });
  
  return response.data;
}

/**
 * Perform a fusion operation using a specific recipe
 */
export async function performFusion(params: {
  result_card_id: string;
  collection_id?: string;
  pack_id?: string;
}): Promise<FusionResult> {
  const { result_card_id, ...queryParams } = params;
  const authHeaders = await getAuthHeaders();
  
  const response = await axios.post(`${BASE_URL}/fusion/fusion_recipes/${result_card_id}`, null, {
    params: queryParams,
    headers: {
      'Content-Type': 'application/json',
      ...authHeaders,
    },
  });
  
  // Check achievements after fusion (only if auth state ready)
  import('@/lib/achievementChecker')
    .then(async ({ checkFusionAchievementsGlobal }) => {
      const { auth } = await import('@/lib/firebase')
      if (auth.currentUser) {
        checkFusionAchievementsGlobal().catch(() => undefined)
      }
    })
    .catch(() => undefined)
  
  return response.data;
}

/**
 * Perform a random fusion operation
 */
export async function performRandomFusion(params: {
  card_id1: string;
  card_id2: string;
  collection_id: string;
}): Promise<FusionResult> {
  const authHeaders = await getAuthHeaders();
  
  const response = await axios.post(`${BASE_URL}/fusion/random-fusion`, {
    card1: {
      card_id: params.card_id1,
      collection_id: params.collection_id
    },
    card2: {
      card_id: params.card_id2,
      collection_id: params.collection_id
    }
  }, {
    headers: {
      'Content-Type': 'application/json',
      ...authHeaders,
    },
  });
  
  // Check achievements after fusion (only if auth state ready)
  import('@/lib/achievementChecker')
    .then(async ({ checkFusionAchievementsGlobal }) => {
      const { auth } = await import('@/lib/firebase')
      if (auth.currentUser) {
        checkFusionAchievementsGlobal().catch(() => undefined)
      }
    })
    .catch(() => undefined)
  
  return response.data;
}

/**
 * Check which cards are missing for fusion recipes
 */
export async function checkMissingCards(params: {
  fusion_recipe_ids: string[];
}): Promise<MissingCardsResponse> {
  const { fusion_recipe_ids } = params;
  const authHeaders = await getAuthHeaders();
  
  const response = await axios.post(`${BASE_URL}/fusion/check-missing-cards`, 
    { fusion_recipe_ids },
    {
      headers: {
        'Content-Type': 'application/json',
        ...authHeaders,
      },
    }
  );
  
  return response.data;
}

/**
 * Get fusion eligible cards (cards with point_worth < 3000)
 */
export async function getFusionEligibleCards(params?: {
  page?: number;
  per_page?: number;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
  search_query?: string;
  subcollection_name?: string;
}) {
  const authHeaders = await getAuthHeaders();
  
  const response = await axios.get(`${BASE_URL}/fusion/fusion-eligible-cards`, {
    params: params || {},
    headers: authHeaders,
  });
  
  return response.data;
}

/**
 * Get reverse fusion recipes - what fusion recipes can create this card as a result
 * This shows "can be created from" relationships
 */
export interface ReverseFusionRecipe {
  recipe_id?: string;
  result_card_id: string;
  result_card_name: string;
  result_card_image_url: string;
  result_card_point_worth?: number;
  pack_id: string;
  pack_collection_id: string;
  pack_name?: string;
  // Ingredients needed to create this card
  ingredients: {
    card_id: string;
    card_name: string;
    image_url: string;
    quantity: number;
    user_quantity?: number;
    has_enough?: boolean;
    point_worth?: number;
  }[];
  can_perform_fusion?: boolean;
  total_cards_needed?: number;
}

export async function getReverseFusionRecipes(params: {
  card_id: string;
  collection_id?: string;
}): Promise<ReverseFusionRecipe[]> {
  const { card_id, collection_id } = params;
  const authHeaders = await getAuthHeaders();
  
  // For now, we'll implement this by searching through all fusion recipes
  // to find ones where this card is the result
  try {
    const allRecipes = await getFusionRecipes({
      collection_id,
      per_page: 1000 // Get a large number to find all relevant recipes
    });
    
    const reverseRecipes: ReverseFusionRecipe[] = [];
    
    // Search through all collections and packs for recipes that create this card
    for (const collection of allRecipes.collections) {
      for (const pack of collection.packs) {
        for (const recipe of pack.cards) {
          if (recipe.result_card_id === card_id) {
            // Found a recipe that creates this card
            try {
              // Get detailed recipe info with user data
              const detailedRecipe = await getFusionRecipeWithUserInfo({
                pack_collection_id: recipe.pack_collection_id,
                pack_id: recipe.pack_id,
                result_card_id: recipe.result_card_id
              });
              
              reverseRecipes.push({
                recipe_id: `${recipe.pack_collection_id}_${recipe.pack_id}_${recipe.result_card_id}`,
                result_card_id: recipe.result_card_id,
                result_card_name: detailedRecipe.result_card_name || recipe.result_card_name || card_id,
                result_card_image_url: detailedRecipe.result_card_image_url || recipe.result_card_image || '',
                result_card_point_worth: detailedRecipe.result_card_point_worth || recipe.point_worth,
                pack_id: recipe.pack_id,
                pack_collection_id: recipe.pack_collection_id,
                ingredients: detailedRecipe.ingredients.map(ing => ({
                  card_id: ing.card_id,
                  card_name: ing.card_name || ing.card_id,
                  image_url: ing.image_url || '',
                  quantity: ing.quantity,
                  user_quantity: ing.user_quantity,
                  has_enough: ing.has_enough,
                  point_worth: ing.point_worth
                })),
                can_perform_fusion: detailedRecipe.can_perform_fusion,
                total_cards_needed: detailedRecipe.total_cards_needed
              });
            } catch (error) {
              console.error(`Failed to get detailed recipe for ${recipe.result_card_id}:`, error);
              // Add basic info even if detailed fetch fails
              reverseRecipes.push({
                result_card_id: recipe.result_card_id,
                result_card_name: recipe.result_card_name || card_id,
                result_card_image_url: recipe.result_card_image || '',
                result_card_point_worth: recipe.point_worth,
                pack_id: recipe.pack_id,
                pack_collection_id: recipe.pack_collection_id,
                ingredients: recipe.ingredients.map(ing => ({
                  card_id: ing.card_id,
                  card_name: ing.card_reference?.split('/').pop() || ing.card_id,
                  image_url: ing.image_url || '',
                  quantity: ing.quantity,
                  user_quantity: ing.owned_quantity,
                  has_enough: false
                })),
                can_perform_fusion: false
              });
            }
          }
        }
      }
    }
    
    return reverseRecipes;
  } catch (error) {
    console.error('Failed to get reverse fusion recipes:', error);
    return [];
  }
}
