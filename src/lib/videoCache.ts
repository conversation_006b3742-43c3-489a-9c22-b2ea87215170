/**
 * Video Cache Manager
 * 
 * Provides multiple strategies for caching draw animation videos to improve performance:
 * 1. IndexedDB storage for long-term caching
 * 2. Memory cache for session-level caching
 * 3. Service Worker cache for offline support (optional)
 * 4. Preloading with progress tracking
 */

// Only define types and classes in browser environment
const isBrowser = typeof window !== 'undefined' && typeof navigator !== 'undefined'

interface CachedVideo {
  url: string
  blob: Blob
  timestamp: number
  size: number
  version: string
}

interface VideoCacheOptions {
  maxAge?: number // Cache expiry in milliseconds (default: 7 days)
  maxSize?: number // Max cache size in bytes (default: 100MB)
  enableServiceWorkerCache?: boolean // Enable SW cache (default: true)
}

// Only define class if we're in browser
let VideoCache: any = null

if (isBrowser) {
  VideoCache = class VideoCache {
  private memoryCache = new Map<string, Blob>()
  private urlCache = new Map<string, string>() // Track created URLs for cleanup
  private loadingPromises = new Map<string, Promise<string>>() // Prevent duplicate downloads
  private dbName = 'zapull-video-cache'
  private dbVersion = 1
  private storeName = 'videos'
  private options: Required<VideoCacheOptions>
  private isSupported = false

  constructor(options: VideoCacheOptions = {}) {
    this.options = {
      maxAge: options.maxAge || 7 * 24 * 60 * 60 * 1000, // 7 days
      maxSize: options.maxSize || 100 * 1024 * 1024, // 100MB
      enableServiceWorkerCache: options.enableServiceWorkerCache ?? true
    }
  }

  /**
   * Initialize the video cache
   */
  async initialize(): Promise<void> {
    try {
      // Check if IndexedDB is supported
      this.isSupported = this.checkSupport()
      if (!this.isSupported) {
        console.warn('Video cache not supported in this environment')
        return
      }

      // Initialize IndexedDB
      await this.initDB()
      
      // Clean expired entries
      await this.cleanup()
      
      // Register service worker if enabled - only in browser environment
      if (this.options.enableServiceWorkerCache && typeof navigator !== 'undefined' && 'serviceWorker' in navigator) {
        await this.registerServiceWorker()
      }

      // Setup cleanup on page unload
      if (typeof window !== 'undefined') {
        window.addEventListener('beforeunload', () => this.cleanupUrls())
      }
    } catch (error) {
      console.warn('Failed to initialize video cache:', error)
      this.isSupported = false
    }
  }

  /**
   * Get video from cache or fetch if not cached
   */
  async getVideo(url: string, onProgress?: (progress: number) => void): Promise<string> {
    try {
      // If caching is not supported, return original URL
      if (!this.isSupported) {
        return url
      }

      // Check if we already have a blob URL created
      const existingBlobUrl = this.urlCache.get(url)
      if (existingBlobUrl) {
        return existingBlobUrl
      }

      // Check memory cache first
      const memoryBlob = this.memoryCache.get(url)
      if (memoryBlob) {
        const blobUrl = URL.createObjectURL(memoryBlob)
        this.urlCache.set(url, blobUrl)
        return blobUrl
      }

      // Check for ongoing download
      const existingPromise = this.loadingPromises.get(url)
      if (existingPromise) {
        return await existingPromise
      }

      // Check IndexedDB cache
      const cachedVideo = await this.getFromDB(url)
      if (cachedVideo && this.isValidCache(cachedVideo)) {
        // Add to memory cache for faster access
        this.memoryCache.set(url, cachedVideo.blob)
        const blobUrl = URL.createObjectURL(cachedVideo.blob)
        this.urlCache.set(url, blobUrl)
        return blobUrl
      }

      // Fetch and cache the video
      const promise = this.fetchAndCache(url, onProgress)
      this.loadingPromises.set(url, promise)
      
      try {
        const result = await promise
        return result
      } finally {
        this.loadingPromises.delete(url)
      }
    } catch (error) {
      console.error('Error getting cached video:', error)
      // Fallback to original URL if caching fails
      return url
    }
  }

  /**
   * Preload video into cache
   */
  async preloadVideo(url: string, onProgress?: (progress: number) => void): Promise<void> {
    try {
      await this.getVideo(url, onProgress)
    } catch (error) {
      console.error('Error preloading video:', error)
    }
  }

  /**
   * Check if video is cached
   */
  async isCached(url: string): Promise<boolean> {
    // Check memory cache
    if (this.memoryCache.has(url)) {
      return true
    }

    // Check IndexedDB cache
    const cachedVideo = await this.getFromDB(url)
    return cachedVideo ? this.isValidCache(cachedVideo) : false
  }

  /**
   * Get cache info (size, count, etc.)
   */
  async getCacheInfo(): Promise<{
    totalSize: number
    videoCount: number
    memoryVideoCount: number
  }> {
    const db = await this.getDB()
    const transaction = db.transaction([this.storeName], 'readonly')
    const store = transaction.objectStore(this.storeName)
    const videos = await this.getAllFromStore(store)
    
    const totalSize = videos.reduce((size, video) => size + video.size, 0)
    
    return {
      totalSize,
      videoCount: videos.length,
      memoryVideoCount: this.memoryCache.size
    }
  }

  /**
   * Clear all cached videos
   */
  async clearCache(): Promise<void> {
    // Cleanup URLs first to prevent memory leaks
    this.cleanupUrls()
    
    // Clear memory cache
    this.memoryCache.clear()
    
    // Clear IndexedDB cache
    try {
      const db = await this.getDB()
      const transaction = db.transaction([this.storeName], 'readwrite')
      const store = transaction.objectStore(this.storeName)
      await store.clear()
    } catch (error) {
      console.warn('Failed to clear IndexedDB cache:', error)
    }
  }

  /**
   * Remove specific video from cache
   */
  async removeVideo(url: string): Promise<void> {
    // Cleanup blob URL
    const blobUrl = this.urlCache.get(url)
    if (blobUrl) {
      try {
        URL.revokeObjectURL(blobUrl)
      } catch (error) {
        console.warn('Failed to revoke blob URL:', error)
      }
      this.urlCache.delete(url)
    }
    
    // Remove from memory cache
    this.memoryCache.delete(url)
    
    // Remove from IndexedDB
    try {
      const db = await this.getDB()
      const transaction = db.transaction([this.storeName], 'readwrite')
      const store = transaction.objectStore(this.storeName)
      await store.delete(url)
    } catch (error) {
      console.warn('Failed to remove video from IndexedDB:', error)
    }
  }

  // Private methods

  private async initDB(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.dbVersion)
      
      request.onerror = () => reject(request.error)
      request.onsuccess = () => resolve()
      
      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result
        
        if (!db.objectStoreNames.contains(this.storeName)) {
          const store = db.createObjectStore(this.storeName, { keyPath: 'url' })
          store.createIndex('timestamp', 'timestamp', { unique: false })
        }
      }
    })
  }

  private async getDB(): Promise<IDBDatabase> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.dbVersion)
      request.onerror = () => reject(request.error)
      request.onsuccess = () => resolve(request.result)
    })
  }

  private async getFromDB(url: string): Promise<CachedVideo | null> {
    const db = await this.getDB()
    const transaction = db.transaction([this.storeName], 'readonly')
    const store = transaction.objectStore(this.storeName)
    
    return new Promise((resolve, reject) => {
      const request = store.get(url)
      request.onerror = () => reject(request.error)
      request.onsuccess = () => resolve(request.result || null)
    })
  }

  private async saveToDB(cachedVideo: CachedVideo): Promise<void> {
    const db = await this.getDB()
    const transaction = db.transaction([this.storeName], 'readwrite')
    const store = transaction.objectStore(this.storeName)
    
    return new Promise((resolve, reject) => {
      const request = store.put(cachedVideo)
      request.onerror = () => reject(request.error)
      request.onsuccess = () => resolve()
    })
  }

  private async getAllFromStore(store: IDBObjectStore): Promise<CachedVideo[]> {
    return new Promise((resolve, reject) => {
      const request = store.getAll()
      request.onerror = () => reject(request.error)
      request.onsuccess = () => resolve(request.result)
    })
  }

  private async fetchAndCache(url: string, onProgress?: (progress: number) => void): Promise<string> {
    try {
      const response = await fetch(url)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch video: ${response.status}`)
      }

      const contentLength = response.headers.get('content-length')
      const totalSize = contentLength ? parseInt(contentLength, 10) : 0
      
      // Check if the video is too large
      if (totalSize > this.options.maxSize) {
        console.warn(`Video too large (${totalSize} bytes), using direct URL`)
        return url
      }

      let loadedSize = 0
      const chunks: Uint8Array[] = []

      // Handle streaming response
      if (response.body) {
        const reader = response.body.getReader()
        
        try {
          while (true) {
            const { done, value } = await reader.read()
            
            if (done) break
            
            if (value) {
              chunks.push(value)
              loadedSize += value.length
              
              if (onProgress && totalSize > 0) {
                onProgress(Math.round((loadedSize / totalSize) * 100))
              }
            }
          }
        } finally {
          reader.releaseLock()
        }
      } else {
        // Fallback for browsers that don't support streaming
        const arrayBuffer = await response.arrayBuffer()
        chunks.push(new Uint8Array(arrayBuffer))
      }

      // Combine chunks into a single blob
      const blob = new Blob(chunks, { type: response.headers.get('content-type') || 'video/mp4' })
      
      // Cache the video
      const cachedVideo: CachedVideo = {
        url,
        blob,
        timestamp: Date.now(),
        size: blob.size,
        version: '1.0'
      }

      // Save to caches
      this.memoryCache.set(url, blob)
      
      // Save to IndexedDB with error handling
      try {
        await this.saveToDB(cachedVideo)
      } catch (error) {
        console.warn('Failed to save video to IndexedDB:', error)
        // Continue anyway, we still have memory cache
      }

      const blobUrl = URL.createObjectURL(blob)
      this.urlCache.set(url, blobUrl)
      return blobUrl
    } catch (error) {
      console.error('Error fetching and caching video:', error)
      throw error
    }
  }

  private isValidCache(cachedVideo: CachedVideo): boolean {
    const now = Date.now()
    return (now - cachedVideo.timestamp) < this.options.maxAge
  }

  private async cleanup(): Promise<void> {
    try {
      const db = await this.getDB()
      const transaction = db.transaction([this.storeName], 'readwrite')
      const store = transaction.objectStore(this.storeName)
      const videos = await this.getAllFromStore(store)

      const now = Date.now()
      let totalSize = 0
      const validVideos: CachedVideo[] = []

      // Filter expired videos and calculate total size
      for (const video of videos) {
        if (this.isValidCache(video)) {
          validVideos.push(video)
          totalSize += video.size
        } else {
          await store.delete(video.url)
        }
      }

      // Remove oldest videos if cache size exceeds limit
      if (totalSize > this.options.maxSize) {
        const sortedVideos = validVideos.sort((a, b) => a.timestamp - b.timestamp)
        let currentSize = totalSize

        for (const video of sortedVideos) {
          if (currentSize <= this.options.maxSize) break
          
          await store.delete(video.url)
          this.memoryCache.delete(video.url)
          currentSize -= video.size
        }
      }
    } catch (error) {
      console.warn('Cache cleanup failed:', error)
    }
  }

  private checkSupport(): boolean {
    try {
      return typeof window !== 'undefined' && 
             typeof navigator !== 'undefined' &&
             'indexedDB' in window && 
             'Blob' in window &&
             'URL' in window &&
             typeof URL.createObjectURL === 'function'
    } catch {
      return false
    }
  }

  private cleanupUrls(): void {
    // Revoke all blob URLs to prevent memory leaks
    for (const [, blobUrl] of this.urlCache) {
      try {
        URL.revokeObjectURL(blobUrl)
      } catch (error) {
        console.warn('Failed to revoke blob URL:', error)
      }
    }
    this.urlCache.clear()
  }

  private async registerServiceWorker(): Promise<void> {
    try {
      // This is a placeholder - you'd need to create a service worker file
      // The service worker would handle caching video requests
      console.log('Service worker registration for video cache not implemented yet')
    } catch (error) {
      console.warn('Service worker registration failed:', error)
    }
  }
  }
} else {
  // Server-side stub
  VideoCache = class {
    async initialize() {}
    async getVideo(url: string) { return url }
    async preloadVideo() {}
    async isCached() { return false }
  }
}

// Create and export a singleton instance - only in browser environment
export const videoCache = VideoCache ? new VideoCache(isBrowser ? {
  maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
  maxSize: 200 * 1024 * 1024, // 200MB - videos can be large
  enableServiceWorkerCache: false // Disabled for now
} : {}) : null

// Initialize the cache when the module is loaded - only in browser environment
if (isBrowser && videoCache && typeof videoCache.initialize === 'function') {
  videoCache.initialize().catch((error: any) => {
    console.warn('Video cache initialization failed:', error)
  })
}

// Export utility functions - with fallbacks for server-side rendering
export const preloadDrawAnimation = async (onProgress?: (progress: number) => void): Promise<void> => {
  if (!isBrowser || !videoCache) {
    return // No-op on server side
  }
  const animationUrl = 'https://draw.zapull.fun/draw_animate1.mp4'
  await videoCache.preloadVideo(animationUrl, onProgress)
}

export const getDrawAnimationUrl = async (onProgress?: (progress: number) => void): Promise<string> => {
  const animationUrl = 'https://draw.zapull.fun/draw_animate1.mp4'
  if (!isBrowser || !videoCache) {
    return animationUrl // Return direct URL on server side
  }
  return await videoCache.getVideo(animationUrl, onProgress)
}

export const isDrawAnimationCached = async (): Promise<boolean> => {
  if (!isBrowser || !videoCache) {
    return false // Not cached on server side
  }
  const animationUrl = 'https://draw.zapull.fun/draw_animate1.mp4'
  return await videoCache.isCached(animationUrl)
}

export { videoCache as default, VideoCache }
