import axios from 'axios';
import { getAuthHeaders } from './authUtils';

// Use user_backend service for pack operations
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://user-backend-351785787544.us-central1.run.app/users/api/v1';

// 获取认证配置
const getAuthConfig = async () => {
  const headers = await getAuthHeaders();
  return { headers };
};

// 类型元数据接口
export interface CollectionMetadata {
  id: string;
  name: string;
  image_url: string | null;
  is_active: boolean | null;
  max_win: number | null;
  min_win: number | null;
  popularity: number;
  price: number | null;
  win_rate: number | null;
  created_at: string | null;
}

// 卡包列表响应接口
export interface PackListResponse {
  packs: Pack[];
  pagination: {
    total_items: number;
    total_pages: number;
    current_page: number;
    per_page: number;
  };
  filters: {
    sort_by: string;
    sort_order: string;
    search_query: string | null;
  };
  next_cursor: string | null;
}

// 卡包接口
export interface Pack {
  id: string;
  name: string;
  image_url: string | null;
  price: number;
  win_rate: number;
  min_win: number;
  max_win: number;
  popularity: number;
  is_active: boolean;
  created_at: string;
  separation?: string;  // Can be "hunt", "feature", "special", or "other" (default)
  has_fusion_recipes?: boolean;  // Whether this pack has fusion recipes
}

// 融合信息接口
export interface FusionInfo {
  result_card_id: string;
  result_card_image_url: string;
  fusion_id: string;
  result_card_name: string;
  pack_reference: string;
}

// 卡片接口
export interface Card {
  id?: string;
  document_id: string;
  name: string;
  card_name?: string;  // Some APIs return card_name instead of name
  image_url: string;
  point_worth: number;
  cash_worth?: number;
  probability: number;
  quantity: number;
  rarity: number;
  condition: string;
  globalRef: string;
  collection_id?: string;
  color?: string;
  used_in_fusion?: FusionInfo[] | null;  // Array of fusion recipes this card is used in
}

export const packsApi = {
  // 获取类型元数据
  getCollectionMetadata: async (): Promise<CollectionMetadata[]> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.get(`${API_BASE_URL}/packs/packs_collection`, config);
      return response.data;
    } catch (error) {
      console.error('获取类型元数据失败:', error);
      throw error;
    }
  },

  // 获取卡包列表
  getPacksByCollection: async (
    collectionId: string,
    params: {
      page: number;
      per_page: number;
      search_query?: string;
      sort_by?: string;
      sort_order?: 'asc' | 'desc';
    }
  ): Promise<PackListResponse> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.get(`${API_BASE_URL}/packs/collection/${collectionId}`, { params, ...config });
      return response.data;
    } catch (error) {
      console.error('获取卡包列表失败:', error);
      throw error;
    }
  },

  // 获取卡包详情
  getPackDetails: async (packId: string, collectionId: string): Promise<Pack> => {
    try {
      const config = await getAuthConfig();
      // User backend expects pack_id in path and optional collection_id as query param
      const response = await axios.get(`${API_BASE_URL}/packs/${packId}`, {
        ...config,
        params: { collection_id: collectionId }
      });
      return response.data;
    } catch (error) {
      console.error('获取卡包详情失败:', error);
      throw error;
    }
  },

  // 获取卡包中的卡片
  getPackCards: async (collectionId: string, packId: string): Promise<Card[]> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.get(`${API_BASE_URL}/packs/${collectionId}/${packId}/cards`, config);
      return response.data;
    } catch (error) {
      console.error('获取卡包卡片失败:', error);
      throw error;
    }
  }
};