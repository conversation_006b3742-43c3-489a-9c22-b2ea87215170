// Patches react-hot-toast's success to register active success toast IDs
// and sets up a guard timestamp to ignore the same click event that created the toast.

import toast from 'react-hot-toast'

export const __successRegistry = {
  activeSuccessIds: new Set<string>(),
  // The click-away handler will ignore events with timeStamp <= this value
  enableClickAwayAt: 0,
}

// Keep original so we can call it
const __originalSuccess = toast.success.bind(toast)

type ToastOptions = Parameters<typeof __originalSuccess>[1]

declare global {
  // Ensure module-level side effect applies once
  // No global declarations needed here besides allowing augmentation if necessary
}

// Monkey-patch toast.success once per app startup
// Adds our identifying class and tracks ids for selective dismissal
// Also sets enableClickAwayAt to a short time in the future to ignore the same click
// that triggered showing the toast.
// eslint-disable-next-line @typescript-eslint/no-explicit-any
;(toast as any).success = (message: Parameters<typeof __originalSuccess>[0], options?: ToastOptions) => {
  const now = typeof performance !== 'undefined' ? performance.now() : Date.now()
  const className = ['app-success-toast', options?.className].filter(Boolean).join(' ')
  const patched: ToastOptions = { ...options, className }

  const id = __originalSuccess(message, patched)

  if (id) {
    try {
      __successRegistry.activeSuccessIds.add(String(id))
    } catch {
      // no-op
    }
  }

  // Guard window: ignore current click and microtasks
  __successRegistry.enableClickAwayAt = now + 50

  // Best-effort cleanup after auto-dismiss to avoid memory leaks
  const duration = typeof patched?.duration === 'number' ? patched.duration : 1000
  if (id) {
    setTimeout(() => {
      __successRegistry.activeSuccessIds.delete(String(id))
    }, duration + 250)
  }

  return id
}

