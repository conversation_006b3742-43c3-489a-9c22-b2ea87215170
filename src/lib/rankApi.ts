import axios from 'axios';
import { getAuthHeaders } from './authUtils';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://user-backend-351785787544.us-central1.run.app/users/api/v1';

// 获取认证配置
const getAuthConfig = async () => {
  const headers = await getAuthHeaders();
  return { headers };
};

// 排行榜用户数据接口
interface RankUser {
  user_id: string;
  total_drawn?: number;  // For level rankings
  spent?: number;        // For weekly spending rankings
  level: number;
  display_name: string;
  avatar: string;
}

// 排行榜API接口
const rankApi = {
  // 获取每周消费排行榜
  getWeeklySpentRanking: async (limit: number = 10): Promise<RankUser[]> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.get(`${API_BASE_URL}/rank/weekly_spent/weekly_spent`, {
        params: { per_page: limit },
        ...config
      });
      return response.data.rankings || [];
    } catch (error) {
      console.error('获取每周消费排行榜失败:', error);
      throw error;
    }
  },

  // 获取等级排行榜
  getTopLevelRanking: async (limit: number = 10): Promise<RankUser[]> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.get(`${API_BASE_URL}/rank/top_level`, {
        params: { per_page: limit },
        ...config
      });
      return response.data.rankings || [];
    } catch (error) {
      console.error('获取等级排行榜失败:', error);
      throw error;
    }
  }
};

export default rankApi;