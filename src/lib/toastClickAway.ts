import toast from 'react-hot-toast'
import { __successRegistry } from './patchHotToastSuccess'

function isInsideAnyToast(target: EventTarget | null) {
  const el = target instanceof Element ? target : null
  if (!el) return false
  // react-hot-toast container and our custom class on success toast
  if (el.closest('.hot-toaster')) return true
  if (el.closest('.app-success-toast')) return true
  return false
}

function onDocumentClick(e: MouseEvent) {
  const allowAt = __successRegistry.enableClickAwayAt
  if (typeof e.timeStamp === 'number' && e.timeStamp <= allowAt) return

  if (isInsideAnyToast(e.target)) return

  for (const id of Array.from(__successRegistry.activeSuccessIds)) {
    toast.dismiss(id)
  }
}

export function attachSuccessToastClickAway() {
  if (typeof window === 'undefined') return
  window.addEventListener('click', onDocumentClick, true)
}

export function detachSuccessToastClickAway() {
  if (typeof window === 'undefined') return
  window.removeEventListener('click', onDocumentClick, true)
}

