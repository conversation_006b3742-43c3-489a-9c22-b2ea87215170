/**
 * 地址数据构建器
 * 用于构建符合API接口格式的地址数据
 */

import { Country, State } from 'country-state-city';

// 符合API接口格式的地址数据
export interface AddressApiData {
  id?: string;
  name: string;
  street: string;
  city: string;
  state: string;
  zip: string;
  country: string;
}

// 内部使用的地址数据，包含额外的字段用于UI交互
export interface AddressFormData {
  id?: string | number;
  name: string;
  street: string;
  city: string;
  cityName?: string;
  state: string;
  stateName?: string;
  zip: string;
  country: string;
  countryCode?: string;
  stateCode?: string;
}

/**
 * 地址数据构建器类
 * 用于将表单数据转换为符合API格式的地址数据
 */
export class AddressBuilder {
  /**
   * 从表单数据构建API格式的地址数据
   * @param formData 表单数据
   * @param id 可选的地址ID
   * @returns 符合API格式的地址数据
   */
  static buildFromForm(formData: AddressFormData, id?: string): AddressApiData {
    return {
      id: id || formData.id?.toString() || Date.now().toString(),
      name: formData.name,
      street: formData.street,
      city: formData.city || formData.cityName || '',
      state: formData.state || this.getStateNameByCode(formData.countryCode, formData.stateCode) || '',
      zip: formData.zip,
      country: formData.countryCode || this.getCountryCodeFromName(formData.country) || ''
    };
  }

  /**
   * 根据国家代码获取国家名称
   * @param countryCode 国家代码
   * @returns 国家名称
   */
  private static getCountryNameByCode(countryCode?: string): string {
    if (!countryCode) return '';
    const country = Country.getCountryByCode(countryCode);
    return country ? country.name : '';
  }

  /**
   * 根据国家名称获取国家代码
   * @param countryName 国家名称
   * @returns 国家代码
   */
  private static getCountryCodeFromName(countryName?: string): string {
    if (!countryName) return '';
    const countries = Country.getAllCountries();
    const country = countries.find(c => c.name === countryName);
    return country ? country.isoCode : '';
  }

  /**
   * 根据国家代码和州/省代码获取州/省名称
   * @param countryCode 国家代码
   * @param stateCode 州/省代码
   * @returns 州/省名称
   */
  private static getStateNameByCode(countryCode?: string, stateCode?: string): string {
    if (!countryCode || !stateCode) return '';
    const state = State.getStateByCodeAndCountry(stateCode, countryCode);
    return state ? state.name : '';
  }
}