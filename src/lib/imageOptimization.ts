/**
 * Cloudflare Image Resizing helper functions
 * 
 * This utility helps optimize images using Cloudflare's Image Resizing service
 * which works seamlessly with your existing R2 storage.
 */

export interface CloudflareImageOptions {
  width?: number
  height?: number
  quality?: number // 1-100
  format?: 'auto' | 'webp' | 'avif' | 'jpeg' | 'png'
  fit?: 'scale-down' | 'contain' | 'cover' | 'crop' | 'pad'
  gravity?: 'auto' | 'side' | 'top' | 'bottom' | 'left' | 'right' | 'center'
  blur?: number // 0-250
  sharpen?: number // 0-10
  brightness?: number // 0-2 (1 is normal)
  contrast?: number // 0-2 (1 is normal)
  gamma?: number // 0.1-9.9 (1 is normal)
  trim?: number // 0-100 (percentage of pixels to trim from edges)
}

/**
 * Generates a Cloudflare Image Resizing URL
 * 
 * @param originalUrl - The original image URL (e.g., from R2)
 * @param options - Image transformation options
 * @param domain - Your Cloudflare domain (defaults to current domain)
 * @returns Optimized image URL
 */
export function getOptimizedImageUrl(
  originalUrl: string,
  options: CloudflareImageOptions = {},
  domain?: string
): string {
  if (!originalUrl) return originalUrl
  
  try {
    // Extract the domain from the image URL (dynamic approach)
    const urlObj = new URL(originalUrl)
    const baseUrl = domain || urlObj.origin
    const path = urlObj.pathname + urlObj.search
    
    // Default options for better performance
    const defaultOptions: CloudflareImageOptions = {
      format: 'auto', // Automatically serve WebP/AVIF when supported
      quality: 85,    // Good balance of quality vs file size
      fit: 'scale-down' // Don't upscale images
    }
    
    const mergedOptions = { ...defaultOptions, ...options }
    
    // Build the options string
    const optionsArray: string[] = []
    
    if (mergedOptions.width) optionsArray.push(`width=${mergedOptions.width}`)
    if (mergedOptions.height) optionsArray.push(`height=${mergedOptions.height}`)
    if (mergedOptions.quality) optionsArray.push(`quality=${mergedOptions.quality}`)
    if (mergedOptions.format) optionsArray.push(`format=${mergedOptions.format}`)
    if (mergedOptions.fit) optionsArray.push(`fit=${mergedOptions.fit}`)
    if (mergedOptions.gravity) optionsArray.push(`gravity=${mergedOptions.gravity}`)
    if (mergedOptions.blur) optionsArray.push(`blur=${mergedOptions.blur}`)
    if (mergedOptions.sharpen) optionsArray.push(`sharpen=${mergedOptions.sharpen}`)
    if (mergedOptions.brightness) optionsArray.push(`brightness=${mergedOptions.brightness}`)
    if (mergedOptions.contrast) optionsArray.push(`contrast=${mergedOptions.contrast}`)
    if (mergedOptions.gamma) optionsArray.push(`gamma=${mergedOptions.gamma}`)
    if (mergedOptions.trim) optionsArray.push(`trim=${mergedOptions.trim}`)
    
    const optionsString = optionsArray.join(',')
    
    // Construct the Cloudflare Image Resizing URL
    return `${baseUrl}/cdn-cgi/image/${optionsString}${path}`
  } catch {
    // If URL parsing fails, return original URL
    return originalUrl
  }
}

/**
 * Pre-configured helpers for common use cases
 */

// For winner cards (small thumbnails)
export function getWinnerImageUrl(originalUrl: string): string {
  return getOptimizedImageUrl(originalUrl, {
    width: 120,
    height: 120,
    quality: 80,
    fit: 'contain'
  })
}

// For pack cards (medium size)
export function getPackImageUrl(originalUrl: string, isMobile: boolean = false): string {
  return getOptimizedImageUrl(originalUrl, {
    width: isMobile ? 160 : 240,
    height: isMobile ? 160 : 240,
    quality: 85,
    fit: 'contain'
  })
}

// For banner/hero images (large, responsive)
export function getBannerImageUrl(originalUrl: string, screenWidth: number): string {
  let width = 1920 // Default for large screens
  
  if (screenWidth <= 640) width = 640
  else if (screenWidth <= 1024) width = 1024
  else if (screenWidth <= 1536) width = 1536
  
  return getOptimizedImageUrl(originalUrl, {
    width,
    quality: 90,
    fit: 'cover'
  })
}

// For profile/avatar images
export function getAvatarImageUrl(originalUrl: string, size: number = 80): string {
  return getOptimizedImageUrl(originalUrl, {
    width: size,
    height: size,
    quality: 85,
    fit: 'cover',
    gravity: 'center'
  })
}

/**
 * React Hook for responsive image optimization
 * Automatically determines optimal size based on screen width
 */
export function useResponsiveImageUrl(
  originalUrl: string,
  type: 'winner' | 'pack' | 'banner' | 'avatar' = 'pack'
): string {
  if (typeof window === 'undefined') {
    // Server-side rendering fallback
    return originalUrl
  }
  
  const screenWidth = window.innerWidth
  const isMobile = screenWidth < 768
  
  switch (type) {
    case 'winner':
      return getWinnerImageUrl(originalUrl)
    case 'pack':
      return getPackImageUrl(originalUrl, isMobile)
    case 'banner':
      return getBannerImageUrl(originalUrl, screenWidth)
    case 'avatar':
      return getAvatarImageUrl(originalUrl)
    default:
      return originalUrl
  }
}
