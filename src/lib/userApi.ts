import axios from 'axios';
import { getAuthHeaders } from './authUtils';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://user-backend-351785787544.us-central1.run.app/users/api/v1';

// Get config with auth headers
const getAuthConfig = async () => {
  const authHeaders = await getAuthHeaders();
  return {
    headers: {
      'Content-Type': 'application/json',
      ...authHeaders,
    },
  };
};

// Address data interface matching backend API requirements
interface AddressData {
  id?: string;
  name: string;
  street: string;
  city: string;
  state: string;
  zip: string;
  country: string;
}

interface UserInfo {
  createdAt: string;
  displayName: string;
  email: string;
  addresses: Array<AddressData>;
  avatar: string;
  level: number;
  pointsBalance: number;
  totalCashRecharged: number;
  totalPointsSpent: number;
  totalFusion: number;
  clientSeed: string;
  referred_by: string;
  total_point_refered: number;
  stripe_account_id: string;
  new_account?: boolean;
}

interface ReferCodeResponse {
  user_id: string;
  refer_code: string;
}

interface ReferralUser {
  id: string;
  displayName: string;
  avatar: string;
  createdAt: string;
}

interface ReferralsResponse {
  user_id: string;
  total_referred: number;
  total_point_refered: number;
}

interface CreateAccountParams {
  email: string;
  displayName: string;
  avatar?: string;
}

// User card related interfaces
interface UserCard {
  card_reference: string;
  card_name: string;
  date_got: string;
  id: string;
  image_url: string;
  point_worth: number;
  quantity: number;
  rarity: number;
  locked_quantity: number;
  expireAt: string;
  buybackexpiresAt: string;
  request_date: string;
  subcollection_name: string;
}

interface UserCardSubcollection {
  subcollection_name: string;
  cards: UserCard[];
  pagination: {
    total_items: number;
    items_per_page: number;
    current_page: number;
    total_pages: number;
  };
  filters: {
    sort_by: string;
    sort_order: string;
    search_query: string;
    collection_id: string;
    filter_out_accepted: boolean;
  };
}

interface UserCardsResponse {
  subcollections: UserCardSubcollection[];
}

interface UserCardsParams {
  collection_id?: string;
  page?: number;
  per_page?: number;
  sort_by?: string;
  sort_order?: string;
  search_query?: string;
}

// Highlights card related interfaces
interface HighlightCard {
  card_reference: string;
  card_name: string;
  date_got: string;
  id: string;
  image_url: string;
  point_worth: number;
  quantity: number;
  rarity: number;
  locked_quantity: number;
  condition?: string;
  expireAt: string;
  buybackexpiresAt: string;
  request_date: string;
  subcollection_name: string;
}

interface HighlightsPagination {
  total_items: number;
  items_per_page: number;
  current_page: number;
  total_pages: number;
}

interface HighlightsFilters {
  sort_by: string;
  sort_order: string;
  search_query: string;
  collection_id: string;
  filter_out_accepted: boolean;
}

interface HighlightsResponse {
  subcollection_name: string;
  cards: HighlightCard[];
  pagination: HighlightsPagination;
  filters: HighlightsFilters;
}

interface HighlightsParams {
  page?: number;
  per_page?: number;
  sort_by?: string;
  sort_order?: string;
  search_query?: string;
}

export const userApi = {
  // Create user account
  createAccount: async (params: CreateAccountParams): Promise<UserInfo> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.post(`${API_BASE_URL}/accounts/create-account`, {
        ...params,
        addresses: [],
        totalFusion: 0
      }, config);
      return response.data;
    } catch (error) {
      console.error('Failed to create account:', error);
      throw error;
    }
  },

  // Get user info
  getUserInfo: async (): Promise<UserInfo> => {
    try {
      const config = await getAuthConfig();
      console.log('getUserInfo config:', config);
      const response = await axios.get(`${API_BASE_URL}/accounts/profile`, config);
      console.log('getUserInfo response:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Failed to get user info:', error);
      console.error('Error response:', error.response?.data);
      console.error('Error status:', error.response?.status);
      throw error;
    }
  },

  // Get only user points balance (more efficient than full profile)
  getUserPointsBalance: async (): Promise<{pointsBalance: number, totalPointsSpent: number, totalCashRecharged: number}> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.get(`${API_BASE_URL}/accounts/points-balance`, config);
      return response.data;
    } catch (error: any) {
      console.error('Failed to get user points balance:', error);
      throw error;
    }
  },

  // Update user profile (display name and avatar)
  updateProfile: async (displayName: string, avatar: string | null): Promise<any> => {
    try {
      const config = await getAuthConfig();
      const requestData: any = { displayName };
      // Only include avatar if it's not null
      if (avatar !== null) {
        requestData.avatar = avatar;
      }
      console.log('Updating profile with data:', { 
        displayName, 
        hasAvatar: avatar !== null,
        avatarLength: avatar ? avatar.length : 0 
      });
      const response = await axios.put(`${API_BASE_URL}/accounts/profile`, requestData, config);
      console.log('Profile update response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Failed to update profile:', error);
      throw error;
    }
  },

  // Update user email
  updateEmail: async (email: string): Promise<UserInfo> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.put(`${API_BASE_URL}/accounts/email`, {
        email
      }, config);
      return response.data;
    } catch (error) {
      console.error('Failed to update email:', error);
      throw error;
    }
  },

  // Add user address
  addAddress: async (address: AddressData, skipValidation: boolean = false): Promise<UserInfo> => {
    try {
      const config = await getAuthConfig();
      const url = skipValidation 
        ? `${API_BASE_URL}/accounts/addresses?skip_validation=true`
        : `${API_BASE_URL}/accounts/addresses`;
      const response = await axios.post(url, address, config);
      return response.data;
    } catch (error) {
      console.error('Failed to add address:', error);
      throw error;
    }
  },

  // Update user address
  updateAddress: async (addressId: string, address: AddressData, skipValidation: boolean = false): Promise<UserInfo> => {
    try {
      const config = await getAuthConfig();
      const url = skipValidation 
        ? `${API_BASE_URL}/accounts/addresses/${addressId}?skip_validation=true`
        : `${API_BASE_URL}/accounts/addresses/${addressId}`;
      const response = await axios.put(url, address, config);
      return response.data;
    } catch (error) {
      console.error('Failed to update address:', error);
      throw error;
    }
  },

  // Delete user address
  deleteAddress: async (addressId: string): Promise<UserInfo> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.delete(`${API_BASE_URL}/accounts/addresses/${addressId}`, config);
      return response.data;
    } catch (error) {
      console.error('Failed to delete address:', error);
      throw error;
    }
  },

  // Update user points
  updatePoints: async (points: number): Promise<UserInfo> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.post(`${API_BASE_URL}/accounts/points`, { points }, config);
      return response.data;
    } catch (error) {
      console.error('Failed to update points:', error);
      throw error;
    }
  },

  // Get user referral code
  getReferCode: async (): Promise<ReferCodeResponse> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.get(`${API_BASE_URL}/accounts/refer-code`, config);
      return response.data;
    } catch (error) {
      console.error('Failed to get referral code:', error);
      throw error;
    }
  },

  // Get user referrals
  getReferrals: async (): Promise<ReferralsResponse> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.get(`${API_BASE_URL}/accounts/referrals`, config);
      return response.data;
    } catch (error) {
      console.error('Failed to get referrals:', error);
      throw error;
    }
  },

  // Check if user has been referred
  checkRefer: async (): Promise<{ user_id: string; is_referred: boolean; referer_id?: string }> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.get(`${API_BASE_URL}/accounts/check-refer`, config);
      return response.data;
    } catch (error) {
      console.error('Failed to check referral status:', error);
      throw error;
    }
  },

  // Validate a referral code
  validateReferralCode: async (referralCode: string): Promise<{ 
    is_valid: boolean; 
    owner_id: string | null; 
    owner_name: string | null; 
    error: string | null;
  }> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.get(`${API_BASE_URL}/accounts/validate-referral/${referralCode}`, config);
      return response.data;
    } catch (error) {
      console.error('Failed to validate referral code:', error);
      throw error;
    }
  },

  // Get user cards
  getUserCards: async (params: UserCardsParams = {}): Promise<UserCardsResponse> => {
    try {
      const config = await getAuthConfig();
      const queryParams = new URLSearchParams();
      // collection_id is required by the backend API, so we should not make the request without it
      if (!params.collection_id) {
        console.error('collection_id is required but was not provided');
        // Return empty response structure to prevent errors
        return {
          subcollections: []
        };
      }
      queryParams.append('collection_id', params.collection_id);
      if (params.page) queryParams.append('page', params.page.toString());
      if (params.per_page) queryParams.append('per_page', params.per_page.toString());
      if (params.sort_by) queryParams.append('sort_by', params.sort_by);
      if (params.sort_order) queryParams.append('sort_order', params.sort_order);
      if (params.search_query) queryParams.append('search_query', params.search_query);
      
      const url = `/cards/cards${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
      const response = await axios.get(`${API_BASE_URL}${url}`, config);
      return response.data;
    } catch (error) {
      console.error('Failed to get user cards:', error);
      throw error;
    }
  },

  // Add card to highlights
  addCardToHighlights: async (cardCollectionId: string, cardId: string): Promise<void> => {
    try {
      const config = await getAuthConfig();
      await axios.post(`${API_BASE_URL}/cards/cards/${cardCollectionId}/${cardId}/highlights`, {}, config);
    } catch (error) {
      console.error('Failed to add card to highlights:', error);
      throw error;
    }
  },

  // Get user highlights cards
  getHighlights: async (params?: {
    collection_id?: string;
    page?: number;
    per_page?: number;
    search_query?: string;
  }): Promise<{ cards: UserCard[], pagination: HighlightsPagination }> => {
    try {
      const config = await getAuthConfig();
      const queryParams = new URLSearchParams();
      if (params?.collection_id) queryParams.append('collection_id', params.collection_id);
      if (params?.page) queryParams.append('page', params.page.toString());
      if (params?.per_page) queryParams.append('per_page', params.per_page.toString());
      if (params?.search_query) queryParams.append('search_query', params.search_query);
      
      const url = `/cards/highlights${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
      const response = await axios.get(`${API_BASE_URL}${url}`, config);
      return response.data;
    } catch (error) {
      console.error('Failed to get highlights cards:', error);
      throw error;
    }
  },

  // Remove card from highlights
  removeCardFromHighlights: async (cardId: string): Promise<void> => {
    try {
      const config = await getAuthConfig();
      await axios.delete(`${API_BASE_URL}/cards/highlights/${cardId}`, config);
    } catch (error) {
      console.error('Failed to remove card from highlights:', error);
      throw error;
    }
  },

  // Get user by reference (alias for getPublicProfile for compatibility)
  getUserByReference: async (userReference: string): Promise<{
    nickname?: string;
    username?: string;
    avatar_url?: string;
    displayName?: string;
    avatar?: string;
  }> => {
    try {
      const profile = await userApi.getPublicProfile(userReference);
      return {
        nickname: profile.displayName,
        username: profile.displayName,
        avatar_url: profile.avatar,
        displayName: profile.displayName,
        avatar: profile.avatar
      };
    } catch (error) {
      console.error('Error fetching user by reference:', error);
      return {
        nickname: userReference,
        username: userReference,
        displayName: userReference,
        avatar_url: '/avatars/default.svg',
        avatar: '/avatars/default.svg'
      };
    }
  },

  // Get public profile data for any user
  getPublicProfile: async (userId: string): Promise<{
    displayName: string;
    avatar: string;
    level: number;
    totalAchievements: number;
    highlights: Array<{
      id: string;
      card_name: string;
      image_url: string;
      point_worth: number;
      rarity: number;
      card_collection_id?: string;
      card_reference?: string;
    }>;
    achievementHighlights: Array<{
      id: string;
      name: string;
      description: string;
      emblemUrl: string;
      achieved: boolean;
      awardedAt: any;
    }>;
  }> => {
    try {
      // This endpoint doesn't require authentication
      const response = await axios.get(`${API_BASE_URL}/accounts/users/${userId}/public-profile`);
      return response.data;
    } catch (error) {
      console.error('Error fetching public profile:', error);
      throw error;
    }
  },

  // Draw multiple cards
  drawMultipleCards: async (packId: string, collectionId: string, count: number): Promise<{ cards: UserCard[] }> => {
    try {
      const config = await getAuthConfig();
      // Check if packId looks like it's URL encoded (contains %XX pattern where X is hex)
      let finalPackId = packId;
      const urlEncodedPattern = /%[0-9A-Fa-f]{2}/;
      if (urlEncodedPattern.test(packId)) {
        try {
          const decoded = decodeURIComponent(packId);
          // Only use decoded version if it's different from original
          if (decoded !== packId) {
            finalPackId = decoded;
          }
        } catch (e) {
          // If decoding fails, use original packId
          console.warn('Failed to decode packId:', e);
        }
      }
      const queryParams = new URLSearchParams();
      queryParams.append('pack_id', finalPackId);
      queryParams.append('collection_id', collectionId);
      queryParams.append('count', count.toString());
      
      const url = `/cards/draw-multiple-cards?${queryParams.toString()}`;
      const response = await axios.post(`${API_BASE_URL}${url}`, {}, config);
      
      // Check achievements after drawing cards (only if auth state ready)
      import('@/lib/achievementChecker')
        .then(async ({ checkDrawAchievementsGlobal }) => {
          const { auth } = await import('@/lib/firebase')
          if (auth.currentUser) {
            checkDrawAchievementsGlobal().catch(() => undefined)
          }
        })
        .catch(() => undefined)
      
      return response.data;
    } catch (error: any) {
      console.error('Failed to draw multiple cards:', error);
      
      // Check if it's an insufficient funds error
      if (error.response?.status === 400 && error.response?.data?.detail) {
        const detail = error.response.data.detail;
        if (detail.includes('Insufficient points balance')) {
          // Extract the required points from the error message
          const match = detail.match(/need (\d+) points/);
          const requiredPoints = match ? parseInt(match[1]) : 0;
          
          // Create a custom error with a user-friendly message
          const customError: any = new Error('Insufficient funds');
          customError.code = 'INSUFFICIENT_FUNDS';
          customError.detail = detail;
          customError.requiredPoints = requiredPoints;
          throw customError;
        }
      }
      
      throw error;
    }
  },

  // Demo draw cards interface
  demoDrawMultipleCards: async (packId: string, collectionId: string, count: number): Promise<UserCard[]> => {
    try {
      const config = await getAuthConfig();
      // Check if packId looks like it's URL encoded (contains %XX pattern where X is hex)
      let finalPackId = packId;
      const urlEncodedPattern = /%[0-9A-Fa-f]{2}/;
      if (urlEncodedPattern.test(packId)) {
        try {
          const decoded = decodeURIComponent(packId);
          // Only use decoded version if it's different from original
          if (decoded !== packId) {
            finalPackId = decoded;
          }
        } catch (e) {
          // If decoding fails, use original packId
          console.warn('Failed to decode packId:', e);
        }
      }
      const queryParams = new URLSearchParams();
      queryParams.append('pack_id', finalPackId);
      queryParams.append('collection_id', collectionId);
      queryParams.append('count', count.toString());
      
      const url = `/cards/demo-draw-multiple-cards?${queryParams.toString()}`;
      const response = await axios.post(`${API_BASE_URL}${url}`, {}, config);
      return response.data;
    } catch (error) {
      console.error('Demo draw failed:', error);
      throw error;
    }
  },

  // Batch destroy cards (redeem for points)
  batchDestroyCards: async (cards: { card_id: string; quantity: number; subcollection_name?: string }[]): Promise<{ cards_destroyed: number; message: string; points_added: number; remaining_points: number }> => {
    try {
      const config = await getAuthConfig();
      // Backend currently expects a query param subcollection_name; provide a fallback while sending per-card values in body
      const fallbackSub = cards.find(c => !!c.subcollection_name)?.subcollection_name || 'mixed';
      const url = `/cards/batch-destroy-cards?subcollection_name=${encodeURIComponent(fallbackSub)}`;
      // Use axios.request to properly send body with DELETE method
      const response = await axios.request({
        method: 'DELETE',
        url: `${API_BASE_URL}${url}`,
        data: { cards: cards.map(c => ({
          card_id: c.card_id,
          quantity: c.quantity,
          subcollection_name: c.subcollection_name
        })) },
        ...config
      });
      return response.data;
    } catch (error) {
      console.error('Failed to batch destroy cards:', error);
      throw error;
    }
  },
  // Get user's primary address
  getUserAddress: async (): Promise<any> => {
    try {
      const config = await getAuthConfig();
      const userInfo = await userApi.getUserInfo();
      // Return the first address if available
      return userInfo.addresses?.[0] || null;
    } catch (error) {
      console.error('Failed to get user address:', error);
      throw error;
    }
  },

  // Calculate withdrawal fee
  calculateWithdrawalFee: async (country: string, totalValue: number): Promise<{
    zone: number;
    fee: number;
    free_shipping_threshold: number;
    is_free_shipping: boolean;
    country: string;
  }> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.post(`${API_BASE_URL}/withdrawals/withdraw/calculate-fee`, {
        country,
        total_value: totalValue
      }, config);
      return response.data;
    } catch (error) {
      console.error('Failed to calculate withdrawal fee:', error);
      throw error;
    }
  },

  // Create withdrawal request
  createWithdrawal: async (withdrawalData: any): Promise<any> => {
    try {
      const config = await getAuthConfig();
      // Ensure we send card_id, quantity, and subcollection_name to backend
      const payload = {
        ...withdrawalData,
        cards: (withdrawalData.cards || []).map((c: any) => ({ 
          card_id: c.card_id, 
          quantity: c.quantity,
          subcollection_name: c.subcollection_name
        }))
      };
      const response = await axios.post(`${API_BASE_URL}/withdrawals/cards/withdraw`, payload, config);
      return response.data;
    } catch (error) {
      console.error('Failed to create withdrawal:', error);
      throw error;
    }
  },

  // Create withdrawal request
  createWithdrawRequest: async (params: {
    cards: Array<{ card_id?: string; card_name?: string; quantity: number; subcollection_name?: string }>;
    address_id: string;
    phone_number: string;
  }): Promise<{ cards: UserCard[] }> => {
    try {
      const config = await getAuthConfig();
      // Map to backend-required shape
      const payload = {
        address_id: params.address_id,
        phone_number: params.phone_number,
        cards: params.cards.map((c) => ({ 
          card_id: c.card_id,
          quantity: c.quantity,
          subcollection_name: c.subcollection_name
        }))
      };
      const response = await axios.post(`${API_BASE_URL}/withdrawals/cards/withdraw`, payload, config);
      return response.data;
    } catch (error) {
      console.error('Failed to create withdrawal request:', error);
      throw error;
    }
  },

  // Get user withdrawal requests
  getWithdrawRequests: async (params?: {
    page?: number;
    per_page?: number;
    sort_by?: string;
    sort_order?: string;
  }): Promise<WithdrawRequestsResponse> => {
    try {
      const config = await getAuthConfig();
      const queryParams = new URLSearchParams();
      if (params?.page) queryParams.append('page', params.page.toString());
      if (params?.per_page) queryParams.append('per_page', params.per_page.toString());
      if (params?.sort_by) queryParams.append('sort_by', params.sort_by);
      if (params?.sort_order) queryParams.append('sort_order', params.sort_order);
      
      const url = `/withdrawals/withdraw-requests${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
      const response = await axios.get(`${API_BASE_URL}${url}`, config);
      return response.data;
    } catch (error) {
      console.error('Failed to get withdrawal requests:', error);
      throw error;
    }
  },

  // Get specific withdrawal request details
  getWithdrawRequestDetails: async (requestId: string): Promise<WithdrawRequestDetails> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.get(`${API_BASE_URL}/withdrawals/withdraw-requests/${requestId}`, config);
      return response.data;
    } catch (error) {
      console.error('Failed to get withdrawal request details:', error);
      throw error;
    }
  },

  // Update withdrawal request
  updateWithdrawRequest: async (requestId: string, params: {
    address_id?: string;
    phone_number?: string;
  }): Promise<WithdrawRequestDetails> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.put(`${API_BASE_URL}/withdrawals/withdraw-requests/${requestId}`, params, config);
      return response.data;
    } catch (error) {
      console.error('Failed to update withdrawal request:', error);
      throw error;
    }
  },

  // Delete withdrawal request
  deleteWithdrawRequest: async (requestId: string): Promise<WithdrawRequestDetails> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.delete(`${API_BASE_URL}/withdrawals/withdraw-requests/${requestId}`, config);
      return response.data;
    } catch (error) {
      console.error('Failed to delete withdrawal request:', error);
      throw error;
    }
  },

  // Check daily reward claim status
  checkDailyRewardStatus: async (): Promise<{
    has_claimed: boolean;
    today_date: string;
    claim_id: string | null;
  }> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.get(`${API_BASE_URL}/accounts/daily-reward-status`, config);
      return response.data;
    } catch (error) {
      console.error('Failed to check daily reward status:', error);
      throw error;
    }
  },

  // Claim daily reward (draw daily free box)
  claimDailyReward: async (): Promise<{
    message: string;
    card: {
      card_name: string;
      point_worth: number;
      image_url: string;
      rarity: number;
      color?: string;
    };
  }> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.post(`${API_BASE_URL}/cards/daily-free-box`, {}, config);
      
      // Check achievements after claiming daily reward
      import('@/lib/achievementChecker')
        .then(async ({ checkDrawAchievementsGlobal }) => {
          const { auth } = await import('@/lib/firebase')
          if (auth.currentUser) {
            checkDrawAchievementsGlobal().catch(() => undefined)
          }
        })
        .catch(() => undefined)
      
      return response.data;
    } catch (error) {
      console.error('Failed to claim daily reward:', error);
      throw error;
    }
  },

  // Mark user onboarding as complete
  completeOnboarding: async (): Promise<UserInfo> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.put(`${API_BASE_URL}/accounts/complete-onboarding`, {}, config);
      return response.data;
    } catch (error) {
      console.error('Failed to complete onboarding:', error);
      throw error;
    }
  },

  // Update new account status
  updateNewAccountStatus: async (newAccount: boolean): Promise<UserInfo> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.put(`${API_BASE_URL}/accounts/new-account`, {
        new_account: newAccount
      }, config);
      return response.data;
    } catch (error) {
      console.error('Failed to update new account status:', error);
      throw error;
    }
  }
};

// Withdrawal related interface definitions
interface WithdrawRequest {
  id: string;
  created_at: string;
  request_date: string;
  status: string;
  user_id: string;
  card_count: number;
  shipping_address: any;
  shippo_address_id: string;
  shippo_parcel_id: string;
  shippo_shipment_id: string;
  shippo_transaction_id: string;
  shippo_label_url: string;
  tracking_number: string;
  tracking_url: string;
  shipping_status: string;
}

interface WithdrawRequestsResponse {
  withdraw_requests: WithdrawRequest[];
  pagination: {
    total_items: number;
    items_per_page: number;
    current_page: number;
    total_pages: number;
  };
}

interface WithdrawRequestDetails extends WithdrawRequest {
  cards: {
    card_reference: string;
    card_name: string;
    date_got: string;
    id: string;
    image_url: string;
    point_worth: number;
    quantity: number;
    rarity: number;
    locked_quantity: number;
    expireAt: string;
    buybackexpiresAt: string;
    request_date: string;
    subcollection_name: string;
  }[];
}

// Export type definitions
export type { 
  UserCard, 
  UserCardSubcollection, 
  UserCardsResponse, 
  UserCardsParams,
  HighlightCard, 
  HighlightsResponse, 
  HighlightsParams,
  AddressData,
  UserInfo,
  WithdrawRequest,
  WithdrawRequestsResponse,
  WithdrawRequestDetails
};