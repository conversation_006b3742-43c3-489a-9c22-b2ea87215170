let lockCount = 0;
let prevOverflow = '';
let prevPaddingRight = '';
let prevTouchAction = '' as any;
let prevOverscrollBehavior = '' as any;
let prevPosition = '';
let prevTop = '';
let prevWidth = '';
let savedScrollY: number | null = null;
let touchListenerAdded = false;

function applyStylesForLock() {
  const body = document.body;
  prevOverflow = body.style.overflow;
  prevPaddingRight = body.style.paddingRight;
  prevTouchAction = (body.style as any).touchAction;
  prevOverscrollBehavior = (body.style as any).overscrollBehavior;
  prevPosition = body.style.position;
  prevTop = body.style.top;
  prevWidth = body.style.width;

  const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;
  // Preserve scroll position using position: fixed technique (works better on iOS)
  savedScrollY = window.scrollY;
  body.style.top = `-${savedScrollY}px`;
  body.style.position = 'fixed';
  body.style.width = '100%'; // Ensure body maintains full width
  body.style.overflow = 'hidden';
  (body.style as any).touchAction = 'none';
  (body.style as any).overscrollBehavior = 'contain';
  if (scrollbarWidth > 0) {
    body.style.paddingRight = `${scrollbarWidth}px`;
  }

}

function restoreStylesAfterUnlock() {
  const body = document.body;
  body.style.overflow = prevOverflow;
  body.style.paddingRight = prevPaddingRight;
  (body.style as any).touchAction = prevTouchAction;
  (body.style as any).overscrollBehavior = prevOverscrollBehavior;
  body.style.position = prevPosition;
  body.style.width = prevWidth;
  const top = body.style.top;
  body.style.top = prevTop;

  if (typeof savedScrollY === 'number') {
    // Restore scroll position
    window.scrollTo(0, savedScrollY);
  } else if (top) {
    const y = parseInt(top) || 0;
    if (y) window.scrollTo(0, -y);
  }
  savedScrollY = null;

}

export function lockBodyScroll() {
  if (typeof document === 'undefined') return;
  if (lockCount === 0) {
    applyStylesForLock();
  }
  lockCount += 1;
}

export function unlockBodyScroll() {
  if (typeof document === 'undefined') return;
  if (lockCount === 0) return;
  lockCount -= 1;
  if (lockCount === 0) {
    restoreStylesAfterUnlock();
  }
}

export function forceUnlockBodyScroll() {
  if (typeof document === 'undefined') return;
  lockCount = 0;
  restoreStylesAfterUnlock();
}

