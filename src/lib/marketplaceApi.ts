import axios from 'axios';
import { getAuthHeaders } from './authUtils';
 
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://user-backend-351785787544.us-central1.run.app/users/api/v1';

// 默认请求配置
const defaultConfig = {
  headers: {
    'Content-Type': 'application/json'
  }
};

// 获取带认证的配置
const getAuthConfig = async () => {
  const authHeaders = await getAuthHeaders();
  return {
    headers: {
      'Content-Type': 'application/json',
      ...authHeaders
    }
  };
};

// 用户挂售卡牌接口
export interface Listing {
  id: string;
  owner_reference: string;
  card_reference: string;
  collection_id: string;
  quantity: number;
  createdAt: string;
  expiresAt: string;
  pricePoints: number;
  priceCash: number;
  highestOfferPoints: Record<string, number | string>;
  highestOfferCash: Record<string, number | string>;
  image_url: string;
  card_name: string;
}

// 官方市场卡牌接口
export interface OfficialCard {
  id: string;
  card_name: string;
  card_reference: string;
  collection_id: string;
  condition: string;
  date_got_in_stock: string;
  image_url: string;
  pricePoints: number;
  quantity: number;
  rarity: number;
}

// 报价接口
export interface Offer {
  amount: number;
  at: string;
  card_reference: string;
  collection_id: string;
  expiresAt: string;
  image_url: string;
  listingId: string;
  offererRef: string;
  offerreference: string;
  payment_due: string;
  status: string;
  type: string;
}

// 分页接口
export interface Pagination {
  total_items: number;
  items_per_page: number;
  current_page: number;
  total_pages: number;
}

// 筛选接口
export interface Filters {
  sort_by: string;
  sort_order: 'asc' | 'desc';
  search_query: string;
  collection_id: string;
  filter_out_accepted: boolean;
}

// 列表响应接口
export interface ListingsResponse {
  listings: Listing[];
  pagination: Pagination;
  filters: Filters;
}

// 官方列表响应接口
export interface OfficialListingsResponse {
  cards: OfficialCard[];
  pagination: Pagination;
  filters: Filters;
}

// 报价列表响应接口
export interface OffersResponse {
  offers: Offer[];
}

// 用户卡片接口
export interface UserCard {
  id: string;
  card_id: string;
  card_name: string;
  collection_id: string;
  image_url: string;
  quantity: number;
  rarity: number;
  condition: string;
}

// 创建列表响应接口
export interface CreateListingResponse {
  listing?: Listing;
  has_stripe_connect?: boolean;
  stripe_connect_status?: string;
  onboarding_url?: string;
}

// 市场API接口
const marketplaceApi = {
  // 获取用户挂售的卡牌
  getUserListings: async (): Promise<Listing[]> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.get(`${API_BASE_URL}/marketplace/listings`, config);
      return response.data;
    } catch (error) {
      console.error('获取用户挂售卡牌失败:', error);
      throw error;
    }
  },

  // 修改用户挂售卡牌信息
  updateUserListing: async (params: {
    collection_id: string;
    card_id: string;
    quantity: number;
    pricePoints: number;
    priceCash: number;
    expiresAt: string;
    card_name: string;
  }): Promise<Listing> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.post(`${API_BASE_URL}/marketplace/listings`, params, config);
      return response.data;
    } catch (error) {
      console.error('修改用户挂售卡牌信息失败:', error);
      throw error;
    }
  },

  // 删除用户挂售卡牌
  deleteUserListing: async (listingId: string): Promise<void> => {
    try {
      const config = await getAuthConfig();
      await axios.delete(`${API_BASE_URL}/marketplace/listings/${listingId}`, config);
    } catch (error) {
      console.error('删除用户挂售卡牌失败:', error);
      throw error;
    }
  },

  // 获取用户挂售卡牌详情
  getUserListingDetail: async (listingId: string): Promise<Listing> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.get(`${API_BASE_URL}/marketplace/listings/${listingId}`, config);
      return response.data;
    } catch (error) {
      console.error('获取用户挂售卡牌详情失败:', error);
      throw error;
    }
  },

  // 为商品提供报价积分
  createPointsOffer: async (params: { listing_id: string, price: number, expired?: number }): Promise<Offer> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.post(
        `${API_BASE_URL}/marketplace/listings/${params.listing_id}/offers/points?expired=${params.expired || 7}`,
        { points: params.price },
        config
      );
      return response.data;
    } catch (error) {
      console.error('为商品提供报价积分失败:', error);
      throw error;
    }
  },

  // 删除商品报价积分
  deletePointsOffer: async (listingId: string, offerId: string): Promise<void> => {
    try {
      const config = await getAuthConfig();
      await axios.delete(`${API_BASE_URL}/marketplace/listings/${listingId}/offers/points/${offerId}`, config);
    } catch (error) {
      console.error('删除商品报价积分失败:', error);
      throw error;
    }
  },

  // 更新商品报价积分
  updatePointsOffer: async (listingId: string, offerId: string, amount: number): Promise<Offer> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.put(
        `${API_BASE_URL}/marketplace/listings/${listingId}/offers/points/${offerId}`,
        { points: amount },
        config
      );
      return response.data;
    } catch (error) {
      console.error('更新商品报价积分失败:', error);
      throw error;
    }
  },

  // 上传商品现金报价
  createCashOffer: async (params: { listing_id: string, price: number, expired?: number }): Promise<Offer> => {
    console.log(params.listing_id,'----listingId')
    try {
      const config = await getAuthConfig();
      const response = await axios.post(
        `${API_BASE_URL}/marketplace/listings/${params.listing_id}/offers/cash?expired=${params.expired || 7}`,
        { cash: params.price },
        config
      );
      return response.data;
    } catch (error) {
      console.error('上传商品现金报价失败:', error);
      throw error;
    }
  },

  // 用户删除商品现金报价
  deleteCashOffer: async (listingId: string, offerId: string): Promise<void> => {
    try {
      const config = await getAuthConfig();
      await axios.delete(`${API_BASE_URL}/marketplace/listings/${listingId}/offers/cash/${offerId}`, config);
    } catch (error) {
      console.error('用户删除商品现金报价失败:', error);
      throw error;
    }
  },

  // 用户更新现金报价单
  updateCashOffer: async (listingId: string, offerId: string, amount: number): Promise<Offer> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.put(
        `${API_BASE_URL}/marketplace/listings/${listingId}/offers/cash/${offerId}`,
        { cash: amount },
        config
      );
      return response.data;
    } catch (error) {
      console.error('用户更新现金报价单失败:', error);
      throw error;
    }
  },

  // 接受商品的最高出价（现金或积分）
  acceptHighestOffer: async (listingId: string, offerType: 'cash' | 'point'): Promise<Listing> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.post(
        `${API_BASE_URL}/marketplace/listings/${listingId}/accept`,
        { offer_type: offerType },
        config
      );
      return response.data;
    } catch (error) {
      console.error('接受商品的最高出价失败:', error);
      throw error;
    }
  },

  // 获取用户的报价列表
  getUserOffers: async (offerType: 'cash' | 'point'): Promise<OffersResponse> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.get(`${API_BASE_URL}/marketplace/my_offers/${offerType}`, config);
      return response.data;
    } catch (error) {
      console.error('获取用户的报价列表失败:', error);
      throw error;
    }
  },

  // 获取用户的已接受报价列表
  getAcceptedOffers: async (offerType: 'cash' | 'point'): Promise<OffersResponse> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.get(`${API_BASE_URL}/marketplace/my_offers/${offerType}`, config);
      return response.data;
    } catch (error) {
      console.error('获取用户的已接受报价失败:', error);
      throw error;
    }
  },

  // 获取用户与别人的报价单
  getAllOffers: async (offerType: 'cash' | 'point', params: {
    page: number;
    per_page: number;
    sort_by?: 'amount' | 'at' | 'card_name';
    sort_order?: 'asc' | 'desc';
    search_query?: string;
  }): Promise<OffersResponse> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.get(`${API_BASE_URL}/marketplace/all_offers/${offerType}`, { ...config, params });
      return response.data;
    } catch (error) {
      console.error('获取用户与别人的报价单失败:', error);
      throw error;
    }
  },

  // 使用积分购买商品 (通过报价支付)
  payWithPointsOffer: async (listingId: string, offerId: string): Promise<{ success: boolean; transaction_id?: string; message?: string }> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.post(
        `${API_BASE_URL}/marketplace/listings/${listingId}/offers/${offerId}/pay`,
        {},
        config
      );
      return response.data;
    } catch (error) {
      console.error('使用积分购买商品失败:', error);
      throw error;
    }
  },

  // 使用积分直接购买商品 (按标价购买)
  payWithPoints: async (listingId: string, quantity: number = 1): Promise<{ success: boolean; transaction_id?: string; message?: string }> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.post(
        `${API_BASE_URL}/marketplace/listings/${listingId}/pay_price_point`,
        { quantity },
        config
      );
      
      // Check achievements after buying with points (silently ignore auth issues)
      import('@/lib/achievementChecker').then(({ checkBuyDealAchievementsGlobal }) => {
        checkBuyDealAchievementsGlobal().catch(() => undefined)
      }).catch(() => undefined)
      
      return response.data;
    } catch (error) {
      console.error('使用积分购买商品失败:', error);
      throw error;
    }
  },

  // 使用现金购买商品 (注意：根据API文档，现金购买也使用pay_price_point接口)
  payWithCash: async (listingId: string, quantity: number): Promise<{ success: boolean; transaction_id?: string; message?: string }> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.post(
        `${API_BASE_URL}/marketplace/listings/${listingId}/pay_price_point`,
        { quantity },
        config
      );
      
      // Check achievements after buying with cash (silently ignore auth issues)
      import('@/lib/achievementChecker').then(({ checkBuyDealAchievementsGlobal }) => {
        checkBuyDealAchievementsGlobal().catch(() => undefined)
      }).catch(() => undefined)
      
      return response.data;
    } catch (error) {
      console.error('使用现金购买商品失败:', error);
      throw error;
    }
  },

  // 获取用户作为买家的交易记录
  getBuyerTransactions: async (params: {
    page: number;
    per_page: number;
  }): Promise<{ transactions: Array<{ id: string; amount: number; status: string; created_at: string }>; pagination: Pagination }> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.get(`${API_BASE_URL}/marketplace/transactions/buyer`, { ...config, params });
      return response.data;
    } catch (error) {
      console.error('获取用户作为买家的交易记录失败:', error);
      throw error;
    }
  },

  // 获取用户作为卖家的交易记录
  getSellerTransactions: async (params: {
    page: number;
    per_page: number;
  }): Promise<{ transactions: Array<{ id: string; amount: number; status: string; created_at: string }>; pagination: Pagination }> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.get(`${API_BASE_URL}/marketplace/transactions/seller`, { ...config, params });
      return response.data;
    } catch (error) {
      console.error('获取用户作为卖家的交易记录失败:', error);
      throw error;
    }
  },

  // 从官方列表购买卡牌
  buyFromOfficialListing: async (params: {
    collection_id: string;
    card_id: string;
    quantity: number;
  }): Promise<{ 
    card: any;
    quantity: number;
    total_price: number;
    collection_id: string;
    message: string;
  }> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.post(
        `${API_BASE_URL}/marketplace/official_listings/buy?collection_id=${params.collection_id}&card_id=${params.card_id}&quantity=${params.quantity}`,
        {},
        config
      );
      // Note: buyFromOfficialListing doesn't count for buy_deal achievements
      return response.data;
    } catch (error: any) {
      console.error('从官方列表购买卡牌失败:', error);
      
      // Re-throw with more context if it's an axios error
      if (error.response?.data?.detail) {
        const errorDetail = error.response.data.detail;
        
        // Create a more user-friendly error object
        const enhancedError = new Error(errorDetail);
        (enhancedError as any).response = error.response;
        throw enhancedError;
      }
      
      throw error;
    }
  },

  // 获取市场中的所有列表
  getAllListings: async (params: {
    collection_id?: string;
    per_page?: number;
    sort_by?: 'priceCash' | 'pricePoints';
    sort_order?: 'asc' | 'desc';
    search_query?: string;
    page?: number;
    filter_out_accepted?: boolean;
    index_name?: string;
    min_price_cash?: number;
    max_price_cash?: number;
    min_price_points?: number;
    max_price_points?: number;
  }): Promise<ListingsResponse> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.get(`${API_BASE_URL}/marketplace/all-listings`, { ...config, params });
      return response.data;
    } catch (error) {
      console.error('获取市场中的所有列表失败:', error);
      throw error;
    }
  },

  // 获取特定集合的官方市场列表
  getOfficialListings: async (params: {
    collection_id?: string;
    page?: number;
    per_page?: number;
    sort_by?: 'pricePoints' | 'priceCash';
    sort_order?: 'asc' | 'desc';
    search_query?: string;
  }): Promise<OfficialListingsResponse> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.get(`${API_BASE_URL}/marketplace/official_listings`, { ...config, params });
      return response.data;
    } catch (error) {
      console.error('获取特定集合的官方市场列表失败:', error);
      throw error;
    }
  },

  // 获取用户拥有的卡片列表
  getUserCards: async (params?: {
    collection_id?: string;
    page?: number;
    per_page?: number;
    search_query?: string;
  }): Promise<{ cards: UserCard[], pagination: Pagination }> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.get(`${API_BASE_URL}/marketplace/cards`, { ...config, params });
      return response.data;
    } catch (error) {
      console.error('获取用户卡片列表失败:', error);
      throw error;
    }
  },

  // Create listing
  createListing: async (params: {
    card_id: string;
    quantity: number;
    collection_id: string;
    pricePoints?: number | null;
    priceCash?: number | null;
  }): Promise<CreateListingResponse> => {
    try {
      const config = await getAuthConfig();
      const payload: any = {
        card_id: params.card_id,
        quantity: params.quantity,
        collection_id: params.collection_id
      };
      
      // Add prices only if they are provided and not null
      if (params.pricePoints !== null && params.pricePoints !== undefined) {
        payload.pricePoints = params.pricePoints;
      }
      if (params.priceCash !== null && params.priceCash !== undefined) {
        payload.priceCash = params.priceCash;
      }
      
      const response = await axios.post(`${API_BASE_URL}/marketplace/listings`, payload, config);
      return response.data;
    } catch (error) {
      console.error('Failed to create listing:', error);
      throw error;
    }
  },
};

// Export named API object
export { marketplaceApi as default };