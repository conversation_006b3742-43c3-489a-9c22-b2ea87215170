import { auth } from './firebase'

// Use the standard API base URL for the cards endpoint
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://user-backend-769075815684.us-central1.run.app/users/api/v1'

export interface CardDetails {
  id: string;
  name?: string;
  card_name?: string;
  image_url: string;
  condition?: string;
  collection_name?: string;
  point_worth?: number;
  rarity?: number;
  // Fields from StoredCardInfo response
  document_id?: string;
  collection_id?: string;
  signed_image_url?: string;
  probability?: number;
  cash_worth?: number;
  globalRef?: string;
  [key: string]: any; // For other fields that might be in the response
}

export const cardsApi = {
  async getCardById(cardIdOrReference: string, collectionName?: string): Promise<CardDetails> {
    try {
      const params = new URLSearchParams()
      if (collectionName) {
        params.append('collection_name', collectionName)
      }

      const queryString = params.toString()
      // Use the /cards/card/{card_id} route as specified in the backend
      const url = `${API_BASE_URL}/cards/card/${cardIdOrReference}${queryString ? `?${queryString}` : ''}`

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error(`Failed to fetch card details: ${response.statusText}`)
      }

      const data = await response.json()
      return data
    } catch (error) {
      console.error('Error fetching card details:', error)
      throw error
    }
  }
}
