/**
 * Get the color theme for a pack based on its price
 * @param price - The price of the pack
 * @returns Object containing color classes and hex values
 */
export function getPackColorTheme(price: number | null | undefined) {
  const actualPrice = price ?? 0;
  
  if (actualPrice >= 5000) {
    return {
      borderColor: 'border-orange-500',
      bgColor: 'bg-orange-500',
      bgGradient: 'bg-gradient-to-r from-orange-400 to-orange-600',
      shadowColor: 'shadow-orange-500/50',
      hoverBg: 'hover:bg-orange-600',
      textColor: 'text-orange-500',
      hexColor: '#f97316', // orange-500
      colorName: 'orange',
      glowEffect: 'shadow-[0_0_15px_rgba(249,115,22,0.2)]',
      subtleGlow: 'shadow-[inset_0_0_20px_rgba(249,115,22,0.08)]',
      ringGlow: 'ring-2 ring-orange-500/40 ring-offset-2 ring-offset-transparent',
      boxShadow: 'shadow-[0_0_25px_rgba(249,115,22,0.4)]',
      buttonBg: 'bg-orange-500 hover:bg-orange-600'
    };
  } else if (actualPrice >= 2000) {
    return {
      borderColor: 'border-purple-500',
      bgColor: 'bg-purple-500',
      bgGradient: 'bg-gradient-to-r from-purple-400 to-purple-600',
      shadowColor: 'shadow-purple-500/50',
      hoverBg: 'hover:bg-purple-600',
      textColor: 'text-purple-500',
      hexColor: '#a855f7', // purple-500
      colorName: 'purple',
      glowEffect: 'shadow-[0_0_15px_rgba(168,85,247,0.2)]',
      subtleGlow: 'shadow-[inset_0_0_20px_rgba(168,85,247,0.08)]',
      ringGlow: 'ring-2 ring-purple-500/40 ring-offset-2 ring-offset-transparent',
      boxShadow: 'shadow-[0_0_25px_rgba(168,85,247,0.4)]',
      buttonBg: 'bg-purple-500 hover:bg-purple-600'
    };
  } else if (actualPrice >= 500) {
    return {
      borderColor: 'border-blue-500',
      bgColor: 'bg-blue-500',
      bgGradient: 'bg-gradient-to-r from-blue-400 to-blue-600',
      shadowColor: 'shadow-blue-500/50',
      hoverBg: 'hover:bg-blue-600',
      textColor: 'text-blue-500',
      hexColor: '#3b82f6', // blue-500
      colorName: 'blue',
      glowEffect: 'shadow-[0_0_15px_rgba(59,130,246,0.2)]',
      subtleGlow: 'shadow-[inset_0_0_20px_rgba(59,130,246,0.08)]',
      ringGlow: 'ring-2 ring-blue-500/40 ring-offset-2 ring-offset-transparent',
      boxShadow: 'shadow-[0_0_25px_rgba(59,130,246,0.4)]',
      buttonBg: 'bg-blue-500 hover:bg-blue-600'
    };
  } else {
    // Under 500 - green
    return {
      borderColor: 'border-green-500',
      bgColor: 'bg-green-500',
      bgGradient: 'bg-gradient-to-r from-green-400 to-green-600',
      shadowColor: 'shadow-green-500/50',
      hoverBg: 'hover:bg-green-600',
      textColor: 'text-green-500',
      hexColor: '#10b981', // green-500
      colorName: 'green',
      glowEffect: 'shadow-[0_0_15px_rgba(16,185,129,0.2)]',
      subtleGlow: 'shadow-[inset_0_0_20px_rgba(16,185,129,0.08)]',
      ringGlow: 'ring-2 ring-green-500/40 ring-offset-2 ring-offset-transparent',
      boxShadow: 'shadow-[0_0_25px_rgba(16,185,129,0.4)]',
      buttonBg: 'bg-green-500 hover:bg-green-600'
    };
  }
}

