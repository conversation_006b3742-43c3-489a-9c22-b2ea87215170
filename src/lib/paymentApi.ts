import axios from 'axios';
import { getAuthHeaders } from './authUtils';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://user-backend-351785787544.us-central1.run.app/users/api/v1';

// 获取带认证的配置
const getAuthConfig = async () => {
  const authHeaders = await getAuthHeaders();
  return {
    headers: {
      'Content-Type': 'application/json',
      ...authHeaders,
    },
  };
};

// 支付意向接口
export interface PaymentIntent {
  id: string;
  client_secret: string;
  amount: number;
  currency: string;
  status: string;
}

// 创建支付意向请求参数
export interface CreatePaymentIntentRequest {
  amount: number;        // 充值金额（美分）
  currency: string;      // 货币类型
  refer_code?: string;   // 可选：推荐码
  payment_method_id?: string; // 可选：使用已保存的支付方式ID
}

// 充值记录
export interface RechargeRecord {
  id: number;
  amount_cash: string;
  points_granted: number;
  created_at: string;
}

// 充值历史响应
export interface RechargeHistoryResponse {
  user_id: string;
  total_cash_recharged: number;
  recharge_history: RechargeRecord[];
}

// 分页充值历史响应
export interface PaginatedRechargeHistoryResponse {
  total_cash_recharged: number;
  recharge_history: RechargeRecord[];
  user_id: string;
  page: number;
  page_size: number;
  total_count: number;
  has_next: boolean;
}

// 支付方式
export interface PaymentMethod {
  id: string;
  type: string;
  card: {
    brand: string;
    last4: string;
    exp_month: number;
    exp_year: number;
    funding: string;
  };
  is_default: boolean;
  created_at: string;
}

// 支付方式列表响应
export interface PaymentMethodsResponse {
  payment_methods: PaymentMethod[];
  default_payment_method_id: string;
}

// 添加支付方式请求
export interface AddPaymentMethodRequest {
  payment_method_id: string;
  set_as_default: boolean;
}

// 支付状态响应
export interface PaymentStatusResponse {
  payment_intent_id: string;
  status: string;
  amount: number;
  currency: string;
  created_at: string;
  metadata: {
    user_id: string;
    listing_id?: string;
  };
}

// 用户推荐状态
export interface UserReferStatus {
  user_id: string;
  is_referred: boolean;
  referer_id: string;
}

// Stripe Connect状态响应
export interface StripeConnectStatusResponse {
  status: 'not_connected' | 'incomplete' | 'ready';
}

// Stripe Connect创建响应
export interface StripeConnectCreateResponse {
  onboarding_url: string;
}

// Stripe仪表板链接响应
export interface StripeDashboardResponse {
  login_url: string;
}

// 税务状态响应
export interface TaxStatusResponse {
  stripe_tax_enabled: boolean;
}

// 税务同意响应
export interface TaxConsentResponse {
  success: boolean;
  user_id: string;
}

export const paymentApi = {
  // 1. 创建支付意向
  createPaymentIntent: async (request: CreatePaymentIntentRequest): Promise<PaymentIntent> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.post(
        `${API_BASE_URL}/payments/payment/create-intent`,
        request,
        config
      );
      return response.data;
    } catch (error) {
      console.error('Failed to create payment intent:', error);
      throw error;
    }
  },

  // 1b. 创建Setup Intent (for adding payment methods)
  createSetupIntent: async (): Promise<{ client_secret: string }> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.post(
        `${API_BASE_URL}/payments/payment/create-setup-intent`,
        {},
        config
      );
      return response.data;
    } catch (error) {
      console.error('Failed to create setup intent:', error);
      throw error;
    }
  },

  // 2. 查询充值记录
  getRechargeHistory: async (): Promise<RechargeHistoryResponse> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.get(
        `${API_BASE_URL}/history/recharge`,
        config
      );
      return response.data;
    } catch (error) {
      console.error('Failed to get recharge history:', error);
      throw error;
    }
  },

  // 3. 检查支付状态
  checkPaymentStatus: async (paymentIntentId: string): Promise<PaymentStatusResponse> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.get(
        `${API_BASE_URL}/payments/payment-status/${paymentIntentId}`,
        config
      );
      return response.data;
    } catch (error) {
      console.error('Failed to check payment status:', error);
      throw error;
    }
  },

  // 4. 获取支付方式列表
  getPaymentMethods: async (): Promise<PaymentMethodsResponse> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.get(
        `${API_BASE_URL}/payments/payment-methods`,
        config
      );
      return response.data;
    } catch (error) {
      console.error('Failed to get payment methods:', error);
      throw error;
    }
  },

  // 5. 添加支付方式
  addPaymentMethod: async (request: AddPaymentMethodRequest): Promise<void> => {
    try {
      const config = await getAuthConfig();
      await axios.post(
        `${API_BASE_URL}/payments/payment-methods`,
        request,
        config
      );
    } catch (error) {
      console.error('Failed to add payment method:', error);
      throw error;
    }
  },

  // 6. 删除支付方式
  deletePaymentMethod: async (paymentMethodId: string): Promise<void> => {
    try {
      const config = await getAuthConfig();
      await axios.delete(
        `${API_BASE_URL}/payments/payment-methods/${paymentMethodId}`,
        config
      );
    } catch (error) {
      console.error('Failed to delete payment method:', error);
      throw error;
    }
  },

  // 6b. 设置默认支付方式
  setDefaultPaymentMethod: async (paymentMethodId: string): Promise<void> => {
    try {
      const config = await getAuthConfig();
      await axios.post(
        `${API_BASE_URL}/payments/payment-methods/${paymentMethodId}/set-default`,
        {},
        config
      );
    } catch (error) {
      console.error('Failed to set default payment method:', error);
      throw error;
    }
  },

  // 7. 检查用户推荐状态（从用户API）
  checkUserReferStatus: async (): Promise<UserReferStatus> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.get(
        `${API_BASE_URL}/accounts/check-refer`,
        config
      );
      return response.data;
    } catch (error) {
      console.error('Failed to check user referral status:', error);
      throw error;
    }
  },

  // 8. 创建市场交易支付意向
  createMarketplacePaymentIntent: async (request: {
    listing_id: string;
    buyer_address_id: string;
    offer_id?: string;
  }): Promise<PaymentIntent> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.post(
        `${API_BASE_URL}/payments/payment/create-marketplace-intent`,
        request,
        config
      );
      return response.data;
    } catch (error) {
      console.error('Failed to create marketplace payment intent:', error);
      throw error;
    }
  },

  // 9. 创建Stripe Connect账户
  createStripeConnect: async (): Promise<StripeConnectCreateResponse> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.post(
        `${API_BASE_URL}/payments/stripe/connect`,
        {},
        config
      );
      return response.data;
    } catch (error) {
      console.error('Failed to create Stripe Connect account:', error);
      throw error;
    }
  },

  // 10. 检查Stripe Connect状态
  getStripeConnectStatus: async (): Promise<StripeConnectStatusResponse> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.get(
        `${API_BASE_URL}/payments/stripe/status`,
        config
      );
      return response.data;
    } catch (error) {
      console.error('Failed to check Stripe Connect status:', error);
      throw error;
    }
  },

  // 11. 获取Stripe仪表板链接
  getStripeDashboardLink: async (): Promise<StripeDashboardResponse> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.get(
        `${API_BASE_URL}/payments/stripe/dashboard`,
        config
      );
      return response.data;
    } catch (error) {
      console.error('Failed to get Stripe Dashboard link:', error);
      throw error;
    }
  },

  // 12. 检查税务状态
  getTaxStatus: async (): Promise<TaxStatusResponse> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.get(
        `${API_BASE_URL}/payments/tax/status`,
        config
      );
      return response.data;
    } catch (error) {
      console.error('Failed to check tax consent status:', error);
      throw error;
    }
  },

  // 13. 更新税务同意
  updateTaxConsent: async (consent: boolean): Promise<TaxConsentResponse> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.post(
        `${API_BASE_URL}/payments/tax/consent`,
        { consent },
        config
      );
      return response.data;
    } catch (error) {
      console.error('Failed to update tax consent:', error);
      throw error;
    }
  },

  // 14. 获取分页充值记录
  getRechargeHistoryPaginated: async (
    userId: string,
    page: number = 1,
    pageSize: number = 10
  ): Promise<PaginatedRechargeHistoryResponse> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.get(
        `${API_BASE_URL}/history/recharge?page=${page}&page_size=${pageSize}`,
        config
      );
      return response.data;
    } catch (error) {
      console.error('Failed to get paginated recharge history:', error);
      throw error;
    }
  },
};

// Stripe 相关工具函数
export const stripeUtils = {
  // 初始化 Stripe
  initializeStripe: () => {
    if (typeof window !== 'undefined' && window.Stripe) {
      const publishableKey = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY;
      if (!publishableKey) {
        console.error('NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY is not set');
        return null;
      }
      return window.Stripe(publishableKey);
    }
    return null;
  },

  // 美元转美分
  dollarsToCents: (dollars: number): number => {
    return Math.round(dollars * 100);
  },

  // 美分转美元
  centsToDollars: (cents: number): number => {
    return cents / 100;
  },

  // 格式化金额显示
  formatAmount: (cents: number): string => {
    return `$${(cents / 100).toFixed(2)}`;
  },
};

// 错误处理工具
export const handlePaymentError = (error: unknown): string => {
  if (error && typeof error === 'object' && 'response' in error) {
    const axiosError = error as { response: { status: number; data?: { detail?: string } } };
    switch (axiosError.response.status) {
      case 400:
        return `参数错误：${axiosError.response.data?.detail || '请检查输入参数'}`;
      case 404:
        return '请求的资源不存在';
      case 403:
        return '您没有权限执行此操作';
      case 500:
        return '服务器错误，请稍后重试';
      default:
        return '发生未知错误';
    }
  }
  if (error instanceof Error) {
    return error.message;
  }
  return 'Network error, please check network connection';
};

export default paymentApi;