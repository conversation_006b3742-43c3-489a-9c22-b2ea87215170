/**
 * Efficient Celebration Effects Manager
 * Handles card celebration animations with optimal performance
 */

type CelebrationColor = 'orange' | 'red';

interface CelebrationState {
  isActive: boolean;
  color: CelebrationColor;
  timeoutId?: NodeJS.Timeout;
}

class CelebrationManager {
  private activeCards = new Map<string, CelebrationState>();
  private observer?: IntersectionObserver;

  constructor() {
    this.initializeIntersectionObserver();
    this.setupVisibilityChangeHandler();
  }

  /**
   * Initialize Intersection Observer to pause animations when cards are off-screen
   */
  private initializeIntersectionObserver() {
    if (typeof window === 'undefined' || !window.IntersectionObserver) return;

    this.observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const cardId = entry.target.getAttribute('data-card-id');
          if (!cardId) return;

          const celebration = this.activeCards.get(cardId);
          if (!celebration) return;

          if (entry.isIntersecting) {
            // Resume animation when card is visible
            this.resumeCelebration(entry.target as HTMLElement, celebration.color);
          } else {
            // Pause animation when card is off-screen
            this.pauseCelebration(entry.target as HTMLElement);
          }
        });
      },
      {
        rootMargin: '50px', // Start observing slightly before card enters viewport
        threshold: 0.1
      }
    );
  }

  /**
   * Setup page visibility change handler to pause animations when tab is inactive
   */
  private setupVisibilityChangeHandler() {
    if (typeof document === 'undefined') return;

    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.pauseAllCelebrations();
      } else {
        this.resumeAllCelebrations();
      }
    });
  }

  /**
   * Start celebration effect for a card
   */
  startCelebration(cardElement: HTMLElement, color: CelebrationColor, duration = 3000) {
    const cardId = this.generateCardId(cardElement);
    
    // Stop any existing celebration
    this.stopCelebration(cardElement);
    
    // Add efficient CSS classes
    cardElement.classList.add('celebrate', color);
    cardElement.setAttribute('data-card-id', cardId);
    
    // Track the celebration
    const celebration: CelebrationState = {
      isActive: true,
      color,
      timeoutId: setTimeout(() => {
        this.stopCelebration(cardElement);
      }, duration)
    };
    
    this.activeCards.set(cardId, celebration);
    
    // Start observing for visibility
    this.observer?.observe(cardElement);
  }

  /**
   * Start burst effect when card is revealed
   */
  triggerBurst(cardElement: HTMLElement) {
    if (!cardElement.classList.contains('celebrate')) return;
    
    cardElement.classList.add('revealed');
    
    // Auto-remove burst class after animation
    setTimeout(() => {
      cardElement.classList.remove('revealed');
    }, 600);
  }

  /**
   * Stop celebration effect for a card
   */
  stopCelebration(cardElement: HTMLElement) {
    const cardId = cardElement.getAttribute('data-card-id');
    if (!cardId) return;

    const celebration = this.activeCards.get(cardId);
    if (celebration?.timeoutId) {
      clearTimeout(celebration.timeoutId);
    }

    // Remove CSS classes
    cardElement.classList.remove('celebrate', 'orange', 'red', 'revealed');
    cardElement.removeAttribute('data-card-id');
    
    // Stop observing
    this.observer?.unobserve(cardElement);
    
    // Remove from tracking
    this.activeCards.delete(cardId);
  }

  /**
   * Pause celebration animation (keep classes but pause)
   */
  private pauseCelebration(cardElement: HTMLElement) {
    cardElement.style.animationPlayState = 'paused';
  }

  /**
   * Resume celebration animation
   */
  private resumeCelebration(cardElement: HTMLElement, color: CelebrationColor) {
    cardElement.style.animationPlayState = 'running';
  }

  /**
   * Pause all active celebrations
   */
  private pauseAllCelebrations() {
    this.activeCards.forEach((celebration, cardId) => {
      const element = document.querySelector(`[data-card-id="${cardId}"]`) as HTMLElement;
      if (element) {
        this.pauseCelebration(element);
      }
    });
  }

  /**
   * Resume all active celebrations
   */
  private resumeAllCelebrations() {
    this.activeCards.forEach((celebration, cardId) => {
      const element = document.querySelector(`[data-card-id="${cardId}"]`) as HTMLElement;
      if (element) {
        this.resumeCelebration(element, celebration.color);
      }
    });
  }

  /**
   * Clean up all celebrations and observers
   */
  cleanup() {
    // Clear all timeouts
    this.activeCards.forEach((celebration) => {
      if (celebration.timeoutId) {
        clearTimeout(celebration.timeoutId);
      }
    });
    
    // Clear tracking
    this.activeCards.clear();
    
    // Disconnect observer
    this.observer?.disconnect();
  }

  /**
   * Generate unique ID for card element
   */
  private generateCardId(cardElement: HTMLElement): string {
    // Try to use existing card index or create one
    const existingId = cardElement.getAttribute('data-card-index');
    if (existingId) return `card-${existingId}`;
    
    // Fallback to position-based ID
    return `card-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get performance metrics
   */
  getMetrics() {
    return {
      activeCelebrations: this.activeCards.size,
      isObserverActive: !!this.observer,
      celebrationDetails: Array.from(this.activeCards.entries()).map(([id, state]) => ({
        id,
        color: state.color,
        isActive: state.isActive
      }))
    };
  }
}

// Singleton instance
export const celebrationManager = new CelebrationManager();

/**
 * Hook for React components to use celebration effects
 */
export function useCelebrationEffects() {
  return {
    startCelebration: celebrationManager.startCelebration.bind(celebrationManager),
    triggerBurst: celebrationManager.triggerBurst.bind(celebrationManager),
    stopCelebration: celebrationManager.stopCelebration.bind(celebrationManager),
    cleanup: celebrationManager.cleanup.bind(celebrationManager),
    getMetrics: celebrationManager.getMetrics.bind(celebrationManager)
  };
}

/**
 * Utility to determine celebration color from card rarity
 */
export function getCelebrationColor(rarity: string): CelebrationColor | null {
  switch (rarity?.toLowerCase()) {
    case 'legendary':
    case 'ultra-rare':
      return 'red';
    case 'epic':
    case 'super-rare':
      return 'orange';
    default:
      return null;
  }
}

/**
 * Utility to determine celebration color from card color
 */
export function getCelebrationColorFromCardColor(color: string): CelebrationColor | null {
  switch (color?.toLowerCase()) {
    case 'red':
      return 'red';
    case 'orange':
      return 'orange';
    default:
      return null;
  }
}
