import { storage } from './firebase'
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage'

/**
 * 上传图片到Firebase Storage
 * @param file 要上传的文件
 * @param path 存储路径
 * @returns 上传后的文件URL
 */
export const uploadImage = async (file: File, path: string): Promise<string> => {
  try {
    // 创建存储引用
    const storageRef = ref(storage, path)
    
    // 上传文件
    const snapshot = await uploadBytes(storageRef, file)
    
    // 获取下载URL
    const downloadURL = await getDownloadURL(snapshot.ref)
    
    return downloadURL
  } catch (error) {
    console.error('图片上传失败:', error)
    throw new Error('图片上传失败')
  }
}

/**
 * 上传用户头像
 * @param userId 用户ID
 * @param file 头像文件
 * @returns 头像URL
 */
export const uploadAvatar = async (userId: string, file: File): Promise<string> => {
  // 生成唯一的文件名
  const fileExtension = file.name.split('.').pop()
  const fileName = `${userId}_${Date.now()}.${fileExtension}`
  const path = `avatars/${fileName}`
  
  return await uploadImage(file, path)
}