import { auth } from './firebase';
import { user<PERSON>pi } from './userApi';
import { useAuthStore } from '@/store/authStore';
import { onAuthStateChanged, reload } from 'firebase/auth';

/**
 * Initialize authentication state listener
 * Call this function on app startup to listen for user auth state changes
 * and load user info when user is logged in
 */
export const initAuthListener = () => {
  const { setUid, setUserInfo, setToken, setAuthInitialized } = useAuthStore.getState();
  
  // Track if this is the initial auth check
  let isInitialCheck = true;

  return onAuthStateChanged(auth, async (user) => {
    if (user) {
      // User is logged in, set user ID
      setUid(user.uid);
      
      // Reload user to get latest verification status
      try {
        await reload(user);
      } catch (error) {
        console.error('Failed to reload user:', error);
      }
      
      // Get and cache token - use force refresh only if not initial check
      try {
        const token = await user.getIdToken(!isInitialCheck);
        setToken(token, Date.now() + 3600000); // Cache for 1 hour
        console.log('Token cached successfully');
      } catch (error) {
        console.error('Failed to get token:', error);
      }
      
      // Check if user is using email/password authentication
      const isEmailPasswordUser = user.providerData.some(
        provider => provider.providerId === 'password'
      );
      
      // For email/password users, check if email is verified
      // We still set the UID so we can detect them in login modal
      // but we don't load user info if not verified
      if (isEmailPasswordUser && !user.emailVerified) {
        console.log('Email not verified, not loading user info');
        // Don't load user info, but keep UID so login modal can detect unverified users
        return;
      }
      
      // Check if it's Google login (or other third-party login)
      const isGoogleLogin = user.providerData.some(provider => 
        provider.providerId === 'google.com' || 
        provider.providerId === 'facebook.com' || 
        provider.providerId === 'twitter.com'
      );
      
      // Always fetch fresh user info on initial check (page refresh/reload)
      // or if it's Google login, or if no userInfo exists, or if user is new_account
      const currentUserInfo = useAuthStore.getState().userInfo;
      const shouldFetchUserInfo = isInitialCheck || 
                                  isGoogleLogin || 
                                  !currentUserInfo || 
                                  currentUserInfo.new_account === true;
      
      if (user.uid && shouldFetchUserInfo) {
        try {
          console.log('Fetching fresh user info:', user.uid, {
            isInitialCheck,
            isGoogleLogin,
            hasUserInfo: !!currentUserInfo,
            isNewAccount: currentUserInfo?.new_account
          });
          const userInfo = await userApi.getUserInfo();
          setUserInfo(userInfo);
        } catch (error) {
          console.error('Failed to get user info:', error);
        }
      }
    } else {
      // User has logged out - only clear state if not initial check
      // This prevents clearing state before Firebase has restored the session
      if (!isInitialCheck) {
        useAuthStore.getState().logout();
      }
    }
    
    // Mark auth as initialized after first check
    if (isInitialCheck) {
      setAuthInitialized(true);
    }
    
    // After first check, mark as not initial
    isInitialCheck = false;
  });
};

/**
 * Check if user is authenticated
 * @returns {boolean} Whether user is authenticated
 */
export const isAuthenticated = () => {
  const { uid } = useAuthStore.getState();
  if (!uid) return false;
  
  const user = auth.currentUser;
  if (!user) return false;
  
  // Check if user is using email/password authentication
  const isEmailPasswordUser = user.providerData.some(
    provider => provider.providerId === 'password'
  );
  
  // For email/password users, require email verification
  // For social login users (Google, etc.), they're considered verified
  if (isEmailPasswordUser && !user.emailVerified) {
    return false;
  }
  
  return true;
};

/**
 * Check if user's email is verified
 * @returns {boolean} Whether user's email is verified
 */
export const isEmailVerified = () => {
  const user = auth.currentUser;
  return user?.emailVerified || false;
};

/**
 * Check if user is authenticated and email verified
 * @returns {boolean} Whether user is fully authenticated (logged in + email verified)
 */
export const isFullyAuthenticated = () => {
  return isAuthenticated() && isEmailVerified();
};

/**
 * Get current user ID
 * @returns {string|null} User ID or null
 */
export const getCurrentUserId = () => {
  return useAuthStore.getState().uid;
};

/**
 * Get current user info
 * @returns {object|null} User info or null
 */
export const getCurrentUserInfo = () => {
  return useAuthStore.getState().userInfo;
};

/**
 * Get authentication headers
 * @returns {object} Object containing Authorization header
 */
export const getAuthHeaders = async () => {
  const { token, isTokenValid, setToken, clearToken } = useAuthStore.getState();
  
  // If there's a valid cached token, use it directly
  if (token && isTokenValid()) {
    return {
      'Authorization': `Bearer ${token}`
    };
  }
  
  // Cached token is invalid, get new token from Firebase
  const user = auth.currentUser;
  if (user) {
    try {
      const newToken = await user.getIdToken();
      // Cache new token with 1 hour expiry
      setToken(newToken, Date.now() + 3600000);
      return {
        'Authorization': `Bearer ${newToken}`
      };
    } catch (error) {
      console.error('Failed to get auth token:', error);
      clearToken();
      return {};
    }
  }
  
  // User not logged in - this is normal, no need to warn or clear token
  return {};
};
