/**
 * Shared image optimization utility using Cloudflare Image Resizing
 * 
 * This function dynamically extracts the domain from any image URL and
 * applies Cloudflare Image Resizing parameters for better performance.
 * Works with dynamic subdomains like pack.zapull.fun, card.zapull.fun, etc.
 */

export interface ImageOptimizationOptions {
  quality?: number
  width?: number
  height?: number
}

/**
 * Optimizes images using Cloudflare Image Resizing with dynamic domain detection
 * 
 * @param url - The original image URL
 * @param options - Optimization options (quality, width, height)
 * @returns Optimized image URL
 */
export const optimizeImage = (url: string, options: ImageOptimizationOptions = {}): string => {
  if (!url) return url
  
  try {
    // Extract the domain from the image URL
    const urlObj = new URL(url)
    const domain = urlObj.origin
    const path = urlObj.pathname + urlObj.search
    
    const { quality = 80, width, height } = options
    const params = [`format=auto`, `quality=${quality}`]
    
    if (width) params.push(`width=${width}`)
    if (height) params.push(`height=${height}`)
    
    return `${domain}/cdn-cgi/image/${params.join(',')}${path}`
  } catch {
    // If URL parsing fails, return original URL
    return url
  }
}
