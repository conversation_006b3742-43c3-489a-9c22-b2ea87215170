import { 
  checkDrawAchievements, 
  checkFusionAchievements, 
  checkBuyDealAchievements,
  checkSellDealAchievements,
  checkLevelAchievements,
  checkAchievements,
  AchievementCheckResponse 
} from './achievementCheckApi';
import toast from 'react-hot-toast';

export interface Achievement {
  id: string;
  name: string;
  description: string;
  reward: Array<{
    type: string;
    amount?: number;
    emblemId?: string;
    url?: string;
  }>;
}

export type AchievementType = 'draw' | 'fusion' | 'buy_deal' | 'sell_deal' | 'level' | 'custom';

// Store for modal state
let achievementModalCallback: ((achievements: Achievement[]) => void) | null = null;

/**
 * Set the callback function to show the achievement modal
 * This should be called once in the app initialization
 */
export const setAchievementModalCallback = (callback: (achievements: Achievement[]) => void) => {
  achievementModalCallback = callback;
};

/**
 * Global achievement checker that can be called from anywhere
 * @param type - The type of achievement to check
 * @param customTypes - Custom achievement types for 'custom' type
 * @returns The awarded achievements
 */
export const checkAchievementsGlobal = async (
  type: AchievementType, 
  customTypes?: Record<string, boolean>
): Promise<Achievement[]> => {
  try {
    let result: AchievementCheckResponse;

    switch (type) {
      case 'draw':
        result = await checkDrawAchievements();
        break;
      case 'fusion':
        result = await checkFusionAchievements();
        break;
      case 'buy_deal':
        result = await checkBuyDealAchievements();
        break;
      case 'sell_deal':
        result = await checkSellDealAchievements();
        break;
      case 'level':
        result = await checkLevelAchievements();
        break;
      case 'custom':
        if (!customTypes) {
          throw new Error('Custom types are required for custom achievement check');
        }
        result = await checkAchievements(customTypes);
        break;
      default:
        throw new Error(`Unknown achievement type: ${type}`);
    }

    if (result.awarded && result.awarded.length > 0) {
      console.log(`New ${type} achievements unlocked:`, result.awarded);
      
      // Show toast notification
      const achievementCount = result.awarded.length;
      const totalPoints = result.awarded.reduce((sum, ach) => {
        const points = ach.reward
          .filter(r => r.type === 'point')
          .reduce((total, r) => total + (r.amount || 0), 0);
        return sum + points;
      }, 0);
      
      toast.success(
        `🏆 Unlocked ${achievementCount} new achievement(s)!${totalPoints > 0 ? ` +${totalPoints} points` : ''}`,
        {
          duration: 4000,
          position: 'top-center',
          style: {
            background: '#FEF3C7',
            color: '#92400E',
            fontWeight: 'bold',
            fontSize: '16px',
            padding: '16px',
            borderRadius: '12px',
            border: '2px solid #FCD34D',
          },
        }
      );
      
      // Show modal if callback is set
      if (achievementModalCallback) {
        achievementModalCallback(result.awarded);
      }
      
      return result.awarded;
    }

    return [];
  } catch (error: any) {
    // Suppress noisy auth errors when Firebase auth state isn't ready
    if (error?.message && /User not authenticated/i.test(error.message)) {
      return []
    }
    console.error(`Failed to check ${type} achievements:`, error);
    return [];
  }
};

// Convenience functions for specific achievement types
export const checkDrawAchievementsGlobal = () => checkAchievementsGlobal('draw');
export const checkFusionAchievementsGlobal = () => checkAchievementsGlobal('fusion');
export const checkBuyDealAchievementsGlobal = () => checkAchievementsGlobal('buy_deal');
export const checkSellDealAchievementsGlobal = () => checkAchievementsGlobal('sell_deal');
export const checkLevelAchievementsGlobal = () => checkAchievementsGlobal('level');