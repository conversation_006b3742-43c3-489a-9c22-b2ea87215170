/**
 * Draw Animation Manager
 * Optimizes draw animations for better performance and smoother experience
 */

interface CardAnimationState {
  visible: boolean
  animationComplete: boolean
  flipping: boolean
  revealed: boolean
}

interface DrawAnimationOptions {
  skipAnimation: boolean
  drawCount: number
  onProgress?: (phase: string, progress: number) => void
}

export class DrawAnimationManager {
  private animationFrameId: number | null = null
  private timeouts: Set<NodeJS.Timeout> = new Set()
  private isAnimating = false

  /**
   * Run optimized card reveal animation
   */
  async runCardRevealAnimation(
    options: DrawAnimationOptions,
    callbacks: {
      setCardVisible: (fn: (prev: boolean[]) => boolean[]) => void
      setCardAnimationComplete: (fn: (prev: boolean[]) => boolean[]) => void
      setShowCards: (fn: (prev: boolean[]) => boolean[]) => void
      setIsDrawing: (value: boolean) => void
    }
  ): Promise<void> {
    const { skipAnimation, drawCount } = options
    const { setCardVisible, setCardAnimationComplete, setShowCards, setIsDrawing } = callbacks

    this.cleanup() // Clean up any previous animations
    this.isAnimating = true

    if (skipAnimation) {
      // Optimized skip animation - batch state updates
      this.runSkipAnimation(drawCount, callbacks)
    } else {
      // Standard animation with RAF optimization
      this.runStandardAnimation(drawCount, callbacks)
    }
  }

  /**
   * Optimized skip animation using RAF batching
   */
  private runSkipAnimation(
    drawCount: number, 
    callbacks: {
      setCardVisible: (fn: (prev: boolean[]) => boolean[]) => void
      setCardAnimationComplete: (fn: (prev: boolean[]) => boolean[]) => void
      setShowCards: (fn: (prev: boolean[]) => boolean[]) => void
      setIsDrawing: (value: boolean) => void
    }
  ) {
    const { setCardVisible, setCardAnimationComplete, setShowCards, setIsDrawing } = callbacks

    // Batch initial state setup
    requestAnimationFrame(() => {
      // Set all cards as visible immediately for skip mode
      setCardVisible(() => new Array(drawCount).fill(true))
      
      // Set animation complete after brief delay for smooth transition
      const timeout = setTimeout(() => {
        setCardAnimationComplete(() => new Array(drawCount).fill(true))
        setIsDrawing(false)
      }, 100)
      
      this.timeouts.add(timeout)
    })
  }

  /**
   * Standard animation with RAF optimization
   */
  private runStandardAnimation(
    drawCount: number,
    callbacks: {
      setCardVisible: (fn: (prev: boolean[]) => boolean[]) => void
      setCardAnimationComplete: (fn: (prev: boolean[]) => boolean[]) => void
      setShowCards: (fn: (prev: boolean[]) => boolean[]) => void
      setIsDrawing: (value: boolean) => void
    }
  ) {
    const { setCardVisible, setCardAnimationComplete, setIsDrawing } = callbacks

    let currentCard = 0
    const cardInterval = 200 // ms between card appearances
    const animationDuration = 800 // ms for each card animation

    const animateNextCard = () => {
      if (currentCard >= drawCount || !this.isAnimating) {
        setIsDrawing(false)
        return
      }

      const cardIndex = currentCard
      
      // Show card with optimized timing
      setCardVisible(prev => {
        const newVisible = [...prev]
        newVisible[cardIndex] = true
        return newVisible
      })

      // Set animation complete after duration
      const timeout = setTimeout(() => {
        if (this.isAnimating) {
          setCardAnimationComplete(prev => {
            const newComplete = [...prev]
            newComplete[cardIndex] = true
            return newComplete
          })
        }
      }, animationDuration)

      this.timeouts.add(timeout)
      currentCard++

      // Schedule next card
      if (currentCard < drawCount) {
        const nextTimeout = setTimeout(() => {
          this.animationFrameId = requestAnimationFrame(animateNextCard)
        }, cardInterval)
        this.timeouts.add(nextTimeout)
      } else {
        // All cards done
        setIsDrawing(false)
      }
    }

    // Start the animation chain
    this.animationFrameId = requestAnimationFrame(animateNextCard)
  }

  /**
   * Optimized card flip with RAF timing
   */
  runCardFlip(
    index: number,
    callbacks: {
      setCardFlipping: (fn: (prev: boolean[]) => boolean[]) => void
      setShowCards: (fn: (prev: boolean[]) => boolean[]) => void
      onFlipComplete?: () => void
    }
  ): void {
    const { setCardFlipping, setShowCards, onFlipComplete } = callbacks

    // Set flipping state immediately
    setCardFlipping(prev => {
      const newFlipping = [...prev]
      newFlipping[index] = true
      return newFlipping
    })

    // Use RAF for smooth flip animation
    requestAnimationFrame(() => {
      requestAnimationFrame(() => {
        setShowCards(prev => {
          const newShow = [...prev]
          newShow[index] = true
          return newShow
        })

        // Reset flipping state after animation
        const timeout = setTimeout(() => {
          setCardFlipping(prev => {
            const next = [...prev]
            next[index] = false
            return next
          })
          onFlipComplete?.()
        }, 400) // Match CSS animation duration

        this.timeouts.add(timeout)
      })
    })
  }

  /**
   * Preload resources for faster animations
   */
  async preloadResources(): Promise<void> {
    // Preload video if not already cached
    try {
      const { useVideoCache } = await import('@/hooks/useVideoCache')
      // This will be handled by the existing video cache system
    } catch (error) {
      console.warn('Failed to preload video resources:', error)
    }
  }

  /**
   * Check if currently animating
   */
  get isRunning(): boolean {
    return this.isAnimating
  }

  /**
   * Clean up all animations and timers
   */
  cleanup(): void {
    this.isAnimating = false

    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId)
      this.animationFrameId = null
    }

    // Clear all timeouts
    this.timeouts.forEach(timeout => clearTimeout(timeout))
    this.timeouts.clear()
  }
}

// Singleton instance
export const drawAnimationManager = new DrawAnimationManager()

/**
 * React hook for using the draw animation manager
 */
export function useDrawAnimation() {
  return {
    runCardRevealAnimation: drawAnimationManager.runCardRevealAnimation.bind(drawAnimationManager),
    runCardFlip: drawAnimationManager.runCardFlip.bind(drawAnimationManager),
    preloadResources: drawAnimationManager.preloadResources.bind(drawAnimationManager),
    cleanup: drawAnimationManager.cleanup.bind(drawAnimationManager),
    isRunning: drawAnimationManager.isRunning
  }
}
