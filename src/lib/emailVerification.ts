import { sendEmailVerification, reload } from 'firebase/auth'
import { auth } from './firebase'

/**
 * Send email verification to the current user
 * @returns {Promise<boolean>} Success status
 */
export const sendVerificationEmail = async (): Promise<boolean> => {
  try {
    const user = auth.currentUser
    if (!user) {
      console.error('No user is currently logged in')
      return false
    }

    if (user.emailVerified) {
      console.log('User email is already verified')
      return true
    }

    await sendEmailVerification(user)
    console.log('Verification email sent successfully')
    return true
  } catch (error: any) {
    console.error('Failed to send verification email:', error)
    
    // Handle specific error cases
    if (error.code === 'auth/too-many-requests') {
      throw new Error('Too many requests. Please wait before requesting another verification email.')
    } else if (error.code === 'auth/user-disabled') {
      throw new Error('Your account has been disabled. Please contact support.')
    } else if (error.code === 'auth/user-not-found') {
      throw new Error('User not found. Please sign in again.')
    } else {
      throw new Error('Failed to send verification email. Please try again.')
    }
  }
}

/**
 * Check if current user's email is verified
 * Reloads user data from Firebase to get latest status
 * @returns {Promise<boolean>} Verification status
 */
export const checkEmailVerification = async (): Promise<boolean> => {
  try {
    const user = auth.currentUser
    if (!user) {
      console.error('No user is currently logged in')
      return false
    }

    // Reload user to get latest verification status
    await reload(user)
    return user.emailVerified
  } catch (error) {
    console.error('Failed to check email verification status:', error)
    return false
  }
}

/**
 * Wait for email verification with polling
 * Checks verification status every few seconds
 * @param {number} timeoutSeconds - Maximum time to wait (default: 300 seconds / 5 minutes)
 * @param {number} pollIntervalSeconds - How often to check (default: 3 seconds)
 * @returns {Promise<boolean>} Whether email was verified within timeout
 */
export const waitForEmailVerification = async (
  timeoutSeconds: number = 300,
  pollIntervalSeconds: number = 3
): Promise<boolean> => {
  const startTime = Date.now()
  const timeoutMs = timeoutSeconds * 1000
  const pollIntervalMs = pollIntervalSeconds * 1000

  return new Promise((resolve) => {
    const checkVerification = async () => {
      try {
        const isVerified = await checkEmailVerification()
        
        if (isVerified) {
          resolve(true)
          return
        }

        // Check if timeout reached
        if (Date.now() - startTime >= timeoutMs) {
          resolve(false)
          return
        }

        // Schedule next check
        setTimeout(checkVerification, pollIntervalMs)
      } catch (error) {
        console.error('Error during verification polling:', error)
        resolve(false)
      }
    }

    checkVerification()
  })
}

/**
 * Get current user's email
 * @returns {string|null} User's email or null
 */
export const getCurrentUserEmail = (): string | null => {
  const user = auth.currentUser
  return user?.email || null
}

/**
 * Check if current user is using email/password authentication
 * @returns {boolean} Whether user is using email/password auth
 */
export const isEmailPasswordUser = (): boolean => {
  const user = auth.currentUser
  if (!user) return false

  return user.providerData.some(provider => provider.providerId === 'password')
}

/**
 * Check if email verification is required for current user
 * Email verification is only required for email/password users
 * @returns {boolean} Whether email verification is required
 */
export const isEmailVerificationRequired = (): boolean => {
  const user = auth.currentUser
  if (!user) return false

  // Only require email verification for email/password authentication
  // Social logins (Google, Facebook, etc.) are considered already verified
  return isEmailPasswordUser()
}
