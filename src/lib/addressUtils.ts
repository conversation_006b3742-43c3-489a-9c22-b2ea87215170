import { Country, State, City } from 'country-state-city';
import { ICountry, IState, ICity } from 'country-state-city/lib/interface';

// 获取所有国家列表
export const getCountries = (): ICountry[] => {
  return Country.getAllCountries();
};

// 根据国家代码获取州/省列表
export const getStatesByCountry = (countryCode: string): IState[] => {
  return State.getStatesOfCountry(countryCode);
};

// 根据国家代码和州/省代码获取城市列表
export const getCitiesByState = (countryCode: string, stateCode: string): ICity[] => {
  return City.getCitiesOfState(countryCode, stateCode);
};

// 根据国家代码获取国家名称
export const getCountryNameByCode = (countryCode: string): string => {
  const country = Country.getCountryByCode(countryCode);
  return country ? country.name : '';
};

// 根据国家代码和州/省代码获取州/省名称
export const getStateNameByCode = (countryCode: string, stateCode: string): string => {
  const state = State.getStateByCodeAndCountry(stateCode, countryCode);
  return state ? state.name : '';
};

// 根据国家代码获取电话代码
export const getCountryPhoneCode = (countryCode: string): string => {
  const country = Country.getCountryByCode(countryCode);
  return country ? country.phonecode : '';
};