import axios from 'axios';
import { getAuthHeaders } from './authUtils';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://user-backend-351785787544.us-central1.run.app/users/api/v1';

// 获取认证配置
const getAuthConfig = async () => {
  const headers = await getAuthHeaders();
  return { headers };
};

// 成就数据接口
export interface Achievement {
  id: string;
  name: string;
  description: string;
  emblemId?: string;
  emblemUrl?: string;
  icon_url?: string;  // Legacy field for compatibility
  condition?: {
    type: string;
    target: number;
  };
  reward?: Array<{
    amount?: number;
    type: string;
    emblemId?: string;
    url?: string;
  }>;
  awardedAt?: string;
  achieved?: boolean;  // Whether the user has achieved this
  is_completed?: boolean;  // Legacy field for compatibility
  progress?: number;  // Current progress towards achievement
}

// 分页数据接口
export interface Pagination {
  total_items: number;
  items_per_page: number;
  current_page: number;
  total_pages: number;
}

// 成就响应接口
export interface AchievementsResponse {
  achievements: Achievement[];
  pagination: Pagination;
}

// 分组成就响应接口
export interface GroupedAchievementsResponse {
  groups: { [key: string]: Achievement[] };
  total: number;
  page: number;
  size: number;
}

// 用户等级响应接口
export interface UserLevelResponse {
  user_id: string;
  previous_level: number;
  current_level: number;
  total_drawn: number;
}

// 成就API参数接口
export interface AchievementParams {
  user_id?: string;
  page?: number;
  per_page?: number;
  sort_by?: string;
  sort_order?: string;
  search_query?: string;
}

// 成就API服务
export const achievementApi = {
  // 获取用户成就高亮
  getUserAchievementHighlights: async (params?: AchievementParams): Promise<AchievementsResponse> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.get(`${API_BASE_URL}/achievements/achievement-highlights`, {
        params: {
          page: params?.page || 1,
          per_page: params?.per_page || 20,
          sort_by: params?.sort_by || 'awardedAt',
          sort_order: params?.sort_order || 'desc',
          search_query: params?.search_query || ''
        },
        ...config
      });
      
      // 规范化成就数据：如果缺少 emblemUrl，则从 reward 中提取
      const rawAchievements = response.data.achievements || [];
      const normalized = rawAchievements.map((a: any) => {
        const emblemFromReward = Array.isArray(a.reward)
          ? a.reward.find((r: any) => r?.type === 'emblem' && (r.url || r.emblemUrl))
          : undefined;
        return {
          ...a,
          emblemUrl: a.emblemUrl || emblemFromReward?.url || emblemFromReward?.emblemUrl || a.icon_url || undefined,
          emblemId: a.emblemId || emblemFromReward?.emblemId || undefined,
        };
      });
      // 确保返回完整的AchievementsResponse对象
      return {
        achievements: normalized,
        pagination: response.data.pagination || {
          total_items: 0,
          items_per_page: params?.per_page || 20,
          current_page: params?.page || 1,
          total_pages: 1
        }
      };
    } catch (error) {
      console.error('获取用户成就高亮失败:', error);
      throw error;
    }
  },

  // 获取用户所有成就
  getUserAchievements: async (params?: AchievementParams): Promise<AchievementsResponse> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.get(`${API_BASE_URL}/achievements/achievements`, {
        params: {
          page: params?.page || 1,
          per_page: params?.per_page || 20,
          sort_by: params?.sort_by || 'awardedAt',
          sort_order: params?.sort_order || 'desc',
          search_query: params?.search_query || ''
        },
        ...config
      });
      
      const rawAchievements = response.data.achievements || [];
      const normalized = rawAchievements.map((a: any) => {
        const emblemFromReward = Array.isArray(a.reward)
          ? a.reward.find((r: any) => r?.type === 'emblem' && (r.url || r.emblemUrl))
          : undefined;
        return {
          ...a,
          emblemUrl: a.emblemUrl || emblemFromReward?.url || emblemFromReward?.emblemUrl || a.icon_url || undefined,
          emblemId: a.emblemId || emblemFromReward?.emblemId || undefined,
        };
      });
      return {
        achievements: normalized,
        pagination: response.data.pagination || {
          total_items: 0,
          items_per_page: params?.per_page || 20,
          current_page: params?.page || 1,
          total_pages: 1
        }
      };
    } catch (error) {
      console.error('获取用户成就失败:', error);
      throw error;
    }
  },

  // 获取所有成就
  getAllAchievements: async (params?: AchievementParams): Promise<AchievementsResponse> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.get(`${API_BASE_URL}/achievements/all-achievements`, {
        params: {
          page: params?.page || 1,
          per_page: params?.per_page || 20,
          sort_by: params?.sort_by || 'name',
          sort_order: params?.sort_order || 'asc',
          search_query: params?.search_query || ''
        },
        ...config
      });
      
      const rawAchievements = response.data.achievements || [];
      const normalized = rawAchievements.map((a: any) => {
        const emblemFromReward = Array.isArray(a.reward)
          ? a.reward.find((r: any) => r?.type === 'emblem' && (r.url || r.emblemUrl))
          : undefined;
        return {
          ...a,
          emblemUrl: a.emblemUrl || emblemFromReward?.url || emblemFromReward?.emblemUrl || a.icon_url || undefined,
          emblemId: a.emblemId || emblemFromReward?.emblemId || undefined,
        };
      });
      return {
        achievements: normalized,
        pagination: response.data.pagination || {
          total_items: 0,
          items_per_page: params?.per_page || 20,
          current_page: params?.page || 1,
          total_pages: 1
        }
      };
    } catch (error) {
      console.error('获取所有成就失败:', error);
      throw error;
    }
  },

  // 获取分组成就（按类型分组并按rank排序）
  getGroupedAchievements: async (params?: AchievementParams): Promise<GroupedAchievementsResponse> => {
    try {
      // Try to get auth config, but continue even if not authenticated
      let config = {};
      try {
        config = await getAuthConfig();
      } catch (authError) {
        // Continue without auth headers - backend should allow viewing achievements without login
        console.log('Fetching achievements without authentication');
      }
      
      // Now using user backend's grouped achievements endpoint
      const response = await axios.get(`${API_BASE_URL}/achievements/all-achievements-grouped`, config);
      
      // The user backend returns the grouped data directly
      return {
        groups: response.data || {},
        total: Object.values(response.data as { [key: string]: any[] }).reduce((acc, group) => acc + group.length, 0),
        page: params?.page || 1,
        size: params?.per_page || 100
      };
    } catch (error) {
      console.error('获取分组成就失败:', error);
      throw error;
    }
  },

  // 绑定成就高亮
  bindAchievementHighlight: async (achievementId: string): Promise<Achievement> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.post(`${API_BASE_URL}/achievements/${achievementId}/highlights`, {}, config);
      return response.data;
    } catch (error) {
      console.error('绑定成就高亮失败:', error);
      throw error;
    }
  },

  // 删除成就高亮
  deleteAchievementHighlight: async (achievementId: string): Promise<void> => {
    try {
      const config = await getAuthConfig();
      await axios.delete(`${API_BASE_URL}/achievements/${achievementId}/highlights`, config);
    } catch (error) {
      console.error('删除成就高亮失败:', error);
      throw error;
    }
  },

  // 获取成就详情
  getAchievementDetail: async (achievementId: string): Promise<Achievement> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.get(`${API_BASE_URL}/achievements/achievements/${achievementId}`, config);
      return response.data;
    } catch (error) {
      console.error('获取成就详情失败:', error);
      throw error;
    }
  },

  // 计算用户等级
  calculateUserLevel: async (): Promise<UserLevelResponse> => {
    try {
      const config = await getAuthConfig();
      const response = await axios.post(`${API_BASE_URL}/achievements/calculate-level`, {}, config);
      return response.data;
    } catch (error) {
      console.error('计算用户等级失败:', error);
      throw error;
    }
  }
};

// 导出命名的API对象
export { achievementApi as default };