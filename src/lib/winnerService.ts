import { collection, getDocs, query, orderBy, limit, Timestamp, DocumentData } from 'firebase/firestore';
import { db } from './firebase';

/**
 * Safely get a field from Firestore document data with fallbacks
 */
function getFieldSafely<T>(data: DocumentData, primaryField: string, alternateFields: string[] = [], defaultValue: T): T {
  if (data[primaryField] !== undefined && data[primaryField] !== null) {
    return data[primaryField] as T;
  }
  for (const field of alternateFields) {
    if (data[field] !== undefined && data[field] !== null) {
      return data[field] as T;
    }
  }
  return defaultValue;
}

export interface CardInfo {
  name: string;
  point_worth: number;
  rarity: number;
  id: string;
  image_url: string;
}

export interface Winner {
  id: string;
  username: string;
  amount: number;
  imageUrl: string;
  timestamp: Date;
  itemName: CardInfo | string;
  card?: CardInfo;
  userId?: string;
  pack_id?: string;
  pack_collection_id?: string;
}

/**
 * Backup: fetch recent winners with a basic query (no ordering)
 */
export const getRecentWinnersBackup = async (count: number = 6): Promise<Winner[]> => {
  try {
    console.log('[winners] Using backup query...');
    const winnersQuery = query(collection(db, 'top_hits'));
    const querySnapshot = await getDocs(winnersQuery);

    console.log('[winners] Backup snapshot size:', querySnapshot.size);

    const winners: Winner[] = [];
    querySnapshot.forEach((doc) => {
      const data = doc.data();

      const timestampValue = getFieldSafely<any>(data, 'timestamp', ['created_at'], null);
      let dateValue: Date;
      if (timestampValue instanceof Timestamp) {
        dateValue = timestampValue.toDate();
      } else if (timestampValue && typeof timestampValue.toDate === 'function') {
        dateValue = timestampValue.toDate();
      } else if (timestampValue && timestampValue._seconds) {
        dateValue = new Date(timestampValue._seconds * 1000);
      } else {
        dateValue = new Date();
      }

      const cardData = getFieldSafely<CardInfo | null>(data, 'card', [], null);
      const itemNameData = getFieldSafely<string | null>(data, 'itemName', ['item_name'], null);

      winners.push({
        id: doc.id,
        username: getFieldSafely<string>(data, 'display_name', ['user_name'], 'Unknown User'),
        amount: cardData && typeof cardData === 'object' && typeof (cardData as any).point_worth === 'number'
          ? (cardData as any).point_worth
          : getFieldSafely<number>(data, 'price', [], 0),
        imageUrl: cardData && typeof cardData === 'object' && (cardData as any).image_url
          ? (cardData as any).image_url
          : getFieldSafely<string>(data, 'image_url', [], ''),
        timestamp: dateValue,
        itemName: itemNameData || (cardData && typeof cardData === 'object' ? cardData : 'Unknown Item'),
        card: cardData && typeof cardData === 'object' ? cardData : undefined,
        userId: getFieldSafely<string>(data, 'user_id', [], ''),
        pack_id: getFieldSafely<string>(data, 'pack_id', [], ''),
        pack_collection_id: getFieldSafely<string>(data, 'pack_collection_id', [], '')
      });
    });

    return winners
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, count);
  } catch (error) {
    console.error('[winners] Backup query failed:', error);
    return [];
  }
};

/**
 * Fetch recent winners from Firestore
 */
export const getRecentWinners = async (count: number = 6): Promise<Winner[]> => {
  try {
    console.log('[winners] Fetching recent winners...');
    let winnersQuery;
    try {
      winnersQuery = query(
        collection(db, 'top_hits'),
        orderBy('timestamp', 'desc'),
        limit(count)
      );
    } catch (sortError) {
      console.warn('[winners] orderBy timestamp failed, trying created_at:', sortError);
      try {
        winnersQuery = query(
          collection(db, 'top_hits'),
          orderBy('created_at', 'desc'),
          limit(count)
        );
      } catch (sortError2) {
        console.warn('[winners] orderBy created_at failed, falling back to basic query:', sortError2);
        winnersQuery = query(collection(db, 'top_hits'), limit(count));
      }
    }

    const querySnapshot = await getDocs(winnersQuery);
    console.log('[winners] Snapshot size:', querySnapshot.size, 'empty:', querySnapshot.empty);

    const winners: Winner[] = [];
    querySnapshot.forEach((doc) => {
      const data = doc.data();

      const timestampValue = getFieldSafely<any>(data, 'timestamp', ['created_at'], null);
      let dateValue: Date;
      if (timestampValue instanceof Timestamp) {
        dateValue = timestampValue.toDate();
      } else if (timestampValue && typeof timestampValue.toDate === 'function') {
        dateValue = timestampValue.toDate();
      } else if (timestampValue && timestampValue._seconds) {
        dateValue = new Date(timestampValue._seconds * 1000);
      } else {
        dateValue = new Date();
      }

      const cardData = getFieldSafely<CardInfo | null>(data, 'card', [], null);
      const itemNameData = getFieldSafely<string | null>(data, 'itemName', ['item_name'], null);

      const winner: Winner = {
        id: doc.id,
        username: getFieldSafely<string>(data, 'display_name', ['user_name'], 'Unknown User'),
        amount: cardData && typeof cardData === 'object' && typeof (cardData as any).point_worth === 'number'
          ? (cardData as any).point_worth
          : getFieldSafely<number>(data, 'price', [], 0),
        imageUrl: cardData && typeof cardData === 'object' && (cardData as any).image_url
          ? (cardData as any).image_url
          : getFieldSafely<string>(data, 'image_url', [], ''),
        timestamp: dateValue,
        itemName: itemNameData || (cardData && typeof cardData === 'object' ? cardData : 'Unknown Item'),
        card: cardData && typeof cardData === 'object' ? cardData : undefined,
        userId: getFieldSafely<string>(data, 'user_id', [], ''),
        pack_id: getFieldSafely<string>(data, 'pack_id', [], ''),
        pack_collection_id: getFieldSafely<string>(data, 'pack_collection_id', [], '')
      };

      winners.push(winner);
    });

    return winners;
  } catch (error) {
    console.error('[winners] Failed to fetch recent winners:', error);
    console.log('[winners] Falling back to backup query...');
    return getRecentWinnersBackup(count);
  }
};
