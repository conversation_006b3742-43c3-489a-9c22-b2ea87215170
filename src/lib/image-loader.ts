import { optimizeImage } from './optimizeImage';

// R2 domains that should not use Next.js optimization
const R2_DOMAINS = [
  'avator.zapull.fun',
  'achievement.zapull.fun',
  'pack.zapull.fun',
  'card.zapull.fun',
  'draw.zapull.fun',
  'avator-dev.zapull.fun',
  'achievement-dev.zapull.fun',
  'pack-dev.zapull.fun',
  'card-dev.zapull.fun'
];

/**
 * Get optimized image props for avatar images.
 * Uses Cloudflare Image Resizing for R2-hosted avatars and Google avatars.
 * Applies consistent avatar sizing (80x80) with quality optimization.
 */
export function getImageProps(src: string, size?: number) {
  if (!src) {
    return {
      src: '/avatars/default.svg',
      unoptimized: true,
    };
  }

  try {
    // Check if this is an R2-hosted avatar or Google avatar
    const url = new URL(src);
    const isR2Avatar = R2_DOMAINS.includes(url.hostname);
    const isGoogleAvatar = url.hostname.includes('googleusercontent.com');
    
    if (isR2Avatar || isGoogleAvatar) {
      // Use Cloudflare Image Resizing for optimal avatar delivery
      // Default avatar size of 80x80 with 85% quality, same as pack images
      const avatarSize = size || 80;
      const optimizedSrc = optimizeImage(src, {
        width: avatarSize,
        height: avatarSize,
        quality: 85
      });
      return {
        src: optimizedSrc,
        unoptimized: true, // Let Cloudflare handle optimization
      };
    }
  } catch (error) {
    // If URL parsing fails, treat as local image
  }

  // For local images (like default avatars), use original src without optimization
  return {
    src,
    unoptimized: true,
  };
}
