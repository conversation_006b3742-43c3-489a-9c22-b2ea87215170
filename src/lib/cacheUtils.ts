// Enhanced cache utility for better performance and error handling

interface CacheEntry<T> {
  data: T
  timestamp: number
  ttl: number
  version?: string
}

class EnhancedCache {
  private cache = new Map<string, CacheEntry<any>>()
  private maxSize = 100 // Maximum number of cache entries
  
  get<T>(key: string): T | null {
    const entry = this.cache.get(key)
    if (!entry) return null
    
    const now = Date.now()
    if (now - entry.timestamp > entry.ttl) {
      this.cache.delete(key)
      return null
    }
    
    return entry.data as T
  }
  
  set<T>(key: string, data: T, ttl: number = 5 * 60 * 1000, version?: string): void {
    // Enforce cache size limit
    if (this.cache.size >= this.maxSize) {
      // Remove oldest entries
      const entries = Array.from(this.cache.entries())
        .sort(([, a], [, b]) => a.timestamp - b.timestamp)
      
      for (let i = 0; i < Math.floor(this.maxSize * 0.2); i++) {
        this.cache.delete(entries[i][0])
      }
    }
    
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
      version
    })
  }
  
  delete(key: string): boolean {
    return this.cache.delete(key)
  }
  
  clear(): void {
    this.cache.clear()
  }
  
  // Get cache stats for debugging
  getStats() {
    const now = Date.now()
    const entries = Array.from(this.cache.values())
    const expired = entries.filter(entry => now - entry.timestamp > entry.ttl).length
    
    return {
      total: this.cache.size,
      expired,
      valid: this.cache.size - expired
    }
  }
  
  // Cleanup expired entries
  cleanup(): number {
    const now = Date.now()
    const keysToDelete: string[] = []
    
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        keysToDelete.push(key)
      }
    }
    
    keysToDelete.forEach(key => this.cache.delete(key))
    return keysToDelete.length
  }
}

// Global cache instance
export const globalCache = new EnhancedCache()

// Wrapper functions for easier usage
export const getCachedData = <T>(key: string): T | null => {
  return globalCache.get<T>(key)
}

export const setCachedData = <T>(key: string, data: T, ttl?: number, version?: string): void => {
  globalCache.set(key, data, ttl, version)
}

export const deleteCachedData = (key: string): boolean => {
  return globalCache.delete(key)
}

// Clean up cache periodically
if (typeof window !== 'undefined') {
  // Clean up expired entries every 5 minutes
  setInterval(() => {
    const cleaned = globalCache.cleanup()
    if (cleaned > 0) {
      console.debug(`[Cache] Cleaned up ${cleaned} expired entries`)
    }
  }, 5 * 60 * 1000)
}
