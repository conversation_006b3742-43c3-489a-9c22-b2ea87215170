/**
 * Audio Manager for optimized mobile audio playback
 * Uses Web Audio API for low-latency playback with HTMLAudioElement fallback
 * Handles audio preloading, buffering, and mobile-specific optimizations
 */

interface AudioPool {
  [key: string]: HTMLAudioElement[]
}

interface AudioBufferCache {
  [key: string]: AudioBuffer
}

class AudioManager {
  private audioPool: AudioPool = {}
  private audioBuffers: { [key: string]: AudioBuffer } = {}
  private loadedSounds = new Set<string>()
  private maxPoolSize = 3 // Keep 3 instances of each sound for overlapping playback
  private isMobile = false
  private audioContext: AudioContext | null = null
  private useWebAudioAPI = false
  private isInitialized = false

  constructor() {
    // Detect mobile browsers - only in browser environment
    this.isMobile = typeof window !== 'undefined' && typeof navigator !== 'undefined' 
      ? /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
      : false
    
    // Initialize Web Audio API if available
    this.initializeWebAudio()
  }

  /**
   * Initialize Web Audio API
   */
  private initializeWebAudio() {
    if (typeof window === 'undefined') return
    
    try {
      // Create AudioContext with optimal settings for low latency
      const AudioContextClass = window.AudioContext || (window as any).webkitAudioContext
      if (AudioContextClass) {
        this.audioContext = new AudioContextClass({
          latencyHint: 'interactive', // Optimize for low latency
          sampleRate: 44100 // Standard sample rate
        })
        this.useWebAudioAPI = true
        console.log('Web Audio API initialized for low-latency playback')
      }
    } catch (error) {
      console.warn('Web Audio API not available, falling back to HTMLAudioElement:', error)
      this.useWebAudioAPI = false
    }
  }

  /**
   * Ensure audio context is ready for playback
   */
  private async ensureAudioContextReady(): Promise<void> {
    if (!this.audioContext || !this.useWebAudioAPI) return
    
    if (this.audioContext.state === 'suspended') {
      try {
        await this.audioContext.resume()
        console.log('Audio context resumed')
      } catch (error) {
        console.warn('Failed to resume audio context:', error)
      }
    }
  }

  /**
   * Load audio buffer for Web Audio API
   */
  private async loadAudioBuffer(soundPath: string): Promise<AudioBuffer | null> {
    if (!this.audioContext || !this.useWebAudioAPI) return null
    
    try {
      const response = await fetch(soundPath)
      const arrayBuffer = await response.arrayBuffer()
      const audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer)
      return audioBuffer
    } catch (error) {
      console.warn(`Failed to load audio buffer for ${soundPath}:`, error)
      return null
    }
  }

  /**
   * Play sound using Web Audio API
   */
  private async playWithWebAudio(soundPath: string, volume: number): Promise<void> {
    if (!this.audioContext || !this.useWebAudioAPI) return
    
    const audioBuffer = this.audioBuffers[soundPath]
    if (!audioBuffer) {
      console.warn(`Audio buffer not found for ${soundPath}`)
      return
    }
    
    try {
      await this.ensureAudioContextReady()
      
      // Create audio nodes
      const source = this.audioContext.createBufferSource()
      const gainNode = this.audioContext.createGain()
      
      // Configure nodes
      source.buffer = audioBuffer
      gainNode.gain.value = volume
      
      // Connect nodes
      source.connect(gainNode)
      gainNode.connect(this.audioContext.destination)
      
      // Play immediately
      source.start(0)
      
    } catch (error) {
      console.warn(`Web Audio API playback failed for ${soundPath}:`, error)
      // Fallback to HTMLAudioElement
      this.playWithHTMLAudio(soundPath, volume)
    }
  }

  /**
   * Play sound using HTMLAudioElement (fallback)
   */
  private async playWithHTMLAudio(soundPath: string, volume: number): Promise<void> {
    const pool = this.audioPool[soundPath]
    if (!pool || pool.length === 0) {
      return this.playDirectly(soundPath, volume)
    }

    // Find an available audio instance
    let audioInstance = pool.find(audio => audio.paused || audio.ended)
    
    // If no available instance, use the first one
    if (!audioInstance) {
      audioInstance = pool[0]
    }

    try {
      // Reset and configure the audio
      audioInstance.currentTime = 0
      audioInstance.volume = volume

      // For mobile, we need to handle the play promise
      const playPromise = audioInstance.play()
      
      if (playPromise !== undefined) {
        playPromise.catch(error => {
          console.warn(`HTML Audio play failed for ${soundPath}:`, error.message)
        })
      }
    } catch (error) {
      console.warn(`Failed to play HTML audio ${soundPath}:`, error)
    }
  }

  /**
   * Preload audio files and create audio pool
   */
  async preloadSounds(soundPaths: string[]) {
    // Preload both HTML audio and Web Audio API buffers
    const htmlPreloadPromises = soundPaths.map(path => this.preloadSound(path))
    const webAudioPromises = this.useWebAudioAPI 
      ? soundPaths.map(path => this.preloadWebAudioBuffer(path))
      : []
    
    await Promise.allSettled([...htmlPreloadPromises, ...webAudioPromises])
  }

  /**
   * Preload Web Audio API buffer
   */
  private async preloadWebAudioBuffer(soundPath: string): Promise<void> {
    if (!this.useWebAudioAPI || this.audioBuffers[soundPath]) return
    
    try {
      const buffer = await this.loadAudioBuffer(soundPath)
      if (buffer) {
        this.audioBuffers[soundPath] = buffer
        console.log(`Web Audio buffer loaded for ${soundPath}`)
      }
    } catch (error) {
      console.warn(`Failed to preload Web Audio buffer for ${soundPath}:`, error)
    }
  }

  /**
   * Preload a single sound and create pool
   */
  private async preloadSound(soundPath: string): Promise<void> {
    if (this.loadedSounds.has(soundPath)) {
      return
    }

    return new Promise((resolve) => {
      const audio = new Audio(soundPath)
      audio.preload = 'auto'
      
      const onLoad = () => {
        // Create audio pool
        this.audioPool[soundPath] = []
        for (let i = 0; i < this.maxPoolSize; i++) {
          const pooledAudio = new Audio(soundPath)
          pooledAudio.preload = 'auto'
          pooledAudio.volume = 0.8 // Slightly lower volume for mobile
          this.audioPool[soundPath].push(pooledAudio)
        }
        
        this.loadedSounds.add(soundPath)
        audio.removeEventListener('canplaythrough', onLoad)
        audio.removeEventListener('error', onError)
        resolve()
      }

      const onError = () => {
        console.warn(`Failed to preload audio: ${soundPath}`)
        audio.removeEventListener('canplaythrough', onLoad)
        audio.removeEventListener('error', onError)
        resolve() // Don't reject, just continue
      }

      audio.addEventListener('canplaythrough', onLoad)
      audio.addEventListener('error', onError)
      
      // Trigger loading
      audio.load()
    })
  }

  /**
   * Play sound with optimizations for mobile
   */
  async playSound(soundPath: string, options: { volume?: number, interrupt?: boolean } = {}) {
    const { volume = 0.8, interrupt = false } = options

    // Try Web Audio API first for lowest latency
    if (this.useWebAudioAPI && this.audioBuffers[soundPath]) {
      await this.playWithWebAudio(soundPath, volume)
      return
    }

    // Fallback to HTML Audio Element
    await this.playWithHTMLAudio(soundPath, volume)
  }

  /**
   * Initialize audio context on first user interaction
   */
  async initializeOnUserInteraction(): Promise<void> {
    if (!this.isInitialized && this.audioContext) {
      try {
        await this.ensureAudioContextReady()
        this.isInitialized = true
        console.log('Audio system initialized on user interaction')
      } catch (error) {
        console.warn('Failed to initialize audio system:', error)
      }
    }
  }

  /**
   * Direct audio play fallback
   */
  private async playDirectly(soundPath: string, volume: number) {
    try {
      const audio = new Audio(soundPath)
      audio.volume = volume
      const playPromise = audio.play()
      
      if (playPromise !== undefined) {
        playPromise.catch(error => {
          console.warn(`Direct audio play failed for ${soundPath}:`, error.message)
        })
      }
    } catch (error) {
      console.warn(`Failed to create/play audio ${soundPath}:`, error)
    }
  }

  /**
   * Stop all currently playing audio of a specific type
   */
  stopSound(soundPath: string) {
    const pool = this.audioPool[soundPath]
    if (pool) {
      pool.forEach(audio => {
        if (!audio.paused) {
          audio.pause()
          audio.currentTime = 0
        }
      })
    }
  }

  /**
   * Stop all audio
   */
  stopAllSounds() {
    Object.values(this.audioPool).forEach(pool => {
      pool.forEach(audio => {
        if (!audio.paused) {
          audio.pause()
          audio.currentTime = 0
        }
      })
    })
  }

  /**
   * Check if mobile device
   */
  get isMobileDevice(): boolean {
    return this.isMobile
  }
}

// Create singleton instance
export const audioManager = new AudioManager()

// Sound paths used in the app
export const SOUND_PATHS = {
  DRAW: '/draw/draw-sound.wav',
  FLIP: '/draw/flip-sound.wav',
  RARITY_1: '/draw/rarity-1.wav',
  RARITY_2: '/draw/rarity-2.wav',
  RARITY_3: '/draw/rarity-3.wav',
} as const

// Initialize audio manager with all sounds
export const initializeAudioManager = async () => {
  const soundPaths = Object.values(SOUND_PATHS)
  await audioManager.preloadSounds(soundPaths)
}
