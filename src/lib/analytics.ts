/**
 * Analytics and event tracking utilities
 * Handles tracking of user interactions and navigation events
 */

export interface NavigationEvent {
  event: 'navigation_click'
  destination: string
  source: 'desktop' | 'mobile'
  user_id?: string
  timestamp: number
}

export interface FusionNavigationEvent extends NavigationEvent {
  event: 'fusion_navigation_click'
  destination: 'fusion' | 'synthesis'
}

/**
 * Track navigation events, particularly before fusion
 * @param event The event to track
 */
export function trackNavigationEvent(event: NavigationEvent | FusionNavigationEvent): void {
  try {
    // Log to console for development
    console.log('📊 Navigation Event:', event)
    
    // In a production environment, you would send this to your analytics service
    // Examples:
    // - Google Analytics 4
    // - Mixpanel
    // - PostHog
    // - Custom analytics endpoint
    
    // For now, we'll store in localStorage as a simple implementation
    const existingEvents = JSON.parse(localStorage.getItem('navigation_events') || '[]')
    existingEvents.push(event)
    
    // Keep only the last 100 events to prevent localStorage bloat
    if (existingEvents.length > 100) {
      existingEvents.splice(0, existingEvents.length - 100)
    }
    
    localStorage.setItem('navigation_events', JSON.stringify(existingEvents))
    
    // You could also send to a custom endpoint:
    // sendToAnalyticsEndpoint(event)
    
  } catch (error) {
    console.error('Failed to track navigation event:', error)
  }
}

/**
 * Track fusion/synthesis navigation specifically
 * @param destination The destination page
 * @param source Whether clicked from desktop or mobile navigation
 * @param userId Optional user ID for authenticated users
 */
export function trackFusionNavigation(
  destination: 'fusion' | 'synthesis', 
  source: 'desktop' | 'mobile', 
  userId?: string
): void {
  const event: FusionNavigationEvent = {
    event: 'fusion_navigation_click',
    destination,
    source,
    user_id: userId,
    timestamp: Date.now()
  }
  
  trackNavigationEvent(event)
}

/**
 * Get all tracked navigation events (for debugging or analytics review)
 */
export function getNavigationEvents(): NavigationEvent[] {
  try {
    return JSON.parse(localStorage.getItem('navigation_events') || '[]')
  } catch {
    return []
  }
}

/**
 * Clear all tracked navigation events
 */
export function clearNavigationEvents(): void {
  localStorage.removeItem('navigation_events')
}

// Example function for sending to custom analytics endpoint
// async function sendToAnalyticsEndpoint(event: NavigationEvent): Promise<void> {
//   try {
//     await fetch('/api/analytics/track', {
//       method: 'POST',
//       headers: {
//         'Content-Type': 'application/json'
//       },
//       body: JSON.stringify(event)
//     })
//   } catch (error) {
//     console.error('Failed to send analytics event:', error)
//   }
// }
