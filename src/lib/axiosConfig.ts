import axios from 'axios';
import { auth } from '@/lib/firebase';
import { useAuthStore } from '@/store/authStore';
import { onAuthStateChanged } from 'firebase/auth';

// Track if we're currently refreshing the token
let isRefreshing = false;
let failedQueue: Array<{
  resolve: (token: string) => void;
  reject: (error: any) => void;
}> = [];

const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach((prom) => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve(token!);
    }
  });
  
  failedQueue = [];
};

// Add a response interceptor to handle 401 errors globally
axios.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;
    
    if (error.response?.status === 401 && !originalRequest._retry) {
      if (isRefreshing) {
        // If we're already refreshing, queue this request
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        }).then((token) => {
          originalRequest.headers['Authorization'] = `Bearer ${token}`;
          return axios(originalRequest);
        }).catch((err) => {
          return Promise.reject(err);
        });
      }
      
      originalRequest._retry = true;
      isRefreshing = true;
      
      try {
        // Wait a bit for auth to be restored if page just refreshed
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // Get the current user
        const currentUser = auth.currentUser;
        
        if (!currentUser) {
          // Double-check by waiting for auth state
          await new Promise((resolve) => {
            const unsubscribe = onAuthStateChanged(auth, (user) => {
              unsubscribe();
              resolve(user);
            });
            // Timeout after 2 seconds
            setTimeout(() => {
              unsubscribe();
              resolve(null);
            }, 2000);
          });
          
          const finalUser = auth.currentUser;
          if (!finalUser) {
            // No user logged in, redirect to login
            useAuthStore.getState().clearToken();
            if (typeof window !== 'undefined') {
              const currentUrl = window.location.pathname + window.location.search;
              window.location.href = `/auth/reauth?redirect=${encodeURIComponent(currentUrl)}`;
            }
            return Promise.reject(error);
          }
        }
        
        // Get fresh token - only force refresh if really needed
        const currentUserFinal = auth.currentUser || currentUser;
        const newToken = await currentUserFinal.getIdToken(true);
        console.log('Token refreshed successfully');
        
        // Store the new token
        useAuthStore.getState().setToken(newToken, Date.now() + 3600000); // 1 hour expiry
        
        // Update the authorization header
        originalRequest.headers['Authorization'] = `Bearer ${newToken}`;
        
        // Process the queue with the new token
        processQueue(null, newToken);
        
        // Retry the original request
        return axios(originalRequest);
      } catch (refreshError) {
        console.error('Token refresh failed:', refreshError);
        processQueue(refreshError, null);
        useAuthStore.getState().clearToken();
        
        // Only redirect to login if refresh fails
        if (typeof window !== 'undefined') {
          const currentUrl = window.location.pathname + window.location.search;
          window.location.href = `/auth/reauth?redirect=${encodeURIComponent(currentUrl)}`;
        }
        
        return Promise.reject(refreshError);
      } finally {
        isRefreshing = false;
      }
    }
    
    return Promise.reject(error);
  }
);

// Export configured axios instance
export default axios;