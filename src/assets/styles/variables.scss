// 主题颜色变量
$primary-color: #409EFF;
$success-color: #67C23A;
$warning-color: #E6A23C;
$danger-color: #F56C6C;
$info-color: #909399;

// 文字颜色
$text-primary: #303133;
$text-regular: #606266;
$text-secondary: #909399;
$text-placeholder: #C0C4CC;

// 边框颜色
$border-base: #DCDFE6;
$border-light: #E4E7ED;
$border-lighter: #EBEEF5;
$border-extra-light: #F2F6FC;

// 背景颜色
$background-base: #F5F7FA;
$background-white: #FFFFFF;

// 布局相关
$header-height: 60px;
$sidebar-width: 210px;
$sidebar-collapsed-width: 64px;

// 响应式断点
$screen-xs: 480px;
$screen-sm: 768px;
$screen-md: 992px;
$screen-lg: 1200px;
$screen-xl: 1600px;

// 间距
$spacing-mini: 4px;
$spacing-small: 8px;
$spacing-base: 16px;
$spacing-large: 24px;
$spacing-xl: 32px;

// 字体大小
$font-size-mini: 12px;
$font-size-small: 13px;
$font-size-base: 14px;
$font-size-medium: 16px;
$font-size-large: 18px;
$font-size-xl: 20px;

// 圆角
$border-radius-small: 2px;
$border-radius-base: 4px;
$border-radius-large: 8px;
$border-radius-circle: 50%;

// 阴影
$box-shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
$box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
$box-shadow-dark: 0 2px 16px 0 rgba(0, 0, 0, 0.2);

// 过渡
$transition-base: all 0.3s ease-in-out;
$transition-fast: all 0.2s ease-in-out;
$transition-slow: all 0.5s ease-in-out;