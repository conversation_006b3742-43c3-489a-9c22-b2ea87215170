#!/usr/bin/env python3
"""Test script to debug user_backend packs service"""

import requests
import json

# Test the user_backend endpoint
collection_id = "one_piece"
url = f"https://user-backend-351785787544.us-central1.run.app/users/api/v1/packs/collection/{collection_id}"

params = {
    "page": 1,
    "per_page": 10,
    "sort_by": "popularity",
    "sort_order": "desc",
    "search_by_cards": False
}

print(f"Testing user_backend packs endpoint...")
print(f"URL: {url}")
print(f"Params: {json.dumps(params, indent=2)}")
print("-" * 50)

try:
    response = requests.get(url, params=params)
    print(f"Status Code: {response.status_code}")
    print(f"Response Headers: {dict(response.headers)}")
    print(f"Response Body:")
    print(json.dumps(response.json(), indent=2))
    
    # If empty, let's try the backend service for comparison
    if response.status_code == 200 and len(response.json().get("packs", [])) == 0:
        print("\n" + "=" * 50)
        print("No packs found in user_backend. Testing backend service...")
        
        backend_url = f"https://backend-351785787544.us-central1.run.app/api/v1/packs/collection/{collection_id}"
        backend_response = requests.get(backend_url, params=params)
        
        print(f"Backend Status Code: {backend_response.status_code}")
        print(f"Backend Response:")
        print(json.dumps(backend_response.json(), indent=2))
        
except Exception as e:
    print(f"Error: {e}")