# Dependencies
node_modules
.npm

# Build output
.next
out

# Environment files
.env
.env.local
.env.production.local
.env.development.local
.env.test.local

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Version control
.git
.gitignore

# Editor directories and files
.idea
.vscode
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db

# Test coverage
coverage
.nyc_output

# Documentation
README.md
*.md

# Development files
deploy-test.js
scripts/