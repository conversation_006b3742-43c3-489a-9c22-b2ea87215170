# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build output (we'll build in the container)
dist
dist-*
build

# Development tools
.vscode
.idea
*.swp
*.swo
*~

# Log files
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directories
coverage
.nyc_output

# Environment files (production values should be injected via Docker)
.env
.env.local
.env.development.local
.env.production.local

# Temporary files
.tmp
.temp

# System files
.DS_Store
Thumbs.db

# Git
.git
.gitignore

# Documentation
README.md
*.md

# Archives
*.rar
*.zip
*.tar.gz

# AI documentation
ai-study/

# Docker files (we don't need these in the build context)
Dockerfile
docker-compose.yml
.dockerignore

# Cloud build files
cloudbuild.yaml
build-and-deploy.sh

# Test files
**/*.test.js
**/*.spec.js
__tests__

# Cache directories
.vite
.cache