#!/usr/bin/env python3
"""
Test script for the improved achievement upload schema.

This script demonstrates the correct structure for uploading achievements
and tests the validation logic.
"""

import asyncio
import httpx
import json
import base64
from typing import Dict, Any

# Example achievement data with proper structure
VALID_ACHIEVEMENT_EXAMPLES = [
    {
        "name": "Level Master",
        "description": "Reach level 10",
        "condition": {
            "type": "level_reached",
            "target": 10
        },
        "reward": [
            {
                "type": "point",
                "amount": 1000
            }
        ],
        "rarity": "common",
        "rank": 1
    },
    {
        "name": "Fusion Expert",
        "description": "Complete 50 fusions",
        "condition": {
            "type": "fusion_reached",
            "target": 50
        },
        "reward": [
            {
                "type": "point",
                "amount": 2500
            },
            {
                "type": "emblem",
                "image": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
            }
        ],
        "rarity": "rare",
        "rank": 2
    },
    {
        "name": "Rare Card Collector",
        "description": "Draw 100 rare cards",
        "condition": {
            "type": "draw_by_rarity",
            "target": 100,
            "rarity": 3
        },
        "reward": [
            {
                "type": "point",
                "amount": 5000
            }
        ],
        "rarity": "epic",
        "rank": 3
    },
    {
        "name": "No Reward Achievement",
        "description": "An achievement with no rewards",
        "condition": {
            "type": "level_reached",
            "target": 1
        },
        "reward": None,
        "rarity": "common"
    },
    {
        "name": "Empty Reward Achievement",
        "description": "An achievement with empty reward list",
        "condition": {
            "type": "level_reached",
            "target": 2
        },
        "reward": [],
        "rarity": "common"
    },
    {
        "name": "Only Emblem Achievement",
        "description": "An achievement with only emblem reward",
        "condition": {
            "type": "fusion_reached",
            "target": 5
        },
        "reward": [
            {
                "type": "emblem",
                "image": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
            }
        ],
        "rarity": "rare"
    },
    {
        "name": "Missing Reward Field",
        "description": "An achievement without reward field at all",
        "condition": {
            "type": "level_reached",
            "target": 3
        },
        "rarity": "common"
    }
]

# Examples of invalid achievement data for testing validation
INVALID_ACHIEVEMENT_EXAMPLES = [
    {
        "name": "Invalid Condition",
        "description": "Missing target in condition",
        "condition": {
            "type": "level_reached"
            # Missing "target" field
        },
        "reward": [
            {
                "type": "point",
                "amount": 100
            }
        ]
    },
    {
        "name": "Invalid Reward",
        "description": "Invalid reward type",
        "condition": {
            "type": "level_reached",
            "target": 5
        },
        "reward": [
            {
                "type": "invalid_type",  # Invalid reward type
                "amount": 100
            }
        ]
    },
    {
        "name": "Missing Rarity for Draw By Rarity",
        "description": "draw_by_rarity condition without rarity field",
        "condition": {
            "type": "draw_by_rarity",
            "target": 10
            # Missing "rarity" field for draw_by_rarity type
        },
        "reward": [
            {
                "type": "point",
                "amount": 100
            }
        ]
    }
]

async def test_upload_achievement(achievement_data: Dict[str, Any], should_succeed: bool = True):
    """Test uploading an achievement"""
    
    print(f"\n{'='*60}")
    print(f"Testing: {achievement_data['name']}")
    print(f"Expected to {'succeed' if should_succeed else 'fail'}")
    print(f"{'='*60}")
    
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                'http://localhost:8000/achievements/upload',
                json=achievement_data,
                headers={'Content-Type': 'application/json'}
            )
            
            print(f"Response status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ Success!")
                print(f"Achievement ID: {result.get('id')}")
                print(f"Name: {result.get('name')}")
                print(f"Condition: {result.get('condition')}")
                print(f"Rewards: {result.get('reward')}")
                
                if should_succeed:
                    print("✅ Test passed - achievement uploaded successfully")
                else:
                    print("❌ Test failed - expected failure but got success")
                    
                return True
            else:
                error_detail = response.json().get('detail', response.text)
                print(f"❌ Error: {error_detail}")
                
                if not should_succeed:
                    print("✅ Test passed - expected failure occurred")
                else:
                    print("❌ Test failed - expected success but got failure")
                    
                return False
                
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return False

async def test_schema_validation():
    """Test the new achievement schema validation"""
    
    print("🧪 Testing Achievement Upload Schema Validation")
    print("=" * 80)
    
    # Test valid achievements
    print("\n📋 Testing Valid Achievements:")
    valid_results = []
    for achievement in VALID_ACHIEVEMENT_EXAMPLES:
        result = await test_upload_achievement(achievement, should_succeed=True)
        valid_results.append(result)
    
    # Test invalid achievements
    print("\n📋 Testing Invalid Achievements:")
    invalid_results = []
    for achievement in INVALID_ACHIEVEMENT_EXAMPLES:
        result = await test_upload_achievement(achievement, should_succeed=False)
        invalid_results.append(result)
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 Test Results Summary:")
    print(f"Valid achievements: {sum(valid_results)}/{len(valid_results)} passed")
    print(f"Invalid achievements: {sum(invalid_results)}/{len(invalid_results)} correctly rejected")
    
    total_passed = sum(valid_results) + sum(invalid_results)
    total_tests = len(valid_results) + len(invalid_results)
    
    if total_passed == total_tests:
        print("✅ All tests passed! Schema validation is working correctly.")
    else:
        print(f"❌ {total_tests - total_passed} tests failed.")
    
    return total_passed == total_tests

async def show_swagger_example():
    """Show what the Swagger documentation should now display"""
    
    print("\n" + "=" * 80)
    print("📖 Swagger Documentation Example")
    print("=" * 80)
    
    example_schema = {
        "name": "string (required)",
        "description": "string (required)",
        "condition": {
            "type": "string (required, e.g., 'level_reached', 'fusion_reached', 'draw_by_rarity')",
            "target": "integer (required, minimum: 1)",
            "rarity": "integer (optional, required for 'draw_by_rarity' type)"
        },
        "reward": "array (optional, can be null, empty, or contain point/emblem rewards)",
        "reward_examples": [
            "null (no rewards)",
            "[] (empty rewards)",
            "[{\"type\": \"point\", \"amount\": 1000}] (point reward only)",
            "[{\"type\": \"emblem\", \"image\": \"base64...\"}] (emblem reward only)",
            "[{\"type\": \"point\", \"amount\": 1000}, {\"type\": \"emblem\", \"image\": \"base64...\"}] (both)"
        ],
        "rarity": "string (optional, e.g., 'common', 'rare', 'epic', 'legendary')",
        "rank": "integer (optional, minimum: 1)"
    }
    
    print("New Swagger schema structure:")
    print(json.dumps(example_schema, indent=2))
    
    print("\nThis replaces the old generic structure with 'additionalProp1'")

if __name__ == "__main__":
    async def main():
        await show_swagger_example()
        await test_schema_validation()
    
    asyncio.run(main())
