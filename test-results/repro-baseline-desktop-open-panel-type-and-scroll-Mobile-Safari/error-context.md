# Page snapshot

```yaml
- navigation:
  - link "LOGO":
    - /url: /
  - button "Categories":
    - img "Categories"
    - img
  - button:
    - img
- main:
  - heading "pokemon" [level=2]
  - heading "Winning is a real prize" [level=1]
  - paragraph: Experience the thrill of x, where every open case has a chance to win a superfight Package
  - button "enroll"
  - heading "Winners Icon Winners" [level=2]:
    - img "Winners Icon"
    - text: Winners
  - text: ★ AnSenSei
  - img "test_search_production"
  - heading "test_search_production" [level=3]
  - img "Coin"
  - text: 2000.00 ★ AnSenSei
  - img "test_search_production"
  - heading "test_search_production" [level=3]
  - img "Coin"
  - text: 2000.00 ★ AnSenSei
  - img "1"
  - heading "1" [level=3]
  - img "Coin"
  - text: 2000.00 ★ AnSenSei
  - img "1"
  - heading "1" [level=3]
  - img "Coin"
  - text: 2000.00 ★ AnSenSei
  - img "test_search_production"
  - heading "test_search_production" [level=3]
  - img "Coin"
  - text: 2000.00 ★ AnSenSei
  - img "test_search_production"
  - heading "test_search_production" [level=3]
  - img "Coin"
  - text: 2000.00 ★ AnSenSei
  - img "test_search_production"
  - heading "test_search_production" [level=3]
  - img "Coin"
  - text: 2000.00 ★ AnSenSei
  - img "test_search_production"
  - heading "test_search_production" [level=3]
  - img "Coin"
  - text: 2000.00 ★ AnSenSei
  - img "1"
  - heading "1" [level=3]
  - img "Coin"
  - text: 2000.00 ★ Andy
  - img "test_search_production"
  - heading "test_search_production" [level=3]
  - img "Coin"
  - text: 2000.00 ★ Andy
  - img "test_search_production"
  - heading "test_search_production" [level=3]
  - img "Coin"
  - text: 2000.00 ★ Andy
  - img "test_search_production"
  - heading "test_search_production" [level=3]
  - img "Coin"
  - text: 2000.00 ★ Andy
  - img "test_search_production"
  - heading "test_search_production" [level=3]
  - img "Coin"
  - text: 2000.00 ★ Andy
  - img "1"
  - heading "1" [level=3]
  - img "Coin"
  - text: 2000.00 ★ AnSenSei
  - img "test_search_production"
  - heading "test_search_production" [level=3]
  - img "Coin"
  - text: 2000.00 ★ AnSenSei
  - img "test_search_production"
  - heading "test_search_production" [level=3]
  - img "Coin"
  - text: 2000.00 ★ AnSenSei
  - img "test_search_production"
  - heading "test_search_production" [level=3]
  - img "Coin"
  - text: 2000.00 ★ AnSenSei
  - img "1"
  - heading "1" [level=3]
  - img "Coin"
  - text: 2000.00 ★ AnSenSei
  - img "test_search_production"
  - heading "test_search_production" [level=3]
  - img "Coin"
  - text: 2000.00 ★ AnSenSei
  - img "test_search_production"
  - heading "test_search_production" [level=3]
  - img "Coin"
  - text: 2000.00 ★ AnSenSei
  - img "test_search_production"
  - heading "test_search_production" [level=3]
  - img "Coin"
  - text: 2000.00 ★ AnSenSei
  - img "test_search_production"
  - heading "test_search_production" [level=3]
  - img "Coin"
  - text: 2000.00 ★ AnSenSei
  - img "1"
  - heading "1" [level=3]
  - img "Coin"
  - text: 2000.00 ★ AnSenSei
  - img "1"
  - heading "1" [level=3]
  - img "Coin"
  - text: 2000.00 ★ AnSenSei
  - img "test_search_production"
  - heading "test_search_production" [level=3]
  - img "Coin"
  - text: 2000.00 ★ AnSenSei
  - img "test_search_production"
  - heading "test_search_production" [level=3]
  - img "Coin"
  - text: 2000.00 ★ AnSenSei
  - img "test_search_production"
  - heading "test_search_production" [level=3]
  - img "Coin"
  - text: 2000.00 ★ AnSenSei
  - img "test_search_production"
  - heading "test_search_production" [level=3]
  - img "Coin"
  - text: 2000.00 ★ AnSenSei
  - img "1"
  - heading "1" [level=3]
  - img "Coin"
  - text: 2000.00 ★ Andy
  - img "test_search_production"
  - heading "test_search_production" [level=3]
  - img "Coin"
  - text: 2000.00 ★ Andy
  - img "test_search_production"
  - heading "test_search_production" [level=3]
  - img "Coin"
  - text: 2000.00 ★ Andy
  - img "test_search_production"
  - heading "test_search_production" [level=3]
  - img "Coin"
  - text: 2000.00 ★ Andy
  - img "test_search_production"
  - heading "test_search_production" [level=3]
  - img "Coin"
  - text: 2000.00 ★ Andy
  - img "1"
  - heading "1" [level=3]
  - img "Coin"
  - text: 2000.00 ★ AnSenSei
  - img "test_search_production"
  - heading "test_search_production" [level=3]
  - img "Coin"
  - text: 2000.00 ★ AnSenSei
  - img "test_search_production"
  - heading "test_search_production" [level=3]
  - img "Coin"
  - text: 2000.00 ★ AnSenSei
  - img "test_search_production"
  - heading "test_search_production" [level=3]
  - img "Coin"
  - text: 2000.00 ★ AnSenSei
  - img "1"
  - heading "1" [level=3]
  - img "Coin"
  - text: 2000.00 ★ AnSenSei
  - img "test_search_production"
  - heading "test_search_production" [level=3]
  - img "Coin"
  - text: 2000.00 ★ AnSenSei
  - img "test_search_production"
  - heading "test_search_production" [level=3]
  - img "Coin"
  - text: "2000.00"
  - button "Filters":
    - img
    - text: Filters
  - heading "pokemon Packs" [level=2]
  - link "new_pack_test Fusion new_pack_test Max 3000 Win 70% Min 100 Coin 1890.00":
    - /url: /packs/pokemon/new_pack_test
    - img "new_pack_test"
    - img
    - text: Fusion new_pack_test Max 3000 Win 70% Min 100
    - img "Coin"
    - text: "1890.00"
  - link "testCard Fusion testCard Max 60 Win 60% Min ? Coin 60.00":
    - /url: /packs/pokemon/测试卡包
    - img "testCard"
    - img
    - text: Fusion testCard Max 60 Win 60% Min ?
    - img "Coin"
    - text: "60.00"
  - link "test_base64 test_base64 Max 2000 Win 80% Min ? Coin 700.00":
    - /url: /packs/pokemon/test_base64
    - img "test_base64"
    - text: test_base64 Max 2000 Win 80% Min ?
    - img "Coin"
    - text: "700.00"
  - link "test_price test_price Max ? Win 60% Min ? Coin 3520.00":
    - /url: /packs/pokemon/test_price
    - img "test_price"
    - text: test_price Max ? Win 60% Min ?
    - img "Coin"
    - text: "3520.00"
  - link "test_bug_image test_bug_image Max 800 Win 90% Min ? Coin 700.00":
    - /url: /packs/pokemon/test_bug_image
    - img "test_bug_image"
    - text: test_bug_image Max 800 Win 90% Min ?
    - img "Coin"
    - text: "700.00"
  - link "test_new_activate Max 700 Win 60% Min ? Coin 400.00":
    - /url: /packs/pokemon/test_new_activate
    - text: test_new_activate Max 700 Win 60% Min ?
    - img "Coin"
    - text: "400.00"
  - link "test_activate Max ? Win 60% Min ? Coin Free":
    - /url: /packs/pokemon/test_activate
    - text: test_activate Max ? Win 60% Min ?
    - img "Coin"
    - text: Free
- alert
```