# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Dependencies
node_modules
.pnp
.pnp.js

# Build outputs
dist
dist-ssr
dist-analyze
coverage
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Environment variables
.env.local
.env.*.local

# Cache
.eslintcache
.stylelintcache

# Temporary files
.temp
.tmp
.cache

# Stats
stats.html

# AI Study directory
ai-study/

# Archive files
*.zip
*.rar
*.tar.gz
*.7z
