server {
    listen       80;
    listen       8080;
    server_name  _;
    
    access_log  /var/log/nginx/host.access.log  main;
    error_log  /var/log/nginx/error.log  error;

    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml application/javascript application/json;
    gzip_disable "MSIE [1-6]\.";

    # Handle static assets
    location ~ ^/assets/(.*)$ {
        root /usr/share/nginx/html;
        try_files $uri =404;
        
        # Cache static assets
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Content-Type-Options "nosniff";
    }

    location = /vite.svg {
        root /usr/share/nginx/html;
        try_files $uri =404;
    }

    location = /favicon.ico {
        root /usr/share/nginx/html;
        try_files $uri =404;
    }

    # Regular paths
    location / {
        root   /usr/share/nginx/html;
        index  index.html index.htm;
        try_files $uri $uri/ /index.html;
        
        # Prevent caching of HTML to ensure fresh builds are loaded
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
        add_header X-Content-Type-Options "nosniff";
    }


    location /api/ {
        proxy_pass https://backend-769075815684.us-central1.run.app/gacha/api/v1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        rewrite ^/api/(.*) /$1 break;
    }

    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }
}
