steps:
  # Build the Docker image with production environment variables (no cache)
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'build'
      - '--no-cache'   # 防止复用旧层
      - '-t'
      - 'us-docker.pkg.dev/$PROJECT_ID/frontend/frontend:latest'
      - '--build-arg'
      - 'NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_51RRaUfG6OdPX4kJzf2jJNN6RgHA5jtt5ZJh78VZMUh04uUG9Ua7JCJ7wdfL6vrF8F7k2jQVQRrCdlDByTeG2uBNm009LYXfPwS'
      - '--build-arg'
      - 'NEXT_PUBLIC_FIREBASE_API_KEY=AIzaSyD0lPBblDCNaRVIJzt0vr3589EbzEDlROQ'
      - '--build-arg'
      - 'NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=zapull-production.firebaseapp.com'
      - '--build-arg'
      - 'NEXT_PUBLIC_FIREBASE_PROJECT_ID=zapull-production'
      - '--build-arg'
      - 'NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=zapull-production.firebasestorage.app'
      - '--build-arg'
      - 'NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=769075815684'
      - '--build-arg'
      - 'NEXT_PUBLIC_FIREBASE_APP_ID=1:769075815684:web:94a8d97592c6f3fbf95c51'
      - '--build-arg'
      - 'NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=G-V5SJVMMW0F'
      - '--build-arg'
      - 'NEXT_PUBLIC_API_BASE_URL=https://user-backend-769075815684.us-central1.run.app/users/api/v1'
      - '--build-arg'
      - 'NEXT_PUBLIC_API_USER_URL=https://user-backend-769075815684.us-central1.run.app'
      - '--build-arg'
      - 'NEXT_PUBLIC_GACHA_API_BASE_URL=https://backend-769075815684.us-central1.run.app/gacha/api/v1'
      - '--build-arg'
      - 'NEXT_PUBLIC_ACHIEVEMENT_CHECK_URL=https://check-achievements-769075815684.us-central1.run.app'
      - '.'

  # Push the Docker image to Artifact Registry
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'us-docker.pkg.dev/$PROJECT_ID/frontend/frontend:latest']

  # Deploy to Cloud Run (production)
  - name: 'gcr.io/cloud-builders/gcloud'
    args:
      - 'run'
      - 'deploy'
      - 'zapull-frontend'
      - '--image=us-docker.pkg.dev/$PROJECT_ID/frontend/frontend:latest'
      - '--platform=managed'
      - '--region=us-central1'
      - '--allow-unauthenticated'
      - '--port=3000'
      - '--memory=512Mi'
      - '--cpu=1'
      - '--min-instances=0'
      - '--max-instances=10'
      # 运行时也设置 pk_live（防 SSR 读错 env）
      - '--set-env-vars=NODE_ENV=production,NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_51RRaUfG6OdPX4kJzf2jJNN6RgHA5jtt5ZJh78VZMUh04uUG9Ua7JCJ7wdfL6vrF8F7k2jQVQRrCdlDByTeG2uBNm009LYXfPwS'

images:
  - 'us-docker.pkg.dev/$PROJECT_ID/frontend/frontend:latest'

timeout: '1200s'