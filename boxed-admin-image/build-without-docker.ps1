# Docker Alternative Solutions Script
param(
    [string]$Method = "auto"
)

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Docker Image Build Alternatives" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

function Test-Command($command) {
    try {
        Get-Command $command -ErrorAction Stop | Out-Null
        return $true
    } catch {
        return $false
    }
}

function Show-Menu {
    Write-Host "Please select build method:" -ForegroundColor Yellow
    Write-Host "1. <PERSON> (Recommended)" -ForegroundColor White
    Write-Host "2. Create GitHub Actions Workflow" -ForegroundColor White
    Write-Host "3. View Cloud Service Solutions" -ForegroundColor White
    Write-Host "4. Create Static File Package" -ForegroundColor White
    Write-Host "5. Check WSL2 + Docker" -ForegroundColor White
    Write-Host "6. View All Solutions" -ForegroundColor White
    Write-Host ""
}

function Install-Podman {
    Write-Host "Checking Podman installation..." -ForegroundColor Yellow
    
    if (Test-Command "podman") {
        Write-Host "Podman is installed" -ForegroundColor Green
        return $true
    }
    
    Write-Host "Podman not installed" -ForegroundColor Red
    Write-Host "Installation options:" -ForegroundColor Yellow
    Write-Host "1. Use Chocolatey: choco install podman-desktop" -ForegroundColor White
    Write-Host "2. Download from: https://podman.io/getting-started/installation" -ForegroundColor White
    
    $install = Read-Host "Install with Chocolatey? (y/n)"
    if ($install -eq "y" -or $install -eq "Y") {
        if (Test-Command "choco") {
            Write-Host "Installing Podman..." -ForegroundColor Yellow
            choco install podman-desktop -y
            return Test-Command "podman"
        } else {
            Write-Host "Chocolatey not installed, please install Podman manually" -ForegroundColor Red
            return $false
        }
    }
    return $false
}

function Build-WithPodman {
    Write-Host "Building with Podman..." -ForegroundColor Cyan
    
    if (-not (Install-Podman)) {
        Write-Host "Podman not available, please choose another option" -ForegroundColor Red
        return
    }
    
    if (-not (Test-Path "dist")) {
        Write-Host "dist folder not found, please build project first" -ForegroundColor Red
        Write-Host "Run: npm run build:skip:prod" -ForegroundColor Yellow
        return
    }
    
    Write-Host "Starting build..." -ForegroundColor Yellow
    podman build -t boxed-admin:latest .
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Build successful!" -ForegroundColor Green
        Write-Host "Exporting image..." -ForegroundColor Yellow
        podman save -o boxed-admin.tar boxed-admin:latest
        
        if (Test-Path "boxed-admin.tar") {
            Write-Host "Image exported to: boxed-admin.tar" -ForegroundColor Green
            $size = (Get-Item 'boxed-admin.tar').Length / 1MB
            Write-Host "File size: $([math]::Round($size, 2)) MB" -ForegroundColor White
        }
    } else {
        Write-Host "Build failed" -ForegroundColor Red
    }
}

function Create-StaticPackage {
    Write-Host "Creating static file package..." -ForegroundColor Cyan
    
    if (-not (Test-Path "dist")) {
        Write-Host "dist folder not found, please build project first" -ForegroundColor Red
        return
    }
    
    $packageDir = "boxed-admin-static"
    if (Test-Path $packageDir) {
        Remove-Item $packageDir -Recurse -Force
    }
    
    New-Item -ItemType Directory -Path $packageDir -Force | Out-Null
    
    Copy-Item -Path "dist/*" -Destination $packageDir -Recurse
    Copy-Item -Path "nginx.conf" -Destination "$packageDir/nginx.conf"
    
    $deployDoc = "# Static File Deployment Guide`n`n## Using Nginx:`n1. Copy all files to nginx html directory`n2. Apply nginx.conf configuration`n3. Restart nginx service"
    
    Set-Content -Path "$packageDir/DEPLOY.md" -Value $deployDoc -Encoding UTF8
    
    Compress-Archive -Path "$packageDir/*" -DestinationPath "boxed-admin-static.zip" -Force
    
    Write-Host "Static package created: boxed-admin-static.zip" -ForegroundColor Green
    $size = (Get-Item 'boxed-admin-static.zip').Length / 1MB
    Write-Host "File size: $([math]::Round($size, 2)) MB" -ForegroundColor White
}

function Create-GitHubActions {
    Write-Host "Creating GitHub Actions workflow..." -ForegroundColor Cyan
    
    $workflowDir = "../.github/workflows"
    if (-not (Test-Path $workflowDir)) {
        New-Item -ItemType Directory -Path $workflowDir -Force | Out-Null
    }
    
    $workflowContent = @'
name: Build Docker Image

on:
  push:
    branches: [ main, master ]
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm install
    
    - name: Build project
      run: npm run build:skip:prod
    
    - name: Prepare Docker context
      run: |
        cd boxed-admin-image
        cp -r ../dist .
    
    - name: Build Docker image
      run: |
        cd boxed-admin-image
        docker build -t boxed-admin:latest .
    
    - name: Save Docker image
      run: |
        docker save -o boxed-admin.tar boxed-admin:latest
    
    - name: Upload Docker image
      uses: actions/upload-artifact@v3
      with:
        name: docker-image
        path: boxed-admin.tar
        retention-days: 30
'@
    
    $workflowFile = "$workflowDir/docker-build.yml"
    Set-Content -Path $workflowFile -Value $workflowContent -Encoding UTF8
    
    Write-Host "GitHub Actions workflow created: $workflowFile" -ForegroundColor Green
    Write-Host "Push code to GitHub to trigger the workflow" -ForegroundColor Yellow
}

function Check-WSL2Docker {
    Write-Host "Checking WSL2 + Docker availability..." -ForegroundColor Cyan
    
    if (Test-Command "wsl") {
        Write-Host "WSL is installed" -ForegroundColor Green
        
        try {
            $wslDistros = wsl -l -v
            Write-Host "WSL distributions:" -ForegroundColor Yellow
            Write-Host $wslDistros -ForegroundColor White
        } catch {
            Write-Host "Failed to get WSL info" -ForegroundColor Red
        }
        
        Write-Host "Commands to install Docker in WSL:" -ForegroundColor Yellow
        Write-Host "wsl -d Ubuntu" -ForegroundColor White
        Write-Host "curl -fsSL https://get.docker.com -o get-docker.sh" -ForegroundColor White
        Write-Host "sudo sh get-docker.sh" -ForegroundColor White
        Write-Host "sudo service docker start" -ForegroundColor White
    } else {
        Write-Host "WSL not installed" -ForegroundColor Red
        Write-Host "Install WSL: wsl --install" -ForegroundColor Yellow
    }
}

# Main logic
if ($Method -eq "auto") {
    Show-Menu
    $choice = Read-Host "Please enter option (1-6)"
} else {
    $choice = $Method
}

switch ($choice) {
    "1" { Build-WithPodman }
    "2" { Create-GitHubActions }
    "3" { 
        Write-Host "View detailed cloud service configs: alternative-solutions.md" -ForegroundColor Yellow
        if (Test-Path "alternative-solutions.md") {
            notepad "alternative-solutions.md"
        }
    }
    "4" { Create-StaticPackage }
    "5" { Check-WSL2Docker }
    "6" { 
        Write-Host "View detailed solutions: alternative-solutions.md" -ForegroundColor Yellow
        if (Test-Path "alternative-solutions.md") {
            notepad "alternative-solutions.md"
        }
    }
    default {
        Write-Host "Invalid option" -ForegroundColor Red
        Show-Menu
    }
}

Write-Host ""
Write-Host "Script completed" -ForegroundColor Green