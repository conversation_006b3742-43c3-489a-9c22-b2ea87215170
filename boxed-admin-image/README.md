# Boxed Admin Docker 镜像构建指南

## 文件说明

- `Dockerfile` - 使用官方nginx镜像的构建文件
- `Dockerfile.alpine` - 使用Alpine Linux的轻量级构建文件
- `nginx.conf` - Nginx配置文件
- `dist/` - 前端构建产物目录
- `build-docker.bat` - 自动化构建脚本

## 构建方法

### 方法1：使用构建脚本（推荐）
```bash
# Windows
build-docker.bat

# Linux/Mac
chmod +x build-docker.sh
./build-docker.sh
```

### 方法2：手动构建

#### 使用官方nginx镜像
```bash
docker build -t boxed-admin:nginx .
```

#### 使用Alpine Linux（轻量级）
```bash
docker build -f Dockerfile.alpine -t boxed-admin:alpine .
```

## 运行容器

```bash
# 使用nginx版本
docker run -d -p 8080:80 --name boxed-admin-container boxed-admin:nginx

# 使用alpine版本
docker run -d -p 8080:80 --name boxed-admin-container boxed-admin:alpine
```

访问地址：http://localhost:8080

## 故障排除

### 网络连接问题
如果遇到无法拉取镜像的问题，可以：

1. **配置Docker镜像源**
   ```json
   {
     "registry-mirrors": [
       "https://mirror.ccs.tencentyun.com",
       "https://docker.mirrors.ustc.edu.cn",
       "https://reg-mirror.qiniu.com"
     ]
   }
   ```

2. **手动拉取镜像**
   ```bash
   docker pull nginx:latest
   docker pull alpine:latest
   ```

3. **使用离线镜像**
   如果有离线的nginx镜像文件，可以使用：
   ```bash
   docker load -i nginx-latest.tar
   ```

### 常用Docker命令

```bash
# 查看镜像
docker images

# 查看运行中的容器
docker ps

# 停止容器
docker stop boxed-admin-container

# 删除容器
docker rm boxed-admin-container

# 删除镜像
docker rmi boxed-admin:nginx
```

## 镜像信息

- **nginx版本**: 基于官方nginx镜像，功能完整
- **alpine版本**: 基于Alpine Linux，体积更小，启动更快
- **端口**: 容器内部使用80端口
- **静态文件路径**: `/usr/share/nginx/html/`
- **配置文件路径**: `/etc/nginx/conf.d/default.conf`