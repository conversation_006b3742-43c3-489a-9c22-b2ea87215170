# Docker无法启动时的镜像生成替代方案

## 问题描述
Docker服务无法启动，但需要将项目打包成容器镜像。

## 解决方案

### 方案1：使用Podman（推荐）
Podman是Docker的替代品，无需守护进程即可运行。

#### 安装Podman
```bash
# Windows (使用Chocolatey)
choco install podman-desktop

# 或下载安装包
# https://podman.io/getting-started/installation
```

#### 使用Podman构建
```bash
# 构建镜像
podman build -t boxed-admin:latest .

# 运行容器
podman run -d -p 8080:80 --name boxed-admin-container boxed-admin:latest

# 导出镜像
podman save -o boxed-admin.tar boxed-admin:latest
```

### 方案2：使用云服务构建

#### GitHub Actions
创建 `.github/workflows/docker-build.yml`：
```yaml
name: Build Docker Image

on:
  push:
    branches: [ main ]
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Build project
      run: |
        npm install
        npm run build:skip:prod
    
    - name: Build Docker image
      run: |
        cd boxed-admin-image
        cp -r ../dist .
        docker build -t boxed-admin:latest .
    
    - name: Save Docker image
      run: |
        docker save -o boxed-admin.tar boxed-admin:latest
    
    - name: Upload artifact
      uses: actions/upload-artifact@v3
      with:
        name: docker-image
        path: boxed-admin.tar
```

#### 阿里云容器镜像服务
1. 登录阿里云控制台
2. 进入容器镜像服务
3. 创建镜像仓库
4. 使用代码源自动构建

#### 腾讯云容器镜像服务
1. 登录腾讯云控制台
2. 进入容器镜像服务TCR
3. 创建镜像仓库
4. 配置自动构建

### 方案3：使用Buildah
Buildah是专门用于构建容器镜像的工具。

```bash
# 安装Buildah (Linux)
sudo apt-get install buildah

# 构建镜像
buildah build-using-dockerfile -t boxed-admin:latest .

# 导出镜像
buildah push boxed-admin:latest oci-archive:boxed-admin.tar
```

### 方案4：手动创建镜像包

#### 创建tar包结构
```bash
# 创建镜像目录结构
mkdir -p boxed-admin-manual/{rootfs,metadata}

# 复制文件
cp -r dist/* boxed-admin-manual/rootfs/usr/share/nginx/html/
cp nginx.conf boxed-admin-manual/rootfs/etc/nginx/conf.d/default.conf

# 创建manifest.json
echo '{
  "schemaVersion": 2,
  "mediaType": "application/vnd.docker.distribution.manifest.v2+json",
  "config": {
    "mediaType": "application/vnd.docker.container.image.v1+json",
    "digest": "sha256:..."
  }
}' > boxed-admin-manual/manifest.json

# 打包
tar -czf boxed-admin-manual.tar.gz -C boxed-admin-manual .
```

### 方案5：使用WSL2 + Docker
如果是Windows环境，可以在WSL2中安装Docker。

```bash
# 在WSL2中安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 启动Docker服务
sudo service docker start

# 构建镜像
docker build -t boxed-admin:latest .
```

### 方案6：使用在线构建服务

#### Docker Hub自动构建
1. 将代码推送到GitHub
2. 连接Docker Hub到GitHub仓库
3. 配置自动构建规则

#### GitLab CI/CD
创建 `.gitlab-ci.yml`：
```yaml
stages:
  - build
  - package

build-app:
  stage: build
  script:
    - npm install
    - npm run build:skip:prod
  artifacts:
    paths:
      - dist/

build-docker:
  stage: package
  image: docker:latest
  services:
    - docker:dind
  script:
    - cd boxed-admin-image
    - cp -r ../dist .
    - docker build -t boxed-admin:latest .
    - docker save -o boxed-admin.tar boxed-admin:latest
  artifacts:
    paths:
      - boxed-admin-image/boxed-admin.tar
```

## 推荐方案选择

1. **快速解决**：使用Podman（最接近Docker体验）
2. **自动化**：使用GitHub Actions或GitLab CI/CD
3. **企业级**：使用云服务（阿里云、腾讯云等）
4. **离线环境**：手动创建镜像包

## 注意事项

1. 确保所有依赖文件都已准备好
2. 检查网络连接和权限设置
3. 验证生成的镜像文件完整性
4. 保存镜像文件到安全位置

## 验证镜像

无论使用哪种方案，都可以通过以下方式验证：

```bash
# 加载镜像（如果是tar格式）
docker load -i boxed-admin.tar

# 运行测试
docker run -d -p 8080:80 boxed-admin:latest

# 访问测试
curl http://localhost:8080
```