upstream my_server{
  server ***********:3000; # 后端server 地址
  keepalive 2000;
}

server {
    listen       80;
    server_name  www.xxx.com; # 修改为docker服务宿主机的ip/域名
    
    #charset koi8-r;
    access_log  /var/log/nginx/host.access.log  main;
    error_log  /var/log/nginx/error.log  error;

    location / {
        root   /usr/share/nginx/html;
        index  index.html index.htm;
        try_files $uri $uri/ /index.html =404;
    }
     #  已api开头的路径都代理到本机的3000端口
    location /api/ {
        proxy_pass https://backend-769075815684.us-central1.run.app/gacha/api/v1;
        proxy_set_header Host $host:$server_port;
        rewrite ^/api/(.*) /$1 break;
    }

    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   html;
    }
    
}
