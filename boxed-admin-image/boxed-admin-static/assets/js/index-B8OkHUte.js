import{g as e,a,b as l,c as t,u as o,d as r,e as u,f as i}from"./storage-BmuEW5sW.js";import{E as d,r as n,w as c,x as s}from"./elementPlus-Bm-E4Nxd.js";import{x as p,X as m,r as _,h as v,a3 as y,Y as f,y as g,P as h,H as b,ag as V,O as w,a6 as k,L as C,A as N,I as q,aq as x,G as U,u as z,M as B,z as O}from"./vue-DWGM1jOu.js";import{_ as F}from"./index-Br8T7GvP.js";import"./request-ZE4ZUch8.js";const I={class:"storage-container"},Q={class:"header-actions"},S={key:1},j={class:"pagination-container"},P={key:1},R=["src"],D={class:"dialog-footer"},G=F(p({name:"StorageCards",__name:"index",setup(p){const F=m({collectionName:"",sortBy:"point_worth",sortOrder:"desc",searchQuery:"",cardId:""}),G=_([]),J=_(!1),A=_(1),L=_(10),M=_(!1),T=_(0),E=_([]),H=_(!1),X=_(!1),Y=_({id:"",card_name:"",rarity:0,point_worth:0,date_got_in_stock:"",image_url:"",quantity:0,condition:"",used_in_fusion:[]}),K=_(!1),W=_(!1),Z=_(),$=_(""),ee=m({image_base64:"",card_name:"",rarity:"",point_worth:0,collection_metadata_id:"",quantity:0,condition:"mint"}),ae={card_name:[{required:!0,message:"请输入卡片名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],collection_metadata_id:[{required:!0,message:"请选择卡片分类",trigger:"change"}],rarity:[{required:!0,message:"请输入稀有度",trigger:"blur"}],point_worth:[{required:!0,message:"请输入点数",trigger:"blur"}]},le=_(!1),te=_(!1),oe=_(),re=_(null),ue=m({collection_metadata_id:"",card_name:"",rarity:0,point_worth:0,quantity:0,condition:""}),ie={card_name:[{required:!0,message:"请输入卡片名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],rarity:[{required:!0,message:"请输入稀有度",trigger:"blur"}],point_worth:[{required:!0,message:"请输入点数",trigger:"blur"}]},de=_(!1),ne=_(0),ce=_(null),se=_(!1),pe=_(!1),me=_({card_id:"",collection_id:"",fusions:[]}),_e=async()=>{J.value=!0;try{const e=await a({collectionName:F.collectionName,page:A.value,per_page:L.value,sort_by:F.sortBy,sort_order:F.sortOrder,search_query:F.searchQuery,card_id:F.cardId});G.value=e.cards,T.value=e.pagination.total_items}catch(e){d.error("获取卡片列表失败")}finally{J.value=!1}},ve=()=>{A.value=1,_e()},ye=()=>{F.collectionName="",F.sortBy="point_worth",F.sortOrder="desc",F.searchQuery="",F.cardId="",A.value=1,_e()},fe=()=>{A.value=1,_e()},ge=e=>{L.value=e,_e()},he=e=>{A.value=e,_e()},be=()=>{K.value=!0},Ve=e=>{const a="image/jpeg"===e.raw.type||"image/png"===e.raw.type,l=e.raw.size/1024/1024<2;if(!a)return void d.error("上传图片只能是 JPG 或 PNG 格式!");if(!l)return void d.error("上传图片大小不能超过 2MB!");const t=new FileReader;t.readAsDataURL(e.raw),t.onload=()=>{$.value=t.result,ee.image_base64=t.result}},we=async()=>{Z.value&&await Z.value.validate(async e=>{if(!e)return!1;W.value=!0;try{await t(ee),d.success("创建卡片成功"),K.value=!1,ke(),_e()}catch(a){d.error("创建卡片失败")}finally{W.value=!1}})},ke=()=>{Z.value&&(Z.value.resetFields(),$.value="")},Ce=async()=>{oe.value&&re.value&&await oe.value.validate(async e=>{if(!e)return!1;te.value=!0;try{await o(re.value.id,ue),d.success("更新卡片成功"),le.value=!1,_e()}catch(a){d.error("更新卡片失败")}finally{te.value=!1}})},Ne=async()=>{if(ce.value&&F.collectionName)try{await r(ce.value.id,F.collectionName,ne.value),d.success("更新数量成功"),de.value=!1,_e()}catch(e){d.error("更新数量失败")}else d.error("参数错误")};return v(()=>{(async()=>{try{const{collections:a,pokemenCollection:l}=await e();E.value=a,l&&(F.collectionName=l,M.value&&_e())}catch(a){d.error("获取卡片分类失败")}})(),M.value=!0}),y(()=>{localStorage.setItem("storageCards_state",JSON.stringify({searchForm:{collectionName:F.collectionName,sortBy:F.sortBy,sortOrder:F.sortOrder,searchQuery:F.searchQuery},pagination:{currentPage:A.value,pageSize:L.value}}))}),f(()=>{const e=localStorage.getItem("storageCards_state");if(e){const a=JSON.parse(e);a.searchForm&&(F.collectionName=a.searchForm.collectionName,F.sortBy=a.searchForm.sortBy,F.sortOrder=a.searchForm.sortOrder,F.searchQuery=a.searchForm.searchQuery),a.pagination&&(A.value=a.pagination.currentPage,L.value=a.pagination.pageSize)}F.collectionName&&_e()}),(e,a)=>{const t=V("el-option"),o=V("el-select"),r=V("el-form-item"),p=V("el-input"),m=V("el-button"),_=V("el-form"),v=V("el-card"),y=V("el-table-column"),f=V("el-image"),M=V("el-icon"),qe=V("el-dropdown-item"),xe=V("el-dropdown-menu"),Ue=V("el-dropdown"),ze=V("el-table"),Be=V("el-pagination"),Oe=V("el-descriptions-item"),Fe=V("el-descriptions"),Ie=V("el-drawer"),Qe=V("el-input-number"),Se=V("el-upload"),je=V("el-dialog"),Pe=V("el-empty"),Re=x("loading");return O(),g("div",I,[h(v,{class:"search-card"},{default:b(()=>[h(_,{ref:"searchFormRef",inline:""},{default:b(()=>[h(r,{label:"卡片分类",prop:"collectionName"},{default:b(()=>[h(o,{modelValue:F.collectionName,"onUpdate:modelValue":a[0]||(a[0]=e=>F.collectionName=e),placeholder:"请选择卡片分类",clearable:"","popper-append-to-body":"",onChange:ve,style:{width:"150px"}},{default:b(()=>[(O(!0),g(w,null,k(E.value,e=>(O(),U(t,{key:e.name,label:e.name,value:e.name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),h(r,{label:"排序字段",prop:"sortBy"},{default:b(()=>[h(o,{modelValue:F.sortBy,"onUpdate:modelValue":a[1]||(a[1]=e=>F.sortBy=e),placeholder:"请选择排序字段",style:{width:"150px"},clearable:""},{default:b(()=>[h(t,{label:"点数",value:"point_worth"}),h(t,{label:"卡片名称",value:"card_name"}),h(t,{label:"入库时间",value:"date_got_in_stock"}),h(t,{label:"数量",value:"quantity"}),h(t,{label:"稀有度",value:"rarity"})]),_:1},8,["modelValue"])]),_:1}),h(r,{label:"排序方式",prop:"sortOrder"},{default:b(()=>[h(o,{modelValue:F.sortOrder,"onUpdate:modelValue":a[2]||(a[2]=e=>F.sortOrder=e),placeholder:"请选择排序方式",style:{width:"150px"},clearable:""},{default:b(()=>[h(t,{label:"升序",value:"asc"}),h(t,{label:"降序",value:"desc"})]),_:1},8,["modelValue"])]),_:1}),h(r,{label:"搜索",prop:"searchQuery"},{default:b(()=>[h(p,{modelValue:F.searchQuery,"onUpdate:modelValue":a[3]||(a[3]=e=>F.searchQuery=e),placeholder:"请输入搜索关键词",clearable:""},null,8,["modelValue"])]),_:1}),h(r,null,{default:b(()=>[h(m,{type:"primary",onClick:fe},{default:b(()=>a[25]||(a[25]=[C("查询")])),_:1,__:[25]}),h(m,{onClick:ye},{default:b(()=>a[26]||(a[26]=[C("重置")])),_:1,__:[26]})]),_:1})]),_:1},512)]),_:1}),h(v,{class:"table-card"},{default:b(()=>[N("div",Q,[h(m,{type:"primary",onClick:be},{default:b(()=>a[27]||(a[27]=[C("创建卡片")])),_:1,__:[27]})]),q((O(),U(ze,{data:G.value,border:"",stripe:""},{default:b(()=>[h(y,{prop:"card_name",label:"卡片名称"}),h(y,{prop:"rarity",label:"稀有度"}),h(y,{prop:"point_worth",label:"点数"}),h(y,{prop:"quantity",label:"数量"}),h(y,{prop:"condition",label:"状态"}),h(y,{prop:"date_got_in_stock",label:"入库时间",width:"180"}),h(y,{label:"图片",width:"120"},{default:b(({row:e})=>[e.image_url?(O(),U(f,{key:0,src:e.image_url,style:{width:"80px",height:"80px"},"preview-src-list":[e.image_url]},null,8,["src","preview-src-list"])):(O(),g("span",S,"-"))]),_:1}),h(y,{label:"操作",width:"280",fixed:"right"},{default:b(({row:e})=>[h(m,{link:"",type:"primary",onClick:a=>(async e=>{if(e.id&&F.collectionName){X.value=!0;try{const a=await l(e.id,F.collectionName);Y.value=a,H.value=!0}catch(a){d.error("获取卡片详情失败")}finally{X.value=!1}}else d.error("参数错误")})(e)},{default:b(()=>a[28]||(a[28]=[C("详情")])),_:2,__:[28]},1032,["onClick"]),h(m,{link:"",type:"primary",onClick:a=>(async e=>{if(e.id&&F.collectionName){pe.value=!0,se.value=!0;try{const a=await i(F.collectionName,e.id);me.value=a}catch(a){d.error("获取合成列表失败")}finally{pe.value=!1}}else d.error("参数错误")})(e)},{default:b(()=>a[29]||(a[29]=[C("查看合成")])),_:2,__:[29]},1032,["onClick"]),h(Ue,null,{dropdown:b(()=>[h(xe,null,{default:b(()=>[h(qe,{onClick:a=>(e=>{e.id&&F.collectionName?(re.value=e,ue.collection_metadata_id=F.collectionName,ue.card_name=e.card_name,ue.rarity=e.rarity,ue.point_worth=e.point_worth,ue.quantity=e.quantity,ue.condition=e.condition,le.value=!0):d.error("参数错误")})(e)},{default:b(()=>a[31]||(a[31]=[C("编辑")])),_:2,__:[31]},1032,["onClick"]),h(qe,{onClick:a=>(e=>{e.id&&F.collectionName?(ce.value=e,ne.value=0,de.value=!0):d.error("参数错误")})(e)},{default:b(()=>a[32]||(a[32]=[C("修改数量")])),_:2,__:[32]},1032,["onClick"]),h(qe,{divided:"",onClick:a=>(e=>{e.id&&F.collectionName?s.confirm("确定要删除该卡片吗？删除后无法恢复。","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{await u(e.id,F.collectionName),d.success("删除卡片成功"),_e()}catch(a){d.error("删除卡片失败")}}).catch(()=>{}):d.error("参数错误")})(e),style:{color:"#F56C6C"}},{default:b(()=>a[33]||(a[33]=[C(" 删除 ")])),_:2,__:[33]},1032,["onClick"])]),_:2},1024)]),default:b(()=>[h(m,{link:"",type:"primary"},{default:b(()=>[a[30]||(a[30]=C("更多")),h(M,{class:"el-icon--right"},{default:b(()=>[h(z(n))]),_:1})]),_:1,__:[30]})]),_:2},1024)]),_:1})]),_:1},8,["data"])),[[Re,J.value]]),N("div",j,[h(Be,{"current-page":A.value,"onUpdate:currentPage":a[4]||(a[4]=e=>A.value=e),"page-size":L.value,"onUpdate:pageSize":a[5]||(a[5]=e=>L.value=e),"page-sizes":[10,20,50,100],total:T.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:ge,onCurrentChange:he},null,8,["current-page","page-size","total"])])]),_:1}),h(Ie,{modelValue:H.value,"onUpdate:modelValue":a[6]||(a[6]=e=>H.value=e),title:"卡片详情",size:"50%","destroy-on-close":!1},{default:b(()=>[q((O(),g("div",null,[h(Fe,{column:2,border:""},{default:b(()=>[h(Oe,{label:"卡片ID"},{default:b(()=>[C(B(Y.value.id),1)]),_:1}),h(Oe,{label:"卡片名称"},{default:b(()=>[C(B(Y.value.card_name),1)]),_:1}),h(Oe,{label:"稀有度"},{default:b(()=>[C(B(Y.value.rarity),1)]),_:1}),h(Oe,{label:"点数"},{default:b(()=>[C(B(Y.value.point_worth),1)]),_:1}),h(Oe,{label:"数量"},{default:b(()=>[C(B(Y.value.quantity),1)]),_:1}),h(Oe,{label:"状态"},{default:b(()=>[C(B(Y.value.condition),1)]),_:1}),h(Oe,{label:"入库时间"},{default:b(()=>[C(B(Y.value.date_got_in_stock||"-"),1)]),_:1}),h(Oe,{label:"卡片图片",span:2},{default:b(()=>[Y.value.image_url?(O(),U(f,{key:0,src:Y.value.image_url,style:{"max-width":"300px","max-height":"300px"}},null,8,["src"])):(O(),g("span",P,"-"))]),_:1})]),_:1})])),[[Re,X.value]])]),_:1},8,["modelValue"]),h(Ie,{modelValue:K.value,"onUpdate:modelValue":a[13]||(a[13]=e=>K.value=e),title:"创建卡片",size:"50%","destroy-on-close":!1},{default:b(()=>[q((O(),U(_,{ref_key:"createFormRef",ref:Z,model:ee,rules:ae,"label-width":"120px"},{default:b(()=>[h(r,{label:"卡片名称",prop:"card_name"},{default:b(()=>[h(p,{modelValue:ee.card_name,"onUpdate:modelValue":a[7]||(a[7]=e=>ee.card_name=e),placeholder:"请输入卡片名称"},null,8,["modelValue"])]),_:1}),h(r,{label:"卡片分类",prop:"collection_metadata_id"},{default:b(()=>[h(o,{modelValue:ee.collection_metadata_id,"onUpdate:modelValue":a[8]||(a[8]=e=>ee.collection_metadata_id=e),placeholder:"请选择卡片分类"},{default:b(()=>[(O(!0),g(w,null,k(E.value,e=>(O(),U(t,{key:e.name,label:e.name,value:e.name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),h(r,{label:"稀有度",prop:"rarity"},{default:b(()=>[h(p,{modelValue:ee.rarity,"onUpdate:modelValue":a[9]||(a[9]=e=>ee.rarity=e),placeholder:"请输入稀有度"},null,8,["modelValue"])]),_:1}),h(r,{label:"点数",prop:"point_worth"},{default:b(()=>[h(Qe,{modelValue:ee.point_worth,"onUpdate:modelValue":a[10]||(a[10]=e=>ee.point_worth=e),min:0,precision:0},null,8,["modelValue"])]),_:1}),h(r,{label:"数量",prop:"quantity"},{default:b(()=>[h(Qe,{modelValue:ee.quantity,"onUpdate:modelValue":a[11]||(a[11]=e=>ee.quantity=e),min:0,precision:0},null,8,["modelValue"])]),_:1}),h(r,{label:"状态",prop:"condition"},{default:b(()=>[h(o,{modelValue:ee.condition,"onUpdate:modelValue":a[12]||(a[12]=e=>ee.condition=e),placeholder:"请选择状态"},{default:b(()=>[h(t,{label:"全新",value:"new"}),h(t,{label:"良好",value:"good"}),h(t,{label:"完美",value:"mint"})]),_:1},8,["modelValue"])]),_:1}),h(r,{label:"卡片图片",prop:"image_base64"},{default:b(()=>[h(Se,{class:"avatar-uploader",action:"","auto-upload":!1,"show-file-list":!1,"on-change":Ve},{default:b(()=>[$.value?(O(),g("img",{key:0,src:$.value,class:"avatar"},null,8,R)):(O(),U(M,{key:1,class:"avatar-uploader-icon"},{default:b(()=>[h(z(c))]),_:1}))]),_:1}),a[34]||(a[34]=N("div",{class:"upload-tip"},"请上传卡片图片，支持jpg、png格式",-1))]),_:1,__:[34]}),h(r,null,{default:b(()=>[h(m,{type:"primary",onClick:we},{default:b(()=>a[35]||(a[35]=[C("创建")])),_:1,__:[35]}),h(m,{onClick:ke},{default:b(()=>a[36]||(a[36]=[C("重置")])),_:1,__:[36]})]),_:1})]),_:1},8,["model"])),[[Re,W.value]])]),_:1},8,["modelValue"]),h(Ie,{modelValue:le.value,"onUpdate:modelValue":a[20]||(a[20]=e=>le.value=e),title:"编辑卡片",size:"50%","destroy-on-close":!1},{default:b(()=>[q((O(),U(_,{ref_key:"editFormRef",ref:oe,model:ue,rules:ie,"label-width":"120px"},{default:b(()=>[h(r,{label:"卡片名称",prop:"card_name"},{default:b(()=>[h(p,{modelValue:ue.card_name,"onUpdate:modelValue":a[14]||(a[14]=e=>ue.card_name=e),placeholder:"请输入卡片名称"},null,8,["modelValue"])]),_:1}),h(r,{label:"稀有度",prop:"rarity"},{default:b(()=>[h(Qe,{modelValue:ue.rarity,"onUpdate:modelValue":a[15]||(a[15]=e=>ue.rarity=e),min:0,precision:0},null,8,["modelValue"])]),_:1}),h(r,{label:"点数",prop:"point_worth"},{default:b(()=>[h(Qe,{modelValue:ue.point_worth,"onUpdate:modelValue":a[16]||(a[16]=e=>ue.point_worth=e),min:0,precision:0},null,8,["modelValue"])]),_:1}),h(r,{label:"数量",prop:"quantity"},{default:b(()=>[h(Qe,{modelValue:ue.quantity,"onUpdate:modelValue":a[17]||(a[17]=e=>ue.quantity=e),min:0,precision:0},null,8,["modelValue"])]),_:1}),h(r,{label:"状态",prop:"condition"},{default:b(()=>[h(o,{modelValue:ue.condition,"onUpdate:modelValue":a[18]||(a[18]=e=>ue.condition=e),placeholder:"请选择状态"},{default:b(()=>[h(t,{label:"全新",value:"new"}),h(t,{label:"良好",value:"good"}),h(t,{label:"完美",value:"mint"})]),_:1},8,["modelValue"])]),_:1}),h(r,null,{default:b(()=>[h(m,{type:"primary",onClick:Ce},{default:b(()=>a[37]||(a[37]=[C("保存")])),_:1,__:[37]}),h(m,{onClick:a[19]||(a[19]=e=>le.value=!1)},{default:b(()=>a[38]||(a[38]=[C("取消")])),_:1,__:[38]})]),_:1})]),_:1},8,["model"])),[[Re,te.value]])]),_:1},8,["modelValue"]),h(je,{modelValue:de.value,"onUpdate:modelValue":a[23]||(a[23]=e=>de.value=e),title:"修改卡片数量",width:"400px"},{footer:b(()=>[N("span",D,[h(m,{onClick:a[22]||(a[22]=e=>de.value=!1)},{default:b(()=>a[40]||(a[40]=[C("取消")])),_:1,__:[40]}),h(m,{type:"primary",onClick:Ne},{default:b(()=>a[41]||(a[41]=[C("确认")])),_:1,__:[41]})])]),default:b(()=>[h(_,{"label-width":"100px"},{default:b(()=>[h(r,{label:"数量变化"},{default:b(()=>[h(Qe,{modelValue:ne.value,"onUpdate:modelValue":a[21]||(a[21]=e=>ne.value=e),min:-999,max:999,precision:0},null,8,["modelValue"]),a[39]||(a[39]=N("div",{class:"quantity-tip"},"正数表示增加，负数表示减少",-1))]),_:1,__:[39]})]),_:1})]),_:1},8,["modelValue"]),h(je,{modelValue:se.value,"onUpdate:modelValue":a[24]||(a[24]=e=>se.value=e),title:"可合成列表",width:"600px"},{default:b(()=>{var e;return[q((O(),g("div",null,[0===(null==(e=me.value.fusions)?void 0:e.length)?(O(),U(Pe,{key:0,description:"暂无合成配方"})):(O(),U(ze,{key:1,data:me.value.fusions,border:"",stripe:""},{default:b(()=>[h(y,{prop:"fusion_id",label:"合成ID"}),h(y,{prop:"result_card_id",label:"结果卡片ID"}),h(y,{prop:"pack_reference",label:"卡包引用"})]),_:1},8,["data"]))])),[[Re,pe.value]])]}),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-e1b7af86"]]);export{G as default};
