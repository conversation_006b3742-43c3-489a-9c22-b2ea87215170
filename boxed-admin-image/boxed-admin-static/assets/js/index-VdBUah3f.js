import{x as e,r as a,y as t,A as l,P as s,H as p,ag as o,L as i,M as r,z as n}from"./vue-DWGM1jOu.js";import{_ as u}from"./index-Br8T7GvP.js";import"./elementPlus-Bm-E4Nxd.js";const d={class:"points-exchange-products"},c={class:"header"},_=u(e({__name:"index",setup(e){const u=a(""),_=a(1),m=a(10),f=a(0),g=a([]),y=()=>{};return(e,a)=>{const v=o("el-button"),b=o("el-input"),h=o("el-table-column"),k=o("el-tag"),w=o("el-table"),x=o("el-pagination");return n(),t("div",d,[l("div",c,[s(v,{type:"primary",onClick:y},{default:p(()=>a[3]||(a[3]=[i("新增商品")])),_:1,__:[3]}),s(b,{modelValue:u.value,"onUpdate:modelValue":a[0]||(a[0]=e=>u.value=e),placeholder:"请输入商品名称搜索",style:{width:"200px"},clearable:""},null,8,["modelValue"])]),s(w,{data:g.value,border:"",style:{width:"100%"}},{default:p(()=>[s(h,{prop:"id",label:"ID",width:"80"}),s(h,{prop:"name",label:"商品名称"}),s(h,{prop:"points",label:"所需积分",width:"120"}),s(h,{prop:"stock",label:"库存数量",width:"120"}),s(h,{prop:"status",label:"状态",width:"100"},{default:p(({row:e})=>[s(k,{type:1===e.status?"success":"info"},{default:p(()=>[i(r(1===e.status?"上架":"下架"),1)]),_:2},1032,["type"])]),_:1}),s(h,{label:"操作",width:"200",fixed:"right"},{default:p(({row:e})=>[s(v,{link:"",type:"primary",onClick:e=>{}},{default:p(()=>a[4]||(a[4]=[i("编辑")])),_:2,__:[4]},1032,["onClick"]),s(v,{link:"",type:"primary",onClick:e=>{}},{default:p(()=>a[5]||(a[5]=[i("库存")])),_:2,__:[5]},1032,["onClick"]),s(v,{link:"",type:1===e.status?"danger":"primary",onClick:e=>{}},{default:p(()=>[i(r(1===e.status?"下架":"上架"),1)]),_:2},1032,["type","onClick"])]),_:1})]),_:1},8,["data"]),s(x,{"current-page":_.value,"onUpdate:currentPage":a[1]||(a[1]=e=>_.value=e),"page-size":m.value,"onUpdate:pageSize":a[2]||(a[2]=e=>m.value=e),total:f.value,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next",class:"pagination"},null,8,["current-page","page-size","total"])])}}}),[["__scopeId","data-v-48fdf641"]]);export{_ as default};
