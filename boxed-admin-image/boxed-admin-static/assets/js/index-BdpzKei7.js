import{d as e}from"./elementPlus-Bm-E4Nxd.js";import{x as a,r as l,y as t,P as d,H as n,ag as o,L as s,u as i,A as r,z as c}from"./vue-DWGM1jOu.js";import{_ as u}from"./index-Br8T7GvP.js";const p={class:"roles-container"},_={class:"card-header"},m=u(a({__name:"index",setup(a){const u=l([{id:1,name:"超级管理员",code:"SUPER_ADMIN",description:"系统最高权限角色"},{id:2,name:"普通管理员",code:"ADMIN",description:"一般管理权限"}]),m=()=>{};return(a,l)=>{const f=o("el-button"),k=o("el-table-column"),b=o("el-icon"),y=o("el-button-group"),C=o("el-table"),h=o("el-card");return c(),t("div",p,[d(h,null,{header:n(()=>[r("div",_,[l[1]||(l[1]=r("span",null,"角色列表",-1)),d(f,{type:"primary",onClick:m},{default:n(()=>l[0]||(l[0]=[s("新增角色")])),_:1,__:[0]})])]),default:n(()=>[d(C,{data:u.value,style:{width:"100%"}},{default:n(()=>[d(k,{prop:"id",label:"ID",width:"80"}),d(k,{prop:"name",label:"角色名称"}),d(k,{prop:"code",label:"角色标识"}),d(k,{prop:"description",label:"描述"}),d(k,{label:"操作",width:"250"},{default:n(({row:a})=>[d(y,null,{default:n(()=>[d(f,{type:"primary",link:"",onClick:e=>{}},{default:n(()=>l[2]||(l[2]=[s("编辑")])),_:2,__:[2]},1032,["onClick"]),d(f,{type:"success",link:"",onClick:e=>{}},{default:n(()=>l[3]||(l[3]=[s("权限设置")])),_:2,__:[3]},1032,["onClick"]),d(f,{type:"danger",link:"",onClick:e=>{}},{default:n(()=>[d(b,null,{default:n(()=>[d(i(e))]),_:1}),l[4]||(l[4]=s(" 删除 "))]),_:2,__:[4]},1032,["onClick"])]),_:2},1024)]),_:1})]),_:1},8,["data"])]),_:1})])}}}),[["__scopeId","data-v-ea4adcda"]]);export{m as default};
