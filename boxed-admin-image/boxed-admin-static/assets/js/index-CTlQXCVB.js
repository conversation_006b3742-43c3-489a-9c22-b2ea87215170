import{d as a}from"./elementPlus-Bm-E4Nxd.js";import{x as e,r as l,y as t,P as s,H as n,ag as r,A as u,L as i,M as o,u as d,z as p}from"./vue-DWGM1jOu.js";import{_ as c}from"./index-Br8T7GvP.js";const _={class:"users-container"},m={class:"card-header"},g={class:"pagination-container"},v=c(e({__name:"index",setup(e){const c=l([{id:1,username:"admin",email:"<EMAIL>",role:"管理员",status:"active"}]),v=l(1),f=l(10),b=l(100),y=()=>{};return(e,l)=>{const k=r("el-button"),x=r("el-table-column"),z=r("el-tag"),h=r("el-icon"),w=r("el-button-group"),C=r("el-table"),j=r("el-pagination"),P=r("el-card");return p(),t("div",_,[s(P,null,{header:n(()=>[u("div",m,[l[3]||(l[3]=u("span",null,"用户列表",-1)),s(k,{type:"primary",onClick:y},{default:n(()=>l[2]||(l[2]=[i("新增用户")])),_:1,__:[2]})])]),default:n(()=>[s(C,{data:c.value,style:{width:"100%"}},{default:n(()=>[s(x,{prop:"id",label:"ID",width:"80"}),s(x,{prop:"username",label:"用户名"}),s(x,{prop:"email",label:"邮箱"}),s(x,{prop:"role",label:"角色"}),s(x,{prop:"status",label:"状态"},{default:n(({row:a})=>[s(z,{type:"active"===a.status?"success":"danger"},{default:n(()=>[i(o("active"===a.status?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),s(x,{label:"操作",width:"200"},{default:n(({row:e})=>[s(w,null,{default:n(()=>[s(k,{type:"primary",link:"",onClick:a=>{}},{default:n(()=>l[4]||(l[4]=[i("编辑")])),_:2,__:[4]},1032,["onClick"]),s(k,{type:"danger",link:"",onClick:a=>{}},{default:n(()=>[s(h,null,{default:n(()=>[s(d(a))]),_:1}),l[5]||(l[5]=i(" 删除 "))]),_:2,__:[5]},1032,["onClick"])]),_:2},1024)]),_:1})]),_:1},8,["data"]),u("div",g,[s(j,{"current-page":v.value,"onUpdate:currentPage":l[0]||(l[0]=a=>v.value=a),"page-size":f.value,"onUpdate:pageSize":l[1]||(l[1]=a=>f.value=a),total:b.value,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next"},null,8,["current-page","page-size","total"])])]),_:1})])}}}),[["__scopeId","data-v-9d66da9a"]]);export{v as default};
