import{x as e,r as l,y as a,A as t,P as u,ag as o,H as d,L as n,M as i,z as r}from"./vue-DWGM1jOu.js";import{_ as p}from"./index-Br8T7GvP.js";import"./elementPlus-Bm-E4Nxd.js";const m={class:"points-exchange-inventory"},s={class:"header"},v=p(e({__name:"index",setup(e){const p=l(""),v=l(1),_=l(10),c=l(0),f=l([]),b=l(!1),g=l(null),w=l({amount:0,reason:""}),h=l(!1),V=l({warning:0}),y=()=>{},x=()=>{};return(e,l)=>{const k=o("el-input"),C=o("el-table-column"),U=o("el-tag"),z=o("el-button"),j=o("el-table"),P=o("el-pagination"),I=o("el-form-item"),A=o("el-input-number"),D=o("el-form"),H=o("el-dialog");return r(),a("div",m,[t("div",s,[u(k,{modelValue:p.value,"onUpdate:modelValue":l[0]||(l[0]=e=>p.value=e),placeholder:"请输入商品名称搜索",style:{width:"200px"},clearable:""},null,8,["modelValue"])]),u(j,{data:f.value,border:"",style:{width:"100%"}},{default:d(()=>[u(C,{prop:"id",label:"ID",width:"80"}),u(C,{prop:"name",label:"商品名称"}),u(C,{prop:"total",label:"总库存",width:"100"}),u(C,{prop:"available",label:"可用库存",width:"100"}),u(C,{prop:"locked",label:"锁定库存",width:"100"}),u(C,{prop:"warning",label:"库存预警",width:"100"},{default:d(({row:e})=>[u(U,{type:e.available<=e.warning?"danger":"success"},{default:d(()=>[n(i(e.warning),1)]),_:2},1032,["type"])]),_:1}),u(C,{label:"操作",width:"180",fixed:"right"},{default:d(({row:e})=>[u(z,{link:"",type:"primary",onClick:l=>(e=>{g.value=e,b.value=!0})(e)},{default:d(()=>l[10]||(l[10]=[n("调整库存")])),_:2,__:[10]},1032,["onClick"]),u(z,{link:"",type:"primary",onClick:l=>(e=>{g.value=e,V.value.warning=e.warning,h.value=!0})(e)},{default:d(()=>l[11]||(l[11]=[n("设置预警")])),_:2,__:[11]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),u(P,{"current-page":v.value,"onUpdate:currentPage":l[1]||(l[1]=e=>v.value=e),"page-size":_.value,"onUpdate:pageSize":l[2]||(l[2]=e=>_.value=e),total:c.value,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next",class:"pagination"},null,8,["current-page","page-size","total"]),u(H,{modelValue:b.value,"onUpdate:modelValue":l[6]||(l[6]=e=>b.value=e),title:"调整库存",width:"500px"},{footer:d(()=>[u(z,{onClick:l[5]||(l[5]=e=>b.value=!1)},{default:d(()=>l[13]||(l[13]=[n("取消")])),_:1,__:[13]}),u(z,{type:"primary",onClick:y},{default:d(()=>l[14]||(l[14]=[n("确认")])),_:1,__:[14]})]),default:d(()=>[u(D,{model:w.value,"label-width":"100px"},{default:d(()=>[u(I,{label:"当前库存"},{default:d(()=>{var e;return[t("span",null,i((null==(e=g.value)?void 0:e.available)||0),1)]}),_:1}),u(I,{label:"调整数量"},{default:d(()=>{var e;return[u(A,{modelValue:w.value.amount,"onUpdate:modelValue":l[3]||(l[3]=e=>w.value.amount=e),min:-(null==(e=g.value)?void 0:e.available),placeholder:"请输入调整数量"},null,8,["modelValue","min"]),l[12]||(l[12]=t("div",{class:"tip"},"正数为入库，负数为出库",-1))]}),_:1,__:[12]}),u(I,{label:"调整原因"},{default:d(()=>[u(k,{modelValue:w.value.reason,"onUpdate:modelValue":l[4]||(l[4]=e=>w.value.reason=e),type:"textarea",placeholder:"请输入调整原因"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),u(H,{modelValue:h.value,"onUpdate:modelValue":l[9]||(l[9]=e=>h.value=e),title:"设置库存预警",width:"500px"},{footer:d(()=>[u(z,{onClick:l[8]||(l[8]=e=>h.value=!1)},{default:d(()=>l[15]||(l[15]=[n("取消")])),_:1,__:[15]}),u(z,{type:"primary",onClick:x},{default:d(()=>l[16]||(l[16]=[n("确认")])),_:1,__:[16]})]),default:d(()=>[u(D,{model:V.value,"label-width":"100px"},{default:d(()=>[u(I,{label:"预警数量"},{default:d(()=>[u(A,{modelValue:V.value.warning,"onUpdate:modelValue":l[7]||(l[7]=e=>V.value.warning=e),min:0,placeholder:"请输入预警数量"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-f7276bc9"]]);export{v as default};
