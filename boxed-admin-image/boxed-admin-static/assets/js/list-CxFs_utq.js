import{x as e,X as a,r as l,y as t,P as u,H as r,ag as d,L as o,I as n,A as s,aq as p,G as i,z as m}from"./vue-DWGM1jOu.js";import{_ as c}from"./index-Br8T7GvP.js";import"./elementPlus-Bm-E4Nxd.js";const _={class:"rankings-container"},b={class:"pagination-container"},f=c(e({__name:"list",setup(e){const c=a({userId:"",username:"",type:""}),f=l([]),v=l(!1),g=l(1),h=l(10),y=l(0),I=()=>{v.value=!0,setTimeout(()=>{v.value=!1},1e3)},V=()=>{c.userId="",c.username="",c.type="",I()},k=e=>{h.value=e,I()},w=e=>{g.value=e,I()};return I(),(e,a)=>{const l=d("el-input"),z=d("el-form-item"),C=d("el-option"),x=d("el-select"),U=d("el-button"),j=d("el-form"),D=d("el-card"),P=d("el-table-column"),S=d("el-table"),T=d("el-pagination"),q=p("loading");return m(),t("div",_,[u(D,{class:"search-card"},{default:r(()=>[u(j,{model:c,inline:""},{default:r(()=>[u(z,{label:"用户ID"},{default:r(()=>[u(l,{modelValue:c.userId,"onUpdate:modelValue":a[0]||(a[0]=e=>c.userId=e),placeholder:"请输入用户ID",clearable:""},null,8,["modelValue"])]),_:1}),u(z,{label:"用户名"},{default:r(()=>[u(l,{modelValue:c.username,"onUpdate:modelValue":a[1]||(a[1]=e=>c.username=e),placeholder:"请输入用户名",clearable:""},null,8,["modelValue"])]),_:1}),u(z,{label:"排名类型"},{default:r(()=>[u(x,{modelValue:c.type,"onUpdate:modelValue":a[2]||(a[2]=e=>c.type=e),placeholder:"请选择排名类型",clearable:""},{default:r(()=>[u(C,{label:"积分排名",value:"points"}),u(C,{label:"等级排名",value:"rank"})]),_:1},8,["modelValue"])]),_:1}),u(z,null,{default:r(()=>[u(U,{type:"primary",onClick:I},{default:r(()=>a[5]||(a[5]=[o("查询")])),_:1,__:[5]}),u(U,{onClick:V},{default:r(()=>a[6]||(a[6]=[o("重置")])),_:1,__:[6]})]),_:1})]),_:1},8,["model"])]),_:1}),u(D,{class:"table-card"},{default:r(()=>[n((m(),i(S,{data:f.value,border:"",stripe:""},{default:r(()=>[u(P,{type:"index",label:"序号",width:"80"}),u(P,{prop:"userId",label:"用户ID",width:"120"}),u(P,{prop:"username",label:"用户名",width:"150"}),u(P,{prop:"points",label:"积分",width:"120"}),u(P,{prop:"rank",label:"等级",width:"120"}),u(P,{prop:"updateTime",label:"更新时间",width:"180"}),u(P,{label:"操作",fixed:"right",width:"120"},{default:r(({row:e})=>[u(U,{link:"",type:"primary",onClick:e=>{}},{default:r(()=>a[7]||(a[7]=[o("查看详情")])),_:2,__:[7]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[q,v.value]]),s("div",b,[u(T,{"current-page":g.value,"onUpdate:currentPage":a[3]||(a[3]=e=>g.value=e),"page-size":h.value,"onUpdate:pageSize":a[4]||(a[4]=e=>h.value=e),"page-sizes":[10,20,50,100],total:y.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:k,onCurrentChange:w},null,8,["current-page","page-size","total"])])]),_:1})])}}}),[["__scopeId","data-v-7a64e03f"]]);export{f as default};
