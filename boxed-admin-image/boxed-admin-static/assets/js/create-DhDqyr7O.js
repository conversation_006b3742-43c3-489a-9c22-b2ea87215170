import{x as e,r as a,h as l,y as u,P as o,H as t,ag as r,aw as i,O as s,a6 as n,G as c,u as d,L as p,A as m,aB as _,z as v}from"./vue-DWGM1jOu.js";import{E as f,w as g}from"./elementPlus-Bm-E4Nxd.js";import{getPackCollections as y,createPack as k}from"./packs-CZud9J-M.js";import{_ as V}from"./index-Br8T7GvP.js";import"./request-ZE4ZUch8.js";const h={class:"create-pack-container"},w={class:"card-header"},b=["src"],x=V(e({__name:"create",setup(e){const V=_(),x=i(),q=a(),U=a(!1),I=a([]),j=a(""),C=a(null),R=a({pack_name:"",collection_id:"",price:0,win_rate:0,max_win:0,popularity:"",image_file:""}),A=a({pack_name:[{required:!0,message:"请输入卡包名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],collection_id:[{required:!0,message:"请选择卡包集合",trigger:"change"}],price:[{required:!0,message:"请输入价格",trigger:"blur"}]}),F=e=>{if(C.value=e.raw,C.value){const e=new FileReader;e.onload=e=>{var a;j.value=null==(a=e.target)?void 0:a.result;const l=j.value;R.value.image_file=l},e.readAsDataURL(C.value)}},L=async()=>{q.value&&await q.value.validate(async e=>{if(e){U.value=!0;try{await k(R.value),f.success("创建卡包成功");const e=x.query.collectionId;e?V.push(`/storage/packs?collectionId=${e}`):V.push("/storage/packs")}catch(a){f.error("创建卡包失败")}finally{U.value=!1}}})},P=()=>{q.value&&(q.value.resetFields(),j.value="",C.value=null)},S=()=>{const e=x.query.collectionId;e?V.push(`/storage/packs?collectionId=${e}`):V.push("/storage/packs")};return l(()=>{(async()=>{try{I.value=await y();const e=x.query.collectionId;e?R.value.collection_id=e:I.value.length>0&&(R.value.collection_id=I.value[0].id.toString())}catch(e){f.error("获取卡包集合失败")}})()}),(e,a)=>{const l=r("el-button"),i=r("el-input"),_=r("el-form-item"),f=r("el-option"),y=r("el-select"),k=r("el-input-number"),V=r("el-icon"),x=r("el-upload"),C=r("el-form"),$=r("el-card");return v(),u("div",h,[o($,{class:"box-card"},{header:t(()=>[m("div",w,[a[7]||(a[7]=m("span",null,"创建卡包",-1)),o(l,{onClick:S},{default:t(()=>a[6]||(a[6]=[p("返回")])),_:1,__:[6]})])]),default:t(()=>[o(C,{ref_key:"formRef",ref:q,model:R.value,rules:A.value,"label-width":"120px",class:"create-form"},{default:t(()=>[o(_,{label:"卡包名称",prop:"pack_name"},{default:t(()=>[o(i,{modelValue:R.value.pack_name,"onUpdate:modelValue":a[0]||(a[0]=e=>R.value.pack_name=e),placeholder:"请输入卡包名称"},null,8,["modelValue"])]),_:1}),o(_,{label:"卡包集合",prop:"collection_id"},{default:t(()=>[o(y,{modelValue:R.value.collection_id,"onUpdate:modelValue":a[1]||(a[1]=e=>R.value.collection_id=e),placeholder:"请选择卡包集合"},{default:t(()=>[(v(!0),u(s,null,n(I.value,e=>(v(),c(f,{key:e.id,label:e.name,value:e.id.toString()},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(_,{label:"价格",prop:"price"},{default:t(()=>[o(k,{modelValue:R.value.price,"onUpdate:modelValue":a[2]||(a[2]=e=>R.value.price=e),min:0,precision:2,step:.1},null,8,["modelValue"])]),_:1}),o(_,{label:"中奖率",prop:"win_rate"},{default:t(()=>[o(k,{modelValue:R.value.win_rate,"onUpdate:modelValue":a[3]||(a[3]=e=>R.value.win_rate=e),min:0,max:100,precision:2,step:.1},null,8,["modelValue"])]),_:1}),o(_,{label:"最大中奖金额",prop:"max_win"},{default:t(()=>[o(k,{modelValue:R.value.max_win,"onUpdate:modelValue":a[4]||(a[4]=e=>R.value.max_win=e),min:0,precision:2,step:.1},null,8,["modelValue"])]),_:1}),o(_,{label:"热度",prop:"popularity"},{default:t(()=>[o(i,{modelValue:R.value.popularity,"onUpdate:modelValue":a[5]||(a[5]=e=>R.value.popularity=e),placeholder:"请输入热度"},null,8,["modelValue"])]),_:1}),o(_,{label:"卡包图片",prop:"image_file"},{default:t(()=>[o(x,{class:"avatar-uploader",action:"","show-file-list":!1,"auto-upload":!1,"on-change":F},{default:t(()=>[j.value?(v(),u("img",{key:0,src:j.value,class:"avatar"},null,8,b)):(v(),c(V,{key:1,class:"avatar-uploader-icon"},{default:t(()=>[o(d(g))]),_:1}))]),_:1})]),_:1}),o(_,null,{default:t(()=>[o(l,{type:"primary",onClick:L,loading:U.value},{default:t(()=>a[8]||(a[8]=[p("创建")])),_:1,__:[8]},8,["loading"]),o(l,{onClick:P},{default:t(()=>a[9]||(a[9]=[p("重置")])),_:1,__:[9]})]),_:1})]),_:1},8,["model","rules"])]),_:1})])}}}),[["__scopeId","data-v-8ce5ca52"]]);export{x as default};
