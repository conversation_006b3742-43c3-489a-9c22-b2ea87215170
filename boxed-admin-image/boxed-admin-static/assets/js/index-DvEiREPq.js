import{E as e,d as a,w as l}from"./elementPlus-Bm-E4Nxd.js";import{r as o}from"./request-ZE4ZUch8.js";import{x as t,X as r,r as i,h as n,a3 as d,Y as u,y as c,P as s,H as p,ag as m,L as y,A as v,I as _,aq as h,G as f,J as b,M as g,O as w,a6 as k,u as V,z as C}from"./vue-DWGM1jOu.js";import{_ as U}from"./index-Br8T7GvP.js";const x={class:"achievements-container"},T={class:"header-actions"},A={key:0},z={key:1},N={key:2},W={key:3},B={key:4},I={key:5},S={key:6},j={key:0},q={key:0},D={key:1},R={class:"pagination-container"},F=["src"],M=["src"],G={class:"dialog-footer"},K=U(t({name:"AchievementsIndex",__name:"index",setup(t){const U={1:"Common(普通)",2:"Uncommon(非普通)",3:"Rare(稀有)",4:"Epic(史诗)",5:"Legendary(传奇)",6:"Mythic(神话)",7:"Unique(唯一)"},K=r({conditionType:"",sortBy:"",sortDirection:"desc"}),L=i([]),H=i(!1),J=i(1),O=i(10),P=i(0),E=i(!1),$=i(!1),X=i(),Y=r({name:"",description:"",condition:{type:"level_reached",target:1,rarity:1},reward:[{type:"point",amount:100}],rarity:1,rank:1}),Q={name:[{required:!0,message:"请输入成就名称",trigger:"blur"}],description:[{required:!0,message:"请输入成就描述",trigger:"blur"}],"condition.type":[{required:!0,message:"请选择条件类型",trigger:"change"}],"condition.target":[{required:!0,message:"请输入目标值",trigger:"blur"}]},Z=i(!1),ee=i(!1),ae=i(),le=i(null),oe=r({id:"",name:"",description:"",condition:{type:"",target:1,rarity:1},reward:[],rarity:1,rank:1}),te={name:[{required:!0,message:"请输入成就名称",trigger:"blur"}],description:[{required:!0,message:"请输入成就描述",trigger:"blur"}],"condition.type":[{required:!0,message:"请选择条件类型",trigger:"change"}],"condition.target":[{required:!0,message:"请输入目标值",trigger:"blur"}]},re=i(!1),ie=i(null),ne=async()=>{H.value=!0;try{const e={page:J.value,size:O.value};K.conditionType&&(e.condition_type=K.conditionType),K.sortBy&&(e.sort_by=K.sortBy,e.sort_direction=K.sortDirection);const a=await(async e=>o.get("/achievements/",{params:e,headers:{Accept:"application/json",Referer:"https://backend-769075815684.us-central1.run.app/gacha/api/v1/docs","sec-ch-ua":'"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',"sec-ch-ua-mobile":"?0","sec-ch-ua-platform":'"Windows"',"User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}))(e);L.value=a.items,P.value=a.total}catch(a){e.error("获取成就列表失败")}finally{H.value=!1}},de=()=>{K.conditionType="",K.sortBy="",K.sortDirection="desc",J.value=1,ne()},ue=()=>{J.value=1,ne()},ce=e=>{O.value=e,ne()},se=e=>{J.value=e,ne()},pe=()=>{ve(),E.value=!0},me=()=>{const e={type:"point",emblemId:void 0,url:void 0,image:void 0,amount:100};Y.reward.push(e)},ye=async()=>{X.value&&await X.value.validate(async a=>{if(!a)return!1;$.value=!0;try{await(async e=>{const a={...e};if(a.condition)if("draw_by_rarity"===a.condition.type)void 0===a.condition.rarity&&(a.condition={...a.condition,rarity:0});else{const{rarity:e,...l}=a.condition;a.condition=l}return void 0===a.rarity&&(a.rarity=0),o.post("/achievements/",a,{headers:{"Content-Type":"application/json",Accept:"application/json",Referer:"https://backend-769075815684.us-central1.run.app/gacha/api/v1/docs","sec-ch-ua":'"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',"sec-ch-ua-mobile":"?0","sec-ch-ua-platform":'"Windows"',"User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}})})(Y),e.success("添加成就成功"),E.value=!1,ve(),ne()}catch(l){e.error("添加成就失败")}finally{$.value=!1}})},ve=()=>{X.value&&(X.value.resetFields(),Y.name="",Y.description="",Y.condition={type:"level_reached",target:1,rarity:""},Y.reward=[{type:"point",amount:100,emblemId:void 0,url:void 0,image:void 0}],Y.rarity="",Y.rank=1)},_e=()=>{var e;const a={type:"point",emblemId:void 0,url:void 0,image:void 0,amount:100};null==(e=oe.reward)||e.push(a)},he=async()=>{ae.value&&le.value&&await ae.value.validate(async a=>{if(!a)return!1;ee.value=!0;try{const{id:a,...l}=oe;await(async(e,a)=>{const l={...a};if(l.condition)if("draw_by_rarity"===l.condition.type)void 0===l.condition.rarity&&(l.condition={...l.condition,rarity:0});else{const{rarity:e,...a}=l.condition;l.condition=a}return void 0===l.rarity&&(l.rarity=0),o.put(`/achievements/${e}`,l,{headers:{"Content-Type":"application/json",Accept:"application/json",Referer:"https://backend-769075815684.us-central1.run.app/gacha/api/v1/docs","sec-ch-ua":'"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',"sec-ch-ua-mobile":"?0","sec-ch-ua-platform":'"Windows"',"User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}})})(a,l),e.success("更新成就成功"),Z.value=!1,ne()}catch(l){e.error("更新成就失败")}finally{ee.value=!1}})},fe=async()=>{if(ie.value)try{await(async e=>o.delete(`/achievements/${e}`,{headers:{"Content-Type":"application/json",Accept:"application/json",Referer:"https://backend-769075815684.us-central1.run.app/gacha/api/v1/docs","sec-ch-ua":'"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',"sec-ch-ua-mobile":"?0","sec-ch-ua-platform":'"Windows"',"User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}))(ie.value.id),e.success("删除成就成功"),re.value=!1,ne()}catch(a){e.error("删除成就失败")}};return n(()=>{ne()}),d(()=>{localStorage.setItem("achievements_state",JSON.stringify({searchForm:{conditionType:K.conditionType,sortBy:K.sortBy,sortDirection:K.sortDirection},pagination:{currentPage:J.value,pageSize:O.value}}))}),u(()=>{const e=localStorage.getItem("achievements_state");if(e){const a=JSON.parse(e);a.searchForm&&(K.conditionType=a.searchForm.conditionType,K.sortBy=a.searchForm.sortBy,K.sortDirection=a.searchForm.sortDirection),a.pagination&&(J.value=a.pagination.currentPage,O.value=a.pagination.pageSize)}ne()}),(e,o)=>{const t=m("el-option"),r=m("el-select"),i=m("el-form-item"),n=m("el-button"),d=m("el-form"),u=m("el-card"),ne=m("el-table-column"),be=m("el-image"),ge=m("el-icon"),we=m("el-table"),ke=m("el-pagination"),Ve=m("el-input"),Ce=m("el-input-number"),Ue=m("el-upload"),xe=m("el-drawer"),Te=m("el-dialog"),Ae=h("loading");return C(),c("div",x,[s(u,{class:"search-card"},{default:p(()=>[s(d,{ref:"searchFormRef",inline:""},{default:p(()=>[s(i,{label:"条件类型",prop:"conditionType"},{default:p(()=>[s(r,{modelValue:K.conditionType,"onUpdate:modelValue":o[0]||(o[0]=e=>K.conditionType=e),placeholder:"请选择条件类型",clearable:"",style:{width:"150px"}},{default:p(()=>[s(t,{label:"等级达成",value:"level_reached"}),s(t,{label:"融合达成",value:"fusion_reached"}),s(t,{label:"稀有度抽取",value:"draw_by_rarity"}),s(t,{label:"完成交易",value:"buy_deal_reached"}),s(t,{label:"完成销售",value:"sell_deal_reached"}),s(t,{label:"抽卡消费积分",value:"draw_spend_points"})]),_:1},8,["modelValue"])]),_:1}),s(i,{label:"排序字段",prop:"sortBy"},{default:p(()=>[s(r,{modelValue:K.sortBy,"onUpdate:modelValue":o[1]||(o[1]=e=>K.sortBy=e),placeholder:"请选择排序字段",style:{width:"150px"},clearable:""},{default:p(()=>[s(t,{label:"等级",value:"rank"}),s(t,{label:"稀有度",value:"rarity"}),s(t,{label:"创建时间",value:"created_at"})]),_:1},8,["modelValue"])]),_:1}),s(i,{label:"排序方式",prop:"sortDirection"},{default:p(()=>[s(r,{modelValue:K.sortDirection,"onUpdate:modelValue":o[2]||(o[2]=e=>K.sortDirection=e),placeholder:"请选择排序方式",style:{width:"150px"},clearable:""},{default:p(()=>[s(t,{label:"升序",value:"asc"}),s(t,{label:"降序",value:"desc"})]),_:1},8,["modelValue"])]),_:1}),s(i,null,{default:p(()=>[s(n,{type:"primary",onClick:ue},{default:p(()=>o[24]||(o[24]=[y("查询")])),_:1,__:[24]}),s(n,{onClick:de},{default:p(()=>o[25]||(o[25]=[y("重置")])),_:1,__:[25]})]),_:1})]),_:1},512)]),_:1}),s(u,{class:"table-card"},{default:p(()=>[v("div",T,[s(n,{type:"primary",onClick:pe},{default:p(()=>o[26]||(o[26]=[y("添加成就")])),_:1,__:[26]})]),_((C(),f(we,{data:L.value,border:"",stripe:""},{default:p(()=>[s(ne,{prop:"name",label:"成就名称"}),s(ne,{prop:"description",label:"描述"}),s(ne,{label:"条件",width:"180"},{default:p(({row:e})=>[v("div",null,[o[27]||(o[27]=y("类型: ")),"level_reached"===e.condition.type?(C(),c("span",A,"等级达成")):"fusion_reached"===e.condition.type?(C(),c("span",z,"融合达成")):"draw_by_rarity"===e.condition.type?(C(),c("span",N,"稀有度抽取")):"buy_deal_reached"===e.condition.type?(C(),c("span",W,"完成交易")):"sell_deal_reached"===e.condition.type?(C(),c("span",B,"完成销售")):"draw_spend_points"===e.condition.type?(C(),c("span",I,"抽卡消费积分")):(C(),c("span",S,g(e.condition.type),1))]),v("div",null,"目标值: "+g(e.condition.target),1),"draw_by_rarity"===e.condition.type&&void 0!==e.condition.rarity?(C(),c("div",j,[o[28]||(o[28]=y(" 稀有度达成条件: ")),v("span",null,g(U[e.condition.rarity]||e.condition.rarity),1)])):b("",!0)]),_:1}),s(ne,{label:"奖励",width:"180"},{default:p(({row:e})=>[(C(!0),c(w,null,k(e.reward,(e,a)=>(C(),c("div",{key:a},["point"===e.type?(C(),c("div",q," 积分: "+g(e.amount),1)):"emblem"===e.type?(C(),c("div",D,[o[29]||(o[29]=y(" 徽章: ")),e.image?(C(),f(be,{key:0,src:e.image,style:{width:"30px",height:"30px"},"preview-src-list":[e.image]},null,8,["src","preview-src-list"])):e.url?(C(),f(be,{key:1,src:e.url,style:{width:"30px",height:"30px"},"preview-src-list":[e.url]},null,8,["src","preview-src-list"])):b("",!0)])):b("",!0)]))),128))]),_:1}),s(ne,{label:"稀有度"},{default:p(({row:e})=>[v("span",null,g(U[e.rarity]||e.rarity),1)]),_:1}),s(ne,{prop:"rank",label:"等级"}),s(ne,{label:"操作",width:"200",fixed:"right"},{default:p(({row:e})=>[s(n,{link:"",type:"primary",onClick:a=>(async e=>{le.value=e,oe.id=e.id,oe.name=e.name,oe.description=e.description,oe.condition={...e.condition},oe.reward=JSON.parse(JSON.stringify(e.reward)),oe.rarity=e.rarity||"",oe.rank=e.rank||1,Z.value=!0})(e)},{default:p(()=>o[30]||(o[30]=[y("编辑")])),_:2,__:[30]},1032,["onClick"]),s(n,{link:"",type:"danger",onClick:a=>(e=>{ie.value=e,re.value=!0})(e)},{default:p(()=>[s(ge,null,{default:p(()=>[s(V(a))]),_:1}),o[31]||(o[31]=y(" 删除 "))]),_:2,__:[31]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[Ae,H.value]]),v("div",R,[s(ke,{"current-page":J.value,"onUpdate:currentPage":o[3]||(o[3]=e=>J.value=e),"page-size":O.value,"onUpdate:pageSize":o[4]||(o[4]=e=>O.value=e),"page-sizes":[10,20,50,100],total:P.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:ce,onCurrentChange:se},null,8,["current-page","page-size","total"])])]),_:1}),s(xe,{modelValue:E.value,"onUpdate:modelValue":o[12]||(o[12]=e=>E.value=e),title:"添加成就",size:"50%","destroy-on-close":!1},{default:p(()=>[_((C(),f(d,{ref_key:"addFormRef",ref:X,model:Y,rules:Q,"label-width":"120px"},{default:p(()=>[s(i,{label:"成就名称",prop:"name"},{default:p(()=>[s(Ve,{modelValue:Y.name,"onUpdate:modelValue":o[5]||(o[5]=e=>Y.name=e),placeholder:"请输入成就名称"},null,8,["modelValue"])]),_:1}),s(i,{label:"描述",prop:"description"},{default:p(()=>[s(Ve,{modelValue:Y.description,"onUpdate:modelValue":o[6]||(o[6]=e=>Y.description=e),placeholder:"请输入成就描述",type:"textarea",rows:3},null,8,["modelValue"])]),_:1}),s(i,{label:"条件类型",prop:"condition.type"},{default:p(()=>[s(r,{modelValue:Y.condition.type,"onUpdate:modelValue":o[7]||(o[7]=e=>Y.condition.type=e),placeholder:"请选择条件类型"},{default:p(()=>[s(t,{label:"等级达成",value:"level_reached"}),s(t,{label:"融合达成",value:"fusion_reached"}),s(t,{label:"稀有度抽取",value:"draw_by_rarity"}),s(t,{label:"完成交易",value:"buy_deal_reached"}),s(t,{label:"完成销售",value:"sell_deal_reached"}),s(t,{label:"抽卡消费积分",value:"draw_spend_points"})]),_:1},8,["modelValue"])]),_:1}),s(i,{label:"目标值",prop:"condition.target"},{default:p(()=>[s(Ce,{modelValue:Y.condition.target,"onUpdate:modelValue":o[8]||(o[8]=e=>Y.condition.target=e),min:1,precision:0},null,8,["modelValue"])]),_:1}),"draw_by_rarity"===Y.condition.type?(C(),f(i,{key:0,label:"稀有度达成条件",prop:"condition.rarity"},{default:p(()=>[s(r,{modelValue:Y.condition.rarity,"onUpdate:modelValue":o[9]||(o[9]=e=>Y.condition.rarity=e),placeholder:"请选择稀有度达成条件"},{default:p(()=>[(C(),c(w,null,k(U,(e,a)=>s(t,{key:a,label:e,value:Number(a)},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1})):b("",!0),s(i,{label:"奖励"},{default:p(()=>[(C(!0),c(w,null,k(Y.reward,(e,i)=>(C(),c("div",{key:i,class:"reward-item"},[s(r,{modelValue:e.type,"onUpdate:modelValue":a=>e.type=a,placeholder:"奖励类型",style:{width:"120px"},onChange:e=>((e,a)=>{"point"===e?(Y.reward[a].amount=100,Y.reward[a].emblemId=void 0,Y.reward[a].url=void 0,Y.reward[a].image=void 0):"emblem"===e&&(Y.reward[a].amount=void 0,Y.reward[a].emblemId=void 0,Y.reward[a].url=void 0,Y.reward[a].image=void 0)})(e,i)},{default:p(()=>[s(t,{label:"积分",value:"point"}),s(t,{label:"徽章",value:"emblem"})]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"]),"point"===e.type?(C(),f(Ce,{key:0,modelValue:e.amount,"onUpdate:modelValue":a=>e.amount=a,min:1,precision:0,placeholder:"积分数量",style:{"margin-left":"10px"}},null,8,["modelValue","onUpdate:modelValue"])):"emblem"===e.type?(C(),f(Ue,{key:1,class:"avatar-uploader",action:"","auto-upload":!1,"show-file-list":!1,"on-change":e=>((e,a)=>{const l=new FileReader;l.readAsDataURL(e.raw),l.onload=()=>{Y.reward[a].image=l.result,Y.reward[a].emblemId=void 0,Y.reward[a].url=void 0,Y.reward[a].amount=void 0}})(e,i),style:{"margin-left":"10px"}},{default:p(()=>[e.image?(C(),c("img",{key:0,src:e.image,class:"avatar"},null,8,F)):(C(),f(ge,{key:1,class:"avatar-uploader-icon"},{default:p(()=>[s(V(l))]),_:1})),o[32]||(o[32]=v("div",{class:"el-upload__text"},"点击上传徽章图片",-1))]),_:2,__:[32]},1032,["on-change"])):b("",!0),s(n,{type:"danger",circle:"",onClick:e=>(e=>{Y.reward.splice(e,1)})(i),style:{"margin-left":"10px"}},{default:p(()=>[s(ge,null,{default:p(()=>[s(V(a))]),_:1})]),_:2},1032,["onClick"])]))),128)),s(n,{type:"primary",onClick:me},{default:p(()=>o[33]||(o[33]=[y("添加奖励")])),_:1,__:[33]})]),_:1}),s(i,{label:"稀有度",prop:"rarity"},{default:p(()=>[s(r,{modelValue:Y.rarity,"onUpdate:modelValue":o[10]||(o[10]=e=>Y.rarity=e),placeholder:"请选择稀有度"},{default:p(()=>[(C(),c(w,null,k(U,(e,a)=>s(t,{key:a,label:e,value:Number(a)},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),s(i,{label:"等级",prop:"rank"},{default:p(()=>[s(Ce,{modelValue:Y.rank,"onUpdate:modelValue":o[11]||(o[11]=e=>Y.rank=e),min:1,precision:0},null,8,["modelValue"])]),_:1}),s(i,null,{default:p(()=>[s(n,{type:"primary",onClick:ye},{default:p(()=>o[34]||(o[34]=[y("添加")])),_:1,__:[34]}),s(n,{onClick:ve},{default:p(()=>o[35]||(o[35]=[y("重置")])),_:1,__:[35]})]),_:1})]),_:1},8,["model"])),[[Ae,$.value]])]),_:1},8,["modelValue"]),s(xe,{modelValue:Z.value,"onUpdate:modelValue":o[21]||(o[21]=e=>Z.value=e),title:"编辑成就",size:"50%","destroy-on-close":!1},{default:p(()=>[_((C(),f(d,{ref_key:"editFormRef",ref:ae,model:oe,rules:te,"label-width":"120px"},{default:p(()=>[s(i,{label:"成就名称",prop:"name"},{default:p(()=>[s(Ve,{modelValue:oe.name,"onUpdate:modelValue":o[13]||(o[13]=e=>oe.name=e),placeholder:"请输入成就名称"},null,8,["modelValue"])]),_:1}),s(i,{label:"描述",prop:"description"},{default:p(()=>[s(Ve,{modelValue:oe.description,"onUpdate:modelValue":o[14]||(o[14]=e=>oe.description=e),placeholder:"请输入成就描述",type:"textarea",rows:3},null,8,["modelValue"])]),_:1}),s(i,{label:"条件类型",prop:"condition.type"},{default:p(()=>[s(r,{modelValue:oe.condition.type,"onUpdate:modelValue":o[15]||(o[15]=e=>oe.condition.type=e),placeholder:"请选择条件类型"},{default:p(()=>[s(t,{label:"等级达成",value:"level_reached"}),s(t,{label:"融合达成",value:"fusion_reached"}),s(t,{label:"稀有度抽取",value:"draw_by_rarity"}),s(t,{label:"完成交易",value:"buy_deal_reached"}),s(t,{label:"完成销售",value:"sell_deal_reached"}),s(t,{label:"抽卡消费积分",value:"draw_spend_points"})]),_:1},8,["modelValue"])]),_:1}),s(i,{label:"目标值",prop:"condition.target"},{default:p(()=>[s(Ce,{modelValue:oe.condition.target,"onUpdate:modelValue":o[16]||(o[16]=e=>oe.condition.target=e),min:1,precision:0},null,8,["modelValue"])]),_:1}),"draw_by_rarity"===oe.condition.type?(C(),f(i,{key:0,label:"稀有度达成条件",prop:"condition.rarity"},{default:p(()=>[s(r,{modelValue:oe.condition.rarity,"onUpdate:modelValue":o[17]||(o[17]=e=>oe.condition.rarity=e),placeholder:"请选择稀有度达成条件"},{default:p(()=>[(C(),c(w,null,k(U,(e,a)=>s(t,{key:a,label:e,value:Number(a)},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1})):b("",!0),s(i,{label:"奖励"},{default:p(()=>[(C(!0),c(w,null,k(oe.reward,(e,i)=>(C(),c("div",{key:i,class:"reward-item"},[s(r,{modelValue:e.type,"onUpdate:modelValue":a=>e.type=a,placeholder:"奖励类型",style:{width:"120px"},onChange:e=>((e,a)=>{"point"===e?(oe.reward[a].amount=100,oe.reward[a].emblemId=void 0,oe.reward[a].url=void 0,oe.reward[a].image=void 0):"emblem"===e&&(oe.reward[a].amount=void 0,oe.reward[a].emblemId=void 0,oe.reward[a].url=void 0,oe.reward[a].image=void 0)})(e,i)},{default:p(()=>[s(t,{label:"积分",value:"point"}),s(t,{label:"徽章",value:"emblem"})]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"]),"point"===e.type?(C(),f(Ce,{key:0,modelValue:e.amount,"onUpdate:modelValue":a=>e.amount=a,min:1,precision:0,placeholder:"积分数量",style:{"margin-left":"10px"}},null,8,["modelValue","onUpdate:modelValue"])):"emblem"===e.type?(C(),f(Ue,{key:1,class:"avatar-uploader",action:"","auto-upload":!1,"show-file-list":!1,"on-change":e=>((e,a)=>{const l=new FileReader;l.readAsDataURL(e.raw),l.onload=()=>{oe.reward&&(oe.reward[a].image=l.result,oe.reward[a].emblemId=void 0,oe.reward[a].url=void 0,oe.reward[a].amount=void 0)}})(e,i),style:{"margin-left":"10px"}},{default:p(()=>[e.image?(C(),c("img",{key:0,src:e.image,class:"avatar"},null,8,M)):(C(),f(ge,{key:1,class:"avatar-uploader-icon"},{default:p(()=>[s(V(l))]),_:1})),o[36]||(o[36]=v("div",{class:"el-upload__text"},"点击上传徽章图片",-1))]),_:2,__:[36]},1032,["on-change"])):b("",!0),s(n,{type:"danger",circle:"",onClick:e=>(e=>{var a;null==(a=oe.reward)||a.splice(e,1)})(i),style:{"margin-left":"10px"}},{default:p(()=>[s(ge,null,{default:p(()=>[s(V(a))]),_:1})]),_:2},1032,["onClick"])]))),128)),s(n,{type:"primary",onClick:_e},{default:p(()=>o[37]||(o[37]=[y("添加奖励")])),_:1,__:[37]})]),_:1}),s(i,{label:"稀有度",prop:"rarity"},{default:p(()=>[s(r,{modelValue:oe.rarity,"onUpdate:modelValue":o[18]||(o[18]=e=>oe.rarity=e),placeholder:"请选择稀有度"},{default:p(()=>[(C(),c(w,null,k(U,(e,a)=>s(t,{key:a,label:e,value:Number(a)},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),s(i,{label:"等级",prop:"rank"},{default:p(()=>[s(Ce,{modelValue:oe.rank,"onUpdate:modelValue":o[19]||(o[19]=e=>oe.rank=e),min:1,precision:0},null,8,["modelValue"])]),_:1}),s(i,null,{default:p(()=>[s(n,{type:"primary",onClick:he},{default:p(()=>o[38]||(o[38]=[y("保存")])),_:1,__:[38]}),s(n,{onClick:o[20]||(o[20]=e=>Z.value=!1)},{default:p(()=>o[39]||(o[39]=[y("取消")])),_:1,__:[39]})]),_:1})]),_:1},8,["model"])),[[Ae,ee.value]])]),_:1},8,["modelValue"]),s(Te,{modelValue:re.value,"onUpdate:modelValue":o[23]||(o[23]=e=>re.value=e),title:"删除确认",width:"400px"},{footer:p(()=>[v("span",G,[s(n,{onClick:o[22]||(o[22]=e=>re.value=!1)},{default:p(()=>o[40]||(o[40]=[y("取消")])),_:1,__:[40]}),s(n,{type:"danger",onClick:fe},{default:p(()=>[s(ge,null,{default:p(()=>[s(V(a))]),_:1}),o[41]||(o[41]=y(" 确认删除 "))]),_:1,__:[41]})])]),default:p(()=>{var e;return[v("p",null,'确定要删除成就 "'+g(null==(e=ie.value)?void 0:e.name)+'" 吗？',1)]}),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-1a30fefc"]]);export{K as default};
