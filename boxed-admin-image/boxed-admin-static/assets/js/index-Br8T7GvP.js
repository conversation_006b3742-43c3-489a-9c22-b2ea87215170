const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/js/index-BWAGFE3d.js","assets/js/vue-DWGM1jOu.js","assets/js/elementPlus-Bm-E4Nxd.js","assets/css/index-DBEeTIZH.css","assets/js/index-CTlQXCVB.js","assets/css/index-CfeJKCY1.css","assets/js/index-BdpzKei7.js","assets/css/index-BWGuym00.css","assets/js/index-B8g8AANT.js","assets/css/index-B-l2qNw-.css","assets/js/index-D3Kk8uNh.js","assets/css/index-LEwxTuGD.css","assets/js/index-VdBUah3f.js","assets/css/index-BqW8JKQs.css","assets/js/index-Cq_IEwCo.js","assets/css/index-BXN72120.css","assets/js/list-CxFs_utq.js","assets/css/list-DmkY28e8.css","assets/js/orders-D2kBF8il.js","assets/css/orders-CvJq6Y8X.css","assets/js/index-B8OkHUte.js","assets/js/storage-BmuEW5sW.js","assets/js/request-ZE4ZUch8.js","assets/css/index-B6EDAqm8.css","assets/js/recipes-R1JWMYY7.js","assets/js/fusion-LqssYdbi.js","assets/js/packs-CZud9J-M.js","assets/css/recipes-D9VzjwdW.css","assets/js/requests-DviWXT4_.js","assets/css/requests-DPySWwXZ.css","assets/js/collections-CtCexKqL.js","assets/css/collections-CbkMO3SC.css","assets/js/index-BrsoyKVB.js","assets/css/index-Ck-vZchV.css","assets/js/create-DhDqyr7O.js","assets/css/create-NHWrzPXd.css","assets/js/detail-ChSABwuu.js","assets/css/detail-sd3LBM5t.css","assets/js/index-B3FtKtrM.js","assets/css/index-C8eiG8NB.css","assets/js/index-DvEiREPq.js","assets/css/index-D-vaQjN3.css","assets/js/new-index-C6kOAKtZ.js","assets/css/new-index-l93n6MDm.css","assets/js/index-DyKK4Cl-.js","assets/css/index-MrVJXGrq.css"])))=>i.map(i=>d[i]);
import{G as e,ag as t,z as n,x as a,aw as i,c as o,H as r,P as s,y as l,L as c,a6 as d,M as p,O as m,r as h,u,J as _,K as f,A as g,ax as y,ay as E,az as k,at as v,aA as P}from"./vue-DWGM1jOu.js";import{d as L,m as x,t as A,s as T,f as S,l as O,u as j,v as D,a as C,c as I,b as R,e as V,g as b,p as w,h as M,i as q,j as B,k as F,n as U,o as $,q as N}from"./elementPlus-Bm-E4Nxd.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver(e=>{for(const n of e)if("childList"===n.type)for(const e of n.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)}).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();const z=(e,t)=>{const n=e.__vccOpts||e;for(const[a,i]of t)n[a]=i;return n};const K=z({},[["render",function(a,i){const o=t("router-view");return n(),e(o)}]]),G={},H=function(e,t,n){let a=Promise.resolve();if(t&&t.length>0){let e=function(e){return Promise.all(e.map(e=>Promise.resolve(e).then(e=>({status:"fulfilled",value:e}),e=>({status:"rejected",reason:e}))))};document.getElementsByTagName("link");const n=document.querySelector("meta[property=csp-nonce]"),i=(null==n?void 0:n.nonce)||(null==n?void 0:n.getAttribute("nonce"));a=e(t.map(e=>{if((e=function(e){return"/"+e}(e))in G)return;G[e]=!0;const t=e.endsWith(".css"),n=t?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${e}"]${n}`))return;const a=document.createElement("link");return a.rel=t?"stylesheet":"modulepreload",t||(a.as="script"),a.crossOrigin="",a.href=e,i&&a.setAttribute("nonce",i),document.head.appendChild(a),t?new Promise((t,n)=>{a.addEventListener("load",t),a.addEventListener("error",()=>n(new Error(`Unable to preload CSS for ${e}`)))}):void 0}))}function i(e){const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}return a.then(t=>{for(const e of t||[])"rejected"===e.status&&i(e.reason);return e().catch(i)})},J=a({__name:"Breadcrumb",setup(a){const h=i(),u=o(()=>h.matched.filter(e=>{var t,n;return(null==(t=e.meta)?void 0:t.title)&&!(null==(n=e.meta)?void 0:n.hidden)}));return(a,i)=>{const o=t("el-breadcrumb-item"),h=t("el-breadcrumb");return n(),e(h,{separator:"/"},{default:r(()=>[s(o,{to:{path:"/"}},{default:r(()=>i[0]||(i[0]=[c("首页")])),_:1,__:[0]}),(n(!0),l(m,null,d(u.value,t=>(n(),e(o,{key:t.path,to:{path:t.path}},{default:r(()=>{var e;return[c(p(null==(e=t.meta)?void 0:e.title),1)]}),_:2},1032,["to"]))),128))]),_:1})}}}),W=[{path:"/system",meta:{title:"系统管理",hidden:!0},icon:"Setting",children:[{path:"/system/users",meta:{title:"用户管理",hidden:!0},icon:"User"},{path:"/system/roles",meta:{title:"角色管理",hidden:!0},icon:"Lock"},{path:"/system/permissions",meta:{title:"权限管理",hidden:!0},icon:"Document"}]},{path:"/points-exchange",meta:{title:"积分兑换",hidden:!0},icon:"ShoppingCart",children:[{path:"/points-exchange/products",meta:{title:"兑换商品列表",hidden:!0},icon:"ShoppingCart"},{path:"/points-exchange/inventory",meta:{title:"库存管理",hidden:!0},icon:"Collection"}]},{path:"/rankings",meta:{title:"排行榜管理",hidden:!0},icon:"TrendCharts",children:[{path:"/rankings/list",meta:{title:"数据查询",hidden:!0},icon:"TrendCharts"}]},{path:"/payment",meta:{title:"支付与充值",hidden:!0},icon:"Money",children:[{path:"/payment/orders",meta:{title:"订单列表",hidden:!0},icon:"Money"}]},{path:"/card-packs",meta:{title:"卡牌管理"},icon:"Collection",children:[{path:"/card-packs/list",meta:{title:"卡牌列表"},icon:"Collection"}]},{path:"/card-synthesis",meta:{title:"卡牌合成"},icon:"MagicStick",children:[{path:"/card-synthesis/recipes",meta:{title:"配方管理"},icon:"Document"},{path:"/card-synthesis/requests",meta:{title:"合成请求列表"},icon:"TrendCharts"}]},{path:"/storage",meta:{title:"卡片存储管理"},icon:"Folder",children:[{path:"/storage/cards",meta:{title:"卡片管理"},icon:"Collection"},{path:"/storage/collections",meta:{title:"卡片分类管理"},icon:"Folder"},{path:"/storage/packs",meta:{title:"卡包管理"},icon:"Present"}]},{path:"/marketplace",meta:{title:"市场管理"},icon:"ShoppingBag",children:[{path:"/marketplace",meta:{title:"官方市场"},icon:"ShoppingCart"}]},{path:"/achievements",meta:{title:"成就管理"},icon:"Trophy",children:[{path:"/achievements",meta:{title:"成就列表"},icon:"Medal"}]},{path:"/shipping",meta:{title:"物流管理"},icon:"Van",children:[{path:"/shipping",meta:{title:"物流信息"},icon:"Van"}]}],Q={class:"header-left"},X={class:"header-right"},Y={class:"user-dropdown"},Z=z(a({__name:"AdminLayout",setup(a){const o={DataLine:$,Setting:U,Document:F,Fold:B,Expand:q,CaretBottom:M,Present:w,ShoppingCart:b,TrendCharts:V,Money:R,Collection:I,Medal:C,Van:D,User:j,Lock:O,Folder:S,ShoppingBag:T,Trophy:A,MagicStick:x,Delete:L},E=i(),k=h(!1),v=()=>{k.value=!k.value};return(a,i)=>{const h=t("el-icon"),P=t("el-menu-item"),L=t("el-sub-menu"),x=t("el-menu"),A=t("el-aside"),T=t("el-button"),S=t("el-dropdown-item"),O=t("el-dropdown-menu"),j=t("el-dropdown"),D=t("el-header"),C=t("router-view"),I=t("el-main"),R=t("el-container");return n(),e(R,{class:"layout-container"},{default:r(()=>[s(A,{width:"200px"},{default:r(()=>[s(x,{"default-active":u(E).path,class:"el-menu-vertical",collapse:k.value,router:""},{default:r(()=>[(n(!0),l(m,null,d(u(W),t=>(n(),l(m,{key:t.path},[t.children||!t.meta||t.meta.hidden?t.children&&t.meta&&!t.meta.hidden?(n(),e(L,{key:1,index:t.path},{title:r(()=>[t.icon?(n(),e(h,{key:0},{default:r(()=>[(n(),e(f(o[t.icon])))]),_:2},1024)):_("",!0),g("span",null,p(t.meta.title),1)]),default:r(()=>[(n(!0),l(m,null,d(t.children,t=>(n(),e(P,{key:t.path,index:t.path},{default:r(()=>[t.icon?(n(),e(h,{key:0},{default:r(()=>[(n(),e(f(o[t.icon])))]),_:2},1024)):_("",!0),c(" "+p(t.meta.title),1)]),_:2},1032,["index"]))),128))]),_:2},1032,["index"])):_("",!0):(n(),e(P,{key:0,index:t.path},{title:r(()=>[c(p(t.meta.title),1)]),default:r(()=>[t.icon?(n(),e(h,{key:0},{default:r(()=>[(n(),e(f(o[t.icon])))]),_:2},1024)):_("",!0)]),_:2},1032,["index"]))],64))),128))]),_:1},8,["default-active","collapse"])]),_:1}),s(R,null,{default:r(()=>[s(D,null,{default:r(()=>[g("div",Q,[s(T,{onClick:v},{default:r(()=>[s(h,null,{default:r(()=>[k.value?(n(),e(u(q),{key:1})):(n(),e(u(B),{key:0}))]),_:1})]),_:1}),s(J,{class:"ml-4"})]),g("div",X,[s(j,null,{dropdown:r(()=>[s(O,null,{default:r(()=>[s(S,null,{default:r(()=>i[1]||(i[1]=[c("个人信息")])),_:1,__:[1]}),s(S,null,{default:r(()=>i[2]||(i[2]=[c("修改密码")])),_:1,__:[2]}),s(S,{divided:""},{default:r(()=>i[3]||(i[3]=[c("退出登录")])),_:1,__:[3]})]),_:1})]),default:r(()=>[g("span",Y,[i[0]||(i[0]=c(" 管理员 ")),s(h,null,{default:r(()=>[s(u(M))]),_:1})])]),_:1})])]),_:1}),s(I,null,{default:r(()=>[(n(),e(y,null,[!1!==a.$route.meta.keepAlive?(n(),e(C,{key:0})):_("",!0)],1024)),!1===a.$route.meta.keepAlive?(n(),e(C,{key:0})):_("",!0)]),_:1})]),_:1})]),_:1})}}}),[["__scopeId","data-v-bfe7b9ef"]]),ee=[{path:"/login",name:"Login",component:()=>H(()=>import("./index-BWAGFE3d.js"),__vite__mapDeps([0,1,2,3])),meta:{title:"登录",hidden:!0}},{path:"/",component:Z,redirect:"/card-packs/list"},{path:"/system",component:Z,redirect:"/system/users",meta:{title:"系统管理",icon:"Setting",hidden:!0},children:[{path:"users",name:"Users",component:()=>H(()=>import("./index-CTlQXCVB.js"),__vite__mapDeps([4,2,1,5])),meta:{title:"用户管理",hidden:!0}},{path:"roles",name:"Roles",component:()=>H(()=>import("./index-BdpzKei7.js"),__vite__mapDeps([6,2,1,7])),meta:{title:"角色管理",hidden:!0}},{path:"permissions",name:"Permissions",component:()=>H(()=>import("./index-B8g8AANT.js"),__vite__mapDeps([8,2,1,9])),meta:{title:"权限管理",hidden:!0}}]},{path:"/user",component:Z,redirect:"/user/profile",meta:{title:"个人中心",icon:"User",hidden:!0},children:[{path:"profile",name:"Profile",component:()=>H(()=>import("./index-D3Kk8uNh.js"),__vite__mapDeps([10,1,2,11])),meta:{title:"个人信息",hidden:!0}}]},{path:"/points-exchange",component:Z,redirect:"/points-exchange/products",meta:{title:"积分兑换",icon:"ShoppingCart",hidden:!0},children:[{path:"products",name:"PointsExchangeProducts",component:()=>H(()=>import("./index-VdBUah3f.js"),__vite__mapDeps([12,1,2,13])),meta:{title:"兑换商品列表",hidden:!0}},{path:"inventory",name:"PointsExchangeInventory",component:()=>H(()=>import("./index-Cq_IEwCo.js"),__vite__mapDeps([14,1,2,15])),meta:{title:"库存管理",hidden:!0}}]},{path:"/rankings",component:Z,redirect:"/rankings/list",meta:{title:"排行榜管理",icon:"TrendCharts",hidden:!0},children:[{path:"list",name:"RankingsList",component:()=>H(()=>import("./list-CxFs_utq.js"),__vite__mapDeps([16,1,2,17])),meta:{title:"数据查询",hidden:!0}}]},{path:"/payment",component:Z,redirect:"/payment/orders",meta:{title:"支付与充值",icon:"Money",hidden:!0},children:[{path:"orders",name:"PaymentOrders",component:()=>H(()=>import("./orders-D2kBF8il.js"),__vite__mapDeps([18,1,2,19])),meta:{title:"订单列表",hidden:!0}}]},{path:"/card-packs",component:Z,redirect:"/card-packs/list",meta:{title:"卡牌管理",icon:"Collection"},children:[{path:"list",name:"CardPacksList",component:()=>H(()=>import("./index-B8OkHUte.js"),__vite__mapDeps([20,21,22,2,1,23])),meta:{title:"卡牌列表"}}]},{path:"/card-synthesis",component:Z,redirect:"/card-synthesis/recipes",meta:{title:"卡牌合成",icon:"MagicStick"},children:[{path:"recipes",name:"CardSynthesisRecipes",component:()=>H(()=>import("./recipes-R1JWMYY7.js"),__vite__mapDeps([24,1,2,25,22,21,26,27])),meta:{title:"配方管理"}},{path:"requests",name:"CardSynthesisRequests",component:()=>H(()=>import("./requests-DviWXT4_.js"),__vite__mapDeps([28,25,22,21,2,1,29])),meta:{title:"合成请求列表"}}]},{path:"/storage",component:Z,redirect:"/storage/cards",meta:{title:"卡片存储管理",icon:"Folder"},children:[{path:"cards",name:"StorageCards",component:()=>H(()=>import("./index-B8OkHUte.js"),__vite__mapDeps([20,21,22,2,1,23])),meta:{title:"卡片管理"}},{path:"collections",name:"StorageCollections",component:()=>H(()=>import("./collections-CtCexKqL.js"),__vite__mapDeps([30,21,22,2,1,31])),meta:{title:"卡片分类管理"}},{path:"packs",name:"StoragePacks",component:()=>H(()=>import("./index-BrsoyKVB.js"),__vite__mapDeps([32,1,26,22,2,33])),meta:{title:"卡包管理"}},{path:"packs/create",name:"StoragePacksCreate",component:()=>H(()=>import("./create-DhDqyr7O.js"),__vite__mapDeps([34,1,2,26,22,35])),meta:{title:"创建卡包",activeMenu:"/storage/packs"}},{path:"packs/:id",name:"StoragePacksDetail",component:()=>H(()=>import("./detail-ChSABwuu.js"),__vite__mapDeps([36,1,26,22,21,2,37])),meta:{title:"卡包详情",activeMenu:"/storage/packs"}}]},{path:"/marketplace",component:Z,redirect:"/marketplace",meta:{title:"市场管理",icon:"ShoppingBag"},children:[{path:"",name:"Marketplace",component:()=>H(()=>import("./index-B3FtKtrM.js"),__vite__mapDeps([38,22,21,2,1,39])),meta:{title:"官方市场"}}]},{path:"/achievements",component:Z,redirect:"/achievements",meta:{title:"成就管理",icon:"Trophy"},children:[{path:"",name:"Achievements",component:()=>H(()=>import("./index-DvEiREPq.js"),__vite__mapDeps([40,2,1,22,41])),meta:{title:"成就列表"}}]},{path:"/shipping",component:Z,redirect:"/shipping",meta:{title:"物流管理",icon:"Van"},children:[{path:"",name:"Shipping",component:()=>H(()=>import("./new-index-C6kOAKtZ.js"),__vite__mapDeps([42,1,22,2,43])),meta:{title:"物流信息"}},{path:"legacy",name:"ShippingLegacy",component:()=>H(()=>import("./index-DyKK4Cl-.js"),__vite__mapDeps([44,2,1,22,45])),meta:{title:"物流信息(旧)"}}]}],te=E({history:k(),routes:ee});te.beforeEach((e,t,n)=>{document.title=e.meta.title||"Boxed Admin";const a=localStorage.getItem("token");"/login"===e.path||a?n():n("/login")});const ne=v(K);ne.use(N),ne.use(te),ne.use(P()),ne.mount("#app");export{z as _,H as a};
