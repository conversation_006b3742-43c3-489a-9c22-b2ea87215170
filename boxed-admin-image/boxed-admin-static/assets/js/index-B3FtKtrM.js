import{r as e}from"./request-ZE4ZUch8.js";import{g as l}from"./storage-BmuEW5sW.js";import{E as a}from"./elementPlus-Bm-E4Nxd.js";import{x as t,X as o,r,h as i,a3 as u,Y as d,y as c,P as n,H as s,ag as p,O as m,a6 as _,L as y,A as f,I as v,aq as h,G as g,z as V}from"./vue-DWGM1jOu.js";import{_ as b}from"./index-Br8T7GvP.js";const I={class:"marketplace-container"},k={class:"header-actions"},C={key:1},w={class:"pagination-container"},q={class:"dialog-footer"},P={class:"dialog-footer"},U=b(t({name:"MarketplaceIndex",__name:"index",setup(t){const b=o({collectionId:"",sortBy:"pricePoints",sortOrder:"asc",searchQuery:""}),U=r([]),x=r(!1),O=r(1),z=r(10),B=r(0),F=r([]),Q=r(!1),S=r(!1),j=r(!1),D=r(),R=o({collection_id:"",card_id:"",quantity:1,pricePoints:0,priceCash:0}),J={collection_id:[{required:!0,message:"请选择卡片分类",trigger:"change"}],card_id:[{required:!0,message:"请输入卡片ID",trigger:"blur"}],pricePoints:[{required:!0,message:"请输入积分价格",trigger:"blur"}]},N=r(!1),A=r(!1),E=r(),G=r(null),H=o({collection_id:"",card_id:"",pricePoints:0,priceCash:0}),L={pricePoints:[{required:!0,message:"请输入积分价格",trigger:"blur"}]},M=r(!1),X=r(null),Y=o({collection_id:"",card_id:"",quantity:1}),$=r(!1),K=r(null),T=o({userId:"",quantity:1}),W=async()=>{if(b.collectionId){x.value=!0;try{const l=await(async l=>e.get("/marketplace/official_listings",{params:l}))({collection_id:b.collectionId,page:O.value,per_page:z.value,sort_by:b.sortBy,sort_order:b.sortOrder,search_query:b.searchQuery});U.value=l.data.cards,B.value=l.data.pagination.total_items}catch(l){a.error("获取市场列表失败")}finally{x.value=!1}}else a.warning("请先选择卡片分类")},Z=()=>{O.value=1,W()},ee=()=>{b.collectionId="",b.sortBy="pricePoints",b.sortOrder="asc",b.searchQuery="",O.value=1,U.value=[],B.value=0},le=()=>{O.value=1,W()},ae=e=>{z.value=e,W()},te=e=>{O.value=e,W()},oe=()=>{R.collection_id=b.collectionId,S.value=!0},re=async()=>{D.value&&await D.value.validate(async l=>{if(!l)return!1;j.value=!0;try{await(async l=>e.post("/marketplace/official_listings",l))(R),a.success("添加商品成功"),S.value=!1,ie(),W()}catch(t){a.error("添加商品失败")}finally{j.value=!1}})},ie=()=>{D.value&&(D.value.resetFields(),R.collection_id=b.collectionId,R.card_id="",R.quantity=1,R.pricePoints=0,R.priceCash=0)},ue=async()=>{E.value&&G.value&&await E.value.validate(async l=>{if(!l)return!1;A.value=!0;try{await(async l=>e.put("/marketplace/official_listings",l))(H),a.success("更新商品成功"),N.value=!1,W()}catch(t){a.error("更新商品失败")}finally{A.value=!1}})},de=async()=>{if(X.value)try{await(async l=>e.post("/marketplace/official_listings",l))(Y),a.success("更新数量成功"),M.value=!1,W()}catch(l){a.error("更新数量失败")}},ce=async()=>{if(K.value&&T.userId)try{await(async(l,a)=>e.post(`/marketplace/buy_out/${l}`,a))(T.userId,{collection_id:b.collectionId,card_id:K.value.id,quantity:T.quantity}),a.success("用户购买处理成功"),$.value=!1,W()}catch(l){a.error("用户购买处理失败")}else a.warning("请输入用户ID")};return i(()=>{(async()=>{try{const{collections:e,pokemenCollection:a}=await l();if(F.value=e,a){const l=e.find(e=>e.name===a);l&&(b.collectionId=l.storagePrefix,Q.value&&W())}}catch(e){a.error("获取卡片分类失败")}})(),Q.value=!0}),u(()=>{localStorage.setItem("marketplace_state",JSON.stringify({searchForm:{collectionId:b.collectionId,sortBy:b.sortBy,sortOrder:b.sortOrder,searchQuery:b.searchQuery},pagination:{currentPage:O.value,pageSize:z.value}}))}),d(()=>{const e=localStorage.getItem("marketplace_state");if(e){const l=JSON.parse(e);l.searchForm&&(b.collectionId=l.searchForm.collectionId,b.sortBy=l.searchForm.sortBy,b.sortOrder=l.searchForm.sortOrder,b.searchQuery=l.searchForm.searchQuery),l.pagination&&(O.value=l.pagination.currentPage,z.value=l.pagination.pageSize)}b.collectionId&&W()}),(e,l)=>{const a=p("el-option"),t=p("el-select"),o=p("el-form-item"),r=p("el-input"),i=p("el-button"),u=p("el-form"),d=p("el-card"),Q=p("el-table-column"),K=p("el-image"),W=p("el-table"),ne=p("el-pagination"),se=p("el-input-number"),pe=p("el-drawer"),me=p("el-dialog"),_e=h("loading");return V(),c("div",I,[n(d,{class:"search-card"},{default:s(()=>[n(u,{ref:"searchFormRef",inline:""},{default:s(()=>[n(o,{label:"卡片分类",prop:"collectionId"},{default:s(()=>[n(t,{modelValue:b.collectionId,"onUpdate:modelValue":l[0]||(l[0]=e=>b.collectionId=e),placeholder:"请选择卡片分类",clearable:"","popper-append-to-body":"",onChange:Z,style:{width:"150px"}},{default:s(()=>[(V(!0),c(m,null,_(F.value,e=>(V(),g(a,{key:e.name,label:e.name,value:e.name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),n(o,{label:"排序字段",prop:"sortBy"},{default:s(()=>[n(t,{modelValue:b.sortBy,"onUpdate:modelValue":l[1]||(l[1]=e=>b.sortBy=e),placeholder:"请选择排序字段",style:{width:"150px"},clearable:""},{default:s(()=>[n(a,{label:"积分价格",value:"pricePoints"}),n(a,{label:"现金价格",value:"priceCash"}),n(a,{label:"卡片名称",value:"card_name"}),n(a,{label:"稀有度",value:"rarity"})]),_:1},8,["modelValue"])]),_:1}),n(o,{label:"排序方式",prop:"sortOrder"},{default:s(()=>[n(t,{modelValue:b.sortOrder,"onUpdate:modelValue":l[2]||(l[2]=e=>b.sortOrder=e),placeholder:"请选择排序方式",style:{width:"150px"},clearable:""},{default:s(()=>[n(a,{label:"升序",value:"asc"}),n(a,{label:"降序",value:"desc"})]),_:1},8,["modelValue"])]),_:1}),n(o,{label:"搜索",prop:"searchQuery"},{default:s(()=>[n(r,{modelValue:b.searchQuery,"onUpdate:modelValue":l[3]||(l[3]=e=>b.searchQuery=e),placeholder:"请输入搜索关键词",clearable:""},null,8,["modelValue"])]),_:1}),n(o,null,{default:s(()=>[n(i,{type:"primary",onClick:le},{default:s(()=>l[23]||(l[23]=[y("查询")])),_:1,__:[23]}),n(i,{onClick:ee},{default:s(()=>l[24]||(l[24]=[y("重置")])),_:1,__:[24]})]),_:1})]),_:1},512)]),_:1}),n(d,{class:"table-card"},{default:s(()=>[f("div",k,[n(i,{type:"primary",onClick:oe},{default:s(()=>l[25]||(l[25]=[y("添加商品")])),_:1,__:[25]})]),v((V(),g(W,{data:U.value,border:"",stripe:""},{default:s(()=>[n(Q,{prop:"card_name",label:"卡片名称"}),n(Q,{prop:"rarity",label:"稀有度"}),n(Q,{prop:"pricePoints",label:"积分价格"}),n(Q,{prop:"priceCash",label:"现金价格"}),n(Q,{prop:"quantity",label:"数量"}),n(Q,{prop:"condition",label:"状态"}),n(Q,{label:"图片",width:"120"},{default:s(({row:e})=>[e.image_url?(V(),g(K,{key:0,src:e.image_url,style:{width:"80px",height:"80px"},"preview-src-list":[e.image_url]},null,8,["src","preview-src-list"])):(V(),c("span",C,"-"))]),_:1}),n(Q,{label:"操作",width:"280",fixed:"right"},{default:s(({row:e})=>[n(i,{link:"",type:"primary",onClick:l=>(e=>{G.value=e,H.collection_id=b.collectionId,H.card_id=e.id,H.pricePoints=e.pricePoints,H.priceCash=e.priceCash||0,N.value=!0})(e)},{default:s(()=>l[26]||(l[26]=[y("编辑")])),_:2,__:[26]},1032,["onClick"]),n(i,{link:"",type:"primary",onClick:l=>(e=>{X.value=e,Y.collection_id=b.collectionId,Y.card_id=e.id,Y.quantity=e.quantity,M.value=!0})(e)},{default:s(()=>l[27]||(l[27]=[y("修改数量")])),_:2,__:[27]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[_e,x.value]]),f("div",w,[n(ne,{"current-page":O.value,"onUpdate:currentPage":l[4]||(l[4]=e=>O.value=e),"page-size":z.value,"onUpdate:pageSize":l[5]||(l[5]=e=>z.value=e),"page-sizes":[10,20,50,100],total:B.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:ae,onCurrentChange:te},null,8,["current-page","page-size","total"])])]),_:1}),n(pe,{modelValue:S.value,"onUpdate:modelValue":l[11]||(l[11]=e=>S.value=e),title:"添加商品",size:"50%","destroy-on-close":!1},{default:s(()=>[v((V(),g(u,{ref_key:"addFormRef",ref:D,model:R,rules:J,"label-width":"120px"},{default:s(()=>[n(o,{label:"卡片分类",prop:"collection_id"},{default:s(()=>[n(t,{modelValue:R.collection_id,"onUpdate:modelValue":l[6]||(l[6]=e=>R.collection_id=e),placeholder:"请选择卡片分类"},{default:s(()=>[(V(!0),c(m,null,_(F.value,e=>(V(),g(a,{key:e.name,label:e.name,value:e.name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),n(o,{label:"卡片ID",prop:"card_id"},{default:s(()=>[n(r,{modelValue:R.card_id,"onUpdate:modelValue":l[7]||(l[7]=e=>R.card_id=e),placeholder:"请输入卡片ID"},null,8,["modelValue"])]),_:1}),n(o,{label:"数量",prop:"quantity"},{default:s(()=>[n(se,{modelValue:R.quantity,"onUpdate:modelValue":l[8]||(l[8]=e=>R.quantity=e),min:1,precision:0},null,8,["modelValue"])]),_:1}),n(o,{label:"积分价格",prop:"pricePoints"},{default:s(()=>[n(se,{modelValue:R.pricePoints,"onUpdate:modelValue":l[9]||(l[9]=e=>R.pricePoints=e),min:0,precision:0},null,8,["modelValue"])]),_:1}),n(o,{label:"现金价格",prop:"priceCash"},{default:s(()=>[n(se,{modelValue:R.priceCash,"onUpdate:modelValue":l[10]||(l[10]=e=>R.priceCash=e),min:0,precision:0},null,8,["modelValue"])]),_:1}),n(o,null,{default:s(()=>[n(i,{type:"primary",onClick:re},{default:s(()=>l[28]||(l[28]=[y("添加")])),_:1,__:[28]}),n(i,{onClick:ie},{default:s(()=>l[29]||(l[29]=[y("重置")])),_:1,__:[29]})]),_:1})]),_:1},8,["model"])),[[_e,j.value]])]),_:1},8,["modelValue"]),n(pe,{modelValue:N.value,"onUpdate:modelValue":l[15]||(l[15]=e=>N.value=e),title:"编辑商品",size:"50%","destroy-on-close":!1},{default:s(()=>[v((V(),g(u,{ref_key:"editFormRef",ref:E,model:H,rules:L,"label-width":"120px"},{default:s(()=>[n(o,{label:"积分价格",prop:"pricePoints"},{default:s(()=>[n(se,{modelValue:H.pricePoints,"onUpdate:modelValue":l[12]||(l[12]=e=>H.pricePoints=e),min:0,precision:0},null,8,["modelValue"])]),_:1}),n(o,{label:"现金价格",prop:"priceCash"},{default:s(()=>[n(se,{modelValue:H.priceCash,"onUpdate:modelValue":l[13]||(l[13]=e=>H.priceCash=e),min:0,precision:0},null,8,["modelValue"])]),_:1}),n(o,null,{default:s(()=>[n(i,{type:"primary",onClick:ue},{default:s(()=>l[30]||(l[30]=[y("保存")])),_:1,__:[30]}),n(i,{onClick:l[14]||(l[14]=e=>N.value=!1)},{default:s(()=>l[31]||(l[31]=[y("取消")])),_:1,__:[31]})]),_:1})]),_:1},8,["model"])),[[_e,A.value]])]),_:1},8,["modelValue"]),n(me,{modelValue:M.value,"onUpdate:modelValue":l[18]||(l[18]=e=>M.value=e),title:"修改商品数量",width:"400px"},{footer:s(()=>[f("span",q,[n(i,{onClick:l[17]||(l[17]=e=>M.value=!1)},{default:s(()=>l[32]||(l[32]=[y("取消")])),_:1,__:[32]}),n(i,{type:"primary",onClick:de},{default:s(()=>l[33]||(l[33]=[y("确认")])),_:1,__:[33]})])]),default:s(()=>[n(u,{"label-width":"100px"},{default:s(()=>[n(o,{label:"数量"},{default:s(()=>[n(se,{modelValue:Y.quantity,"onUpdate:modelValue":l[16]||(l[16]=e=>Y.quantity=e),min:1,precision:0},null,8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["modelValue"]),n(me,{modelValue:$.value,"onUpdate:modelValue":l[22]||(l[22]=e=>$.value=e),title:"用户购买",width:"400px"},{footer:s(()=>[f("span",P,[n(i,{onClick:l[21]||(l[21]=e=>$.value=!1)},{default:s(()=>l[34]||(l[34]=[y("取消")])),_:1,__:[34]}),n(i,{type:"primary",onClick:ce},{default:s(()=>l[35]||(l[35]=[y("确认")])),_:1,__:[35]})])]),default:s(()=>[n(u,{"label-width":"100px"},{default:s(()=>[n(o,{label:"用户ID",prop:"userId"},{default:s(()=>[n(r,{modelValue:T.userId,"onUpdate:modelValue":l[19]||(l[19]=e=>T.userId=e),placeholder:"请输入用户ID"},null,8,["modelValue"])]),_:1}),n(o,{label:"购买数量"},{default:s(()=>[n(se,{modelValue:T.quantity,"onUpdate:modelValue":l[20]||(l[20]=e=>T.quantity=e),min:1,precision:0},null,8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-658a5c31"]]);export{U as default};
