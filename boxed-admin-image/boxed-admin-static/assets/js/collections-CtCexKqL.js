import{h as e,i as a,j as l,k as r}from"./storage-BmuEW5sW.js";import{E as t,x as o}from"./elementPlus-Bm-E4Nxd.js";import{x as i,r as s,X as u,h as n,y as d,P as c,H as m,ag as f,A as p,I as v,L as _,aq as g,G as b,J as y,M as x,z as h}from"./vue-DWGM1jOu.js";import{_ as C}from"./index-Br8T7GvP.js";import"./request-ZE4ZUch8.js";const V={class:"collections-container"},k={class:"header-actions"},w={class:"dialog-footer"},P={key:0,class:"detail-section"},F=C(i({name:"StorageCollections",__name:"collections",setup(i){const C=s([]),F=s(!1),j=s(!1),q=s(!1),U=s(),B=u({name:"",firestoreCollection:"",storagePrefix:""}),I={name:[{required:!0,message:"请输入分类名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],firestoreCollection:[{required:!0,message:"请输入Firestore集合名称",trigger:"blur"}],storagePrefix:[{required:!0,message:"请输入存储前缀",trigger:"blur"}]},T=s(!1),z=s(!1),A=s({name:"",firestoreCollection:"",storagePrefix:""}),E=s({detail:[]}),G=async()=>{F.value=!0;try{const a=await e();C.value=a}catch(a){t.error("获取分类列表失败")}finally{F.value=!1}},H=()=>{j.value=!0},J=async()=>{U.value&&await U.value.validate(async e=>{if(!e)return!1;q.value=!0;try{await a(B),t.success("创建分类成功"),j.value=!1,L(),G()}catch(l){t.error("创建分类失败")}finally{q.value=!1}})},L=()=>{U.value&&U.value.resetFields()};return n(()=>{G()}),(e,a)=>{const i=f("el-button"),s=f("el-table-column"),u=f("el-table"),n=f("el-card"),L=f("el-input"),M=f("el-form-item"),R=f("el-form"),S=f("el-dialog"),X=f("el-descriptions-item"),$=f("el-descriptions"),D=g("loading");return h(),d("div",V,[c(n,{class:"table-card"},{default:m(()=>[p("div",k,[c(i,{type:"primary",onClick:H},{default:m(()=>a[6]||(a[6]=[_("创建分类")])),_:1,__:[6]})]),v((h(),b(u,{data:C.value,border:"",stripe:""},{default:m(()=>[c(s,{prop:"name",label:"分类名称"}),c(s,{prop:"firestoreCollection",label:"Firestore集合"}),c(s,{prop:"storagePrefix",label:"存储前缀"}),c(s,{label:"操作",width:"200",fixed:"right"},{default:m(({row:e})=>[c(i,{link:"",type:"primary",onClick:a=>(async e=>{A.value=e,T.value=!0,z.value=!0;try{const a=await l(e.name);E.value=a}catch(a){t.error("获取分类详情失败")}finally{z.value=!1}})(e)},{default:m(()=>a[7]||(a[7]=[_("查看详情")])),_:2,__:[7]},1032,["onClick"]),c(i,{link:"",type:"danger",onClick:a=>(async e=>{try{await o.confirm(`确定要删除分类 "${e.name}" 吗？删除后无法恢复，且可能影响相关卡片数据。`,"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),F.value=!0,await r(e.name),t.success("删除分类成功"),G()}catch(a){"cancel"!==a&&t.error("删除分类失败")}finally{F.value=!1}})(e)},{default:m(()=>a[8]||(a[8]=[_("删除")])),_:2,__:[8]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[D,F.value]])]),_:1}),c(S,{modelValue:j.value,"onUpdate:modelValue":a[4]||(a[4]=e=>j.value=e),title:"创建分类",width:"500px"},{footer:m(()=>[p("span",w,[c(i,{onClick:a[3]||(a[3]=e=>j.value=!1)},{default:m(()=>a[9]||(a[9]=[_("取消")])),_:1,__:[9]}),c(i,{type:"primary",onClick:J},{default:m(()=>a[10]||(a[10]=[_("确认")])),_:1,__:[10]})])]),default:m(()=>[v((h(),b(R,{ref_key:"createFormRef",ref:U,model:B,rules:I,"label-width":"120px"},{default:m(()=>[c(M,{label:"分类名称",prop:"name"},{default:m(()=>[c(L,{modelValue:B.name,"onUpdate:modelValue":a[0]||(a[0]=e=>B.name=e),placeholder:"请输入分类名称"},null,8,["modelValue"])]),_:1}),c(M,{label:"Firestore集合",prop:"firestoreCollection"},{default:m(()=>[c(L,{modelValue:B.firestoreCollection,"onUpdate:modelValue":a[1]||(a[1]=e=>B.firestoreCollection=e),placeholder:"请输入Firestore集合名称"},null,8,["modelValue"])]),_:1}),c(M,{label:"存储前缀",prop:"storagePrefix"},{default:m(()=>[c(L,{modelValue:B.storagePrefix,"onUpdate:modelValue":a[2]||(a[2]=e=>B.storagePrefix=e),placeholder:"请输入存储前缀"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])),[[D,q.value]])]),_:1},8,["modelValue"]),c(S,{modelValue:T.value,"onUpdate:modelValue":a[5]||(a[5]=e=>T.value=e),title:"分类详情",width:"600px"},{default:m(()=>[v((h(),d("div",null,[c($,{column:1,border:""},{default:m(()=>[c(X,{label:"分类名称"},{default:m(()=>[_(x(A.value.name),1)]),_:1}),c(X,{label:"Firestore集合"},{default:m(()=>[_(x(A.value.firestoreCollection),1)]),_:1}),c(X,{label:"存储前缀"},{default:m(()=>[_(x(A.value.storagePrefix),1)]),_:1})]),_:1}),E.value.detail?(h(),d("div",P,[a[11]||(a[11]=p("h3",null,"详细信息",-1)),c(u,{data:E.value.detail,border:"",stripe:""},{default:m(()=>[c(s,{prop:"loc",label:"位置"}),c(s,{prop:"msg",label:"消息"}),c(s,{prop:"type",label:"类型"})]),_:1},8,["data"])])):y("",!0)])),[[D,z.value]])]),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-07f483f3"]]);export{F as default};
