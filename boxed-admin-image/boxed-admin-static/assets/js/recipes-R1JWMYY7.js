import{x as e,X as l,r as a,c as i,h as d,y as t,P as c,A as r,H as n,ag as o,L as u,O as _,a6 as s,M as p,u as v,J as m,I as g,aq as y,G as f,a4 as k,z as h}from"./vue-DWGM1jOu.js";import{E as b,d as V,x as w}from"./elementPlus-Bm-E4Nxd.js";import{g as C,c as q,u as I,d as x}from"./fusion-LqssYdbi.js";import{g as U,a as D}from"./storage-BmuEW5sW.js";import{getPackList as z}from"./packs-CZud9J-M.js";import{_ as P}from"./index-Br8T7GvP.js";import"./request-ZE4ZUch8.js";const Q={class:"recipes-container"},j={class:"header-actions"},S={class:"expanded-recipes"},E={key:0,class:"card-detail-content"},J={class:"pagination-container"},N={class:"pack-select-container"},O={class:"card-select-container"},B={key:0,class:"selected-card-preview"},F=["src"],R={key:1,class:"selected-card-image-placeholder"},T={class:"selected-card-info"},A={class:"ingredient-card-container"},G={class:"pack-select-container"},H={class:"card-select-container"},K={class:"ingredient-card-container"},L={class:"pack-selection-header"},M={key:1},X={key:1,class:"no-packs"},W={key:2,class:"pack-pagination"},Y={key:0,class:"card-grid"},Z=["onClick"],$=["src"],ee={key:1,class:"card-image-placeholder"},le={class:"card-info"},ae={class:"card-name"},ie={class:"card-rarity"},de={class:"card-points"},te={key:1,class:"no-cards"},ce=P(e({__name:"recipes",setup(e){const P=l({collectionId:"",searchQuery:"",userId:""}),ce=a(1),re=a(10),ne=a({collections:[],pagination:{total_items:0,total_pages:0,current_page:1,per_page:10},filters:{sort_by:"result_card_id",sort_order:"desc",search_query:""}}),oe=i(()=>{const e=[];return ne.value.collections.forEach(l=>{l.packs.forEach(a=>{e.push({pack_id:a.pack_id,collection_id:l.collection_id,cards_count:a.cards_count||a.cards.length,cards:a.cards})})}),e}),ue=a(!1),_e=a(null),se=a(!1),pe=a([]),ve=a(!1),me=a(!1),ge=a(),ye=l({result_card_id:"",card_collection_id:"",pack_id:"",pack_collection_id:"",collection_metadata_id:"",ingredients:[{card_id:"",quantity:1}]}),fe=a(!1),ke=a(),he=a(null),be=l({result_card_id:"",card_collection_id:"",pack_id:"",pack_collection_id:"",ingredients:[],originalIngredients:[]}),Ve={result_card_id:[{required:!0,message:"请输入目标卡牌ID",trigger:"blur"}],card_collection_id:[{required:!0,message:"请选择分类",trigger:"change"}],pack_id:[{required:!0,message:"请输入卡包ID",trigger:"blur"}],pack_collection_id:[{required:!0,message:"请输入卡包分类",trigger:"blur"}]},we=a(!1),Ce=async()=>{se.value=!0;try{ne.value=await C({collection_id:P.collectionId,user_id:P.userId,page:ce.value,per_page:re.value,search_query:P.searchQuery})}catch(e){b.error("获取配方列表失败")}finally{se.value=!1}},qe=()=>{ce.value=1,Ce()},Ie=()=>{ce.value=1,Ce()},xe=()=>{P.collectionId="",P.searchQuery="",P.userId="",ce.value=1,Ce()},Ue=e=>{re.value=e,Ce()},De=e=>{ce.value=e,Ce()},ze=a(!1),Pe=a(!1),Qe=a({packs:[],pagination:{total_items:0,total_pages:0,current_page:1,per_page:10},filters:{sort_by:"pack_name",sort_order:"desc",search_query:""},next_cursor:null}),je=l({searchQuery:""}),Se=a(1),Ee=a(10),Je=a(!1),Ne=a(!1),Oe=a([]),Be=a("选择卡片"),Fe=a(null),Re=a(-1),Te=a(!1),Ae=a(!1),Ge=a(!1),He=()=>{ye.result_card_id="",ye.card_collection_id="",ye.pack_id="",ye.pack_collection_id="",ye.collection_metadata_id="",ye.ingredients=[{card_id:"",quantity:1}],Fe.value=null,me.value=!0},Ke=()=>{ye.ingredients.push({card_id:"",quantity:1})},Le=async()=>{ge.value&&await ge.value.validate(async e=>{if(e){we.value=!0;try{const e=ye.ingredients.filter(e=>e.card_id&&e.quantity>0);if(0===e.length)return void b.warning("请至少添加一种材料");await q({result_card_id:ye.result_card_id,card_collection_id:ye.card_collection_id,pack_id:ye.pack_id,pack_collection_id:ye.pack_collection_id,collection_metadata_id:ye.collection_metadata_id,ingredients:e}),b.success("创建配方成功"),me.value=!1,Ce()}catch(l){b.error("创建配方失败")}finally{we.value=!1}}})},Me=()=>{be.ingredients.push({card_collection_id:"",card_id:"",card_reference:"",quantity:1})},Xe=async()=>{ke.value&&he.value&&await ke.value.validate(async e=>{if(e){we.value=!0;try{const e=[],l=[];if(be.ingredients.forEach(a=>{const i=be.originalIngredients.find(e=>e.card_id===a.card_id);i?i.quantity!==a.quantity&&(l.push({card_id:i.card_id,quantity:i.quantity}),e.push({card_id:a.card_id,quantity:a.quantity})):e.push({card_id:a.card_id,quantity:a.quantity})}),be.originalIngredients.forEach(e=>{be.ingredients.some(l=>l.card_id===e.card_id)||l.push({card_id:e.card_id,quantity:e.quantity})}),0===e.length&&0===l.length)return b.info("配方未发生变更"),void(fe.value=!1);await I(he.value.pack_collection_id,he.value.pack_id,he.value.result_card_id,{card_collection_id:be.card_collection_id,pack_id:be.pack_id,pack_collection_id:be.pack_collection_id,added_ingredients:e.length>0?e:void 0,deleted_ingredients:l.length>0?l:void 0}),b.success("更新配方成功"),fe.value=!1,Ce()}catch(l){b.error("更新配方失败")}finally{we.value=!1}}})},We=async()=>{ye.card_collection_id?(Te.value=!0,Ae.value=!1,Ge.value=!1,Be.value="选择目标卡牌",await Ye(ye.card_collection_id)):b.warning("请先选择分类")},Ye=async e=>{Oe.value=[],Je.value=!0,Ne.value=!0;try{const l=await D({collectionName:e,page:1,per_page:100,sort_by:"point_worth",sort_order:"desc"});Oe.value=l.cards}catch(l){b.error("获取卡片列表失败")}finally{Ne.value=!1}},Ze=()=>{ye.pack_id=""},$e=()=>{ye.pack_collection_id?(ze.value=!0,el()):b.warning("请先选择卡包分类")},el=async()=>{if(ye.pack_collection_id){Pe.value=!0;try{Qe.value=await z(ye.pack_collection_id,{page:Se.value,per_page:Ee.value,search_query:je.searchQuery,sort_by:"pack_name",sort_order:"desc"})}catch(e){b.error("获取卡包列表失败")}finally{Pe.value=!1}}else b.warning("请先选择卡包分类")},ll=()=>{Se.value=1,el()},al=()=>{je.searchQuery="",Se.value=1,el()},il=e=>{Ee.value=e,el()},dl=e=>{Se.value=e,el()};return d(()=>{(async()=>{try{const{collections:e,pokemenCollection:l}=await U();if(pe.value=e,l){const a=e.find(e=>e.name===l);a&&(P.collectionId=a.storagePrefix,ve.value&&Ce())}}catch(e){b.error("获取分类列表失败")}})(),ve.value=!0}),(e,l)=>{var a;const i=o("el-button"),d=o("el-option"),C=o("el-select"),q=o("el-form-item"),I=o("el-input"),U=o("el-form"),D=o("el-card"),z=o("el-table-column"),se=o("el-tag"),ve=o("el-icon"),el=o("el-table"),tl=o("el-descriptions-item"),cl=o("el-descriptions"),rl=o("el-divider"),nl=o("el-drawer"),ol=o("el-pagination"),ul=o("el-input-number"),_l=o("el-dialog"),sl=o("el-image"),pl=o("el-empty"),vl=y("loading");return h(),t("div",Q,[c(D,{class:"search-card"},{default:n(()=>[r("div",j,[c(i,{type:"primary",onClick:He},{default:n(()=>l[22]||(l[22]=[u("新增配方")])),_:1,__:[22]})]),c(U,{model:P,inline:"",class:"mt-4"},{default:n(()=>[c(q,{label:"分类"},{default:n(()=>[c(C,{modelValue:P.collectionId,"onUpdate:modelValue":l[0]||(l[0]=e=>P.collectionId=e),style:{width:"150px"},placeholder:"请选择分类",clearable:"",onChange:qe},{default:n(()=>[(h(!0),t(_,null,s(pe.value,e=>(h(),f(d,{key:e.storagePrefix,label:e.name,value:e.storagePrefix},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),c(q,{label:"目标卡牌"},{default:n(()=>[c(I,{modelValue:P.searchQuery,"onUpdate:modelValue":l[1]||(l[1]=e=>P.searchQuery=e),placeholder:"请输入目标卡牌",clearable:""},null,8,["modelValue"])]),_:1}),c(q,null,{default:n(()=>[c(i,{type:"primary",onClick:Ie},{default:n(()=>l[23]||(l[23]=[u("查询")])),_:1,__:[23]}),c(i,{onClick:xe},{default:n(()=>l[24]||(l[24]=[u("重置")])),_:1,__:[24]})]),_:1})]),_:1},8,["model"])]),_:1}),c(D,{class:"pack-recipe-list-card"},{default:n(()=>[c(el,{data:oe.value,border:"",stripe:""},{default:n(()=>[c(z,{type:"expand"},{default:n(({row:e})=>[r("div",S,[r("h4",null,p(e.pack_id)+" - 配方列表",1),c(el,{data:e.cards,border:"",style:{width:"100%"}},{default:n(()=>[c(z,{prop:"card_reference",label:"卡牌名称","min-width":"150"}),c(z,{prop:"result_card_id",label:"卡牌ID","min-width":"120"}),c(z,{label:"所需材料","min-width":"200"},{default:n(({row:e})=>[(h(!0),t(_,null,s(e.ingredients,e=>(h(),f(se,{key:e.card_id,class:"mr-2",type:"success",size:"small"},{default:n(()=>[u(p(e.card_reference)+" x"+p(e.quantity),1)]),_:2},1024))),128))]),_:1}),c(z,{label:"操作",width:"180",fixed:"right"},{default:n(({row:a})=>[c(i,{link:"",type:"primary",onClick:l=>((e,l)=>{_e.value={...e,collection_id:(null==l?void 0:l.collection_id)||"",pack_id:(null==l?void 0:l.pack_id)||""},ue.value=!0})(a,e)},{default:n(()=>l[25]||(l[25]=[u("查看详情")])),_:2,__:[25]},1032,["onClick"]),c(i,{link:"",type:"primary",onClick:e=>(e=>{he.value=e,be.result_card_id=e.result_card_id,be.card_collection_id=e.card_collection_id,be.pack_id=e.pack_id,be.pack_collection_id=e.pack_collection_id,be.ingredients=JSON.parse(JSON.stringify(e.ingredients)),be.originalIngredients=JSON.parse(JSON.stringify(e.ingredients)),fe.value=!0})(a)},{default:n(()=>l[26]||(l[26]=[u("编辑")])),_:2,__:[26]},1032,["onClick"]),c(i,{link:"",type:"danger",onClick:e=>(e=>{w.confirm("确定要删除该配方吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{await x(e.pack_collection_id,e.pack_id,e.result_card_id),b.success("删除配方成功"),Ce()}catch(l){b.error("删除配方失败")}}).catch(()=>{})})(a)},{default:n(()=>[c(ve,null,{default:n(()=>[c(v(V))]),_:1}),l[27]||(l[27]=u(" 删除 "))]),_:2,__:[27]},1032,["onClick"])]),_:2},1024)]),_:2},1032,["data"])])]),_:1}),c(z,{prop:"pack_id",label:"卡包ID","min-width":"150"}),c(z,{prop:"collection_id",label:"分类","min-width":"120"}),c(z,{prop:"cards_count",label:"配方数量",width:"100"})]),_:1},8,["data"])]),_:1}),c(nl,{modelValue:ue.value,"onUpdate:modelValue":l[2]||(l[2]=e=>ue.value=e),title:"卡片详情",size:"50%","destroy-on-close":!0,direction:"rtl"},{default:n(()=>[_e.value?(h(),t("div",E,[c(cl,{title:"基本信息",column:2,border:""},{default:n(()=>[c(tl,{label:"卡牌名称"},{default:n(()=>[u(p(_e.value.card_reference),1)]),_:1}),c(tl,{label:"卡牌ID"},{default:n(()=>[u(p(_e.value.result_card_id),1)]),_:1}),c(tl,{label:"所属卡包"},{default:n(()=>[u(p(_e.value.pack_id),1)]),_:1}),c(tl,{label:"分类"},{default:n(()=>[u(p(_e.value.collection_id),1)]),_:1})]),_:1}),c(rl,{"content-position":"left"},{default:n(()=>l[28]||(l[28]=[u("所需材料")])),_:1,__:[28]}),c(el,{data:_e.value.ingredients,border:"",style:{width:"100%"}},{default:n(()=>[c(z,{prop:"card_reference",label:"材料名称","min-width":"120"}),c(z,{prop:"card_id",label:"材料ID","min-width":"120"}),c(z,{prop:"quantity",label:"数量",width:"80",align:"center"})]),_:1},8,["data"])])):m("",!0)]),_:1},8,["modelValue"]),r("div",J,[c(ol,{"current-page":ce.value,"onUpdate:currentPage":l[3]||(l[3]=e=>ce.value=e),"page-size":re.value,"onUpdate:pageSize":l[4]||(l[4]=e=>re.value=e),"page-sizes":[10,20,50,100],total:(null==(a=ne.value.pagination)?void 0:a.total_items)||0,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:Ue,onCurrentChange:De},null,8,["current-page","page-size","total"])]),c(_l,{modelValue:me.value,"onUpdate:modelValue":l[10]||(l[10]=e=>me.value=e),title:"新增配方",width:"600px"},{footer:n(()=>[c(i,{onClick:l[9]||(l[9]=e=>me.value=!1)},{default:n(()=>l[33]||(l[33]=[u("取消")])),_:1,__:[33]}),c(i,{type:"primary",onClick:Le,loading:we.value},{default:n(()=>l[34]||(l[34]=[u("确定")])),_:1,__:[34]},8,["loading"])]),default:n(()=>[c(U,{model:ye,rules:Ve,ref_key:"recipeFormRef",ref:ge,"label-width":"100px"},{default:n(()=>[c(q,{label:"分类",prop:"card_collection_id"},{default:n(()=>[c(C,{modelValue:ye.card_collection_id,"onUpdate:modelValue":l[5]||(l[5]=e=>ye.card_collection_id=e),placeholder:"请选择分类",clearable:""},{default:n(()=>[(h(!0),t(_,null,s(pe.value,e=>(h(),f(d,{key:e.collection_id,label:e.name,value:e.storagePrefix},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),c(q,{label:"卡包分类",prop:"pack_collection_id"},{default:n(()=>[c(C,{modelValue:ye.pack_collection_id,"onUpdate:modelValue":l[6]||(l[6]=e=>ye.pack_collection_id=e),placeholder:"请选择卡包分类",clearable:"",onChange:Ze},{default:n(()=>[(h(!0),t(_,null,s(pe.value,e=>(h(),f(d,{key:e.storagePrefix,label:e.name,value:e.storagePrefix},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),c(q,{label:"卡包ID",prop:"pack_id"},{default:n(()=>[r("div",N,[c(I,{modelValue:ye.pack_id,"onUpdate:modelValue":l[7]||(l[7]=e=>ye.pack_id=e),placeholder:"请输入卡包ID",class:"pack-id-input"},null,8,["modelValue"]),c(i,{type:"primary",onClick:$e},{default:n(()=>l[29]||(l[29]=[u("选择卡包")])),_:1,__:[29]})])]),_:1}),c(q,{label:"目标卡牌",prop:"result_card_id"},{default:n(()=>[r("div",O,[c(I,{modelValue:ye.result_card_id,"onUpdate:modelValue":l[8]||(l[8]=e=>ye.result_card_id=e),placeholder:"请输入目标卡牌ID",class:"card-id-input"},null,8,["modelValue"]),c(i,{type:"primary",onClick:We},{default:n(()=>l[30]||(l[30]=[u("选择卡牌")])),_:1,__:[30]})]),Fe.value?(h(),t("div",B,[Fe.value.image_url?(h(),t("img",{key:0,src:Fe.value.image_url,class:"selected-card-image"},null,8,F)):(h(),t("div",R,"无图片")),r("div",T,[r("h4",null,p(Fe.value.card_name),1),r("p",null,"ID: "+p(Fe.value.id),1),r("p",null,"稀有度: "+p(Fe.value.rarity),1)])])):m("",!0)]),_:1}),c(q,{label:"材料"},{default:n(()=>[(h(!0),t(_,null,s(ye.ingredients,(e,a)=>(h(),t("div",{key:a,class:"ingredient-item"},[r("div",A,[c(I,{modelValue:e.card_id,"onUpdate:modelValue":l=>e.card_id=l,placeholder:"卡牌ID",class:"ingredient-input"},null,8,["modelValue","onUpdate:modelValue"]),c(i,{type:"primary",onClick:e=>(async e=>{ye.card_collection_id?(Te.value=!1,Ae.value=!0,Ge.value=!1,Re.value=e,Be.value="选择材料卡牌",await Ye(ye.card_collection_id)):b.warning("请先选择分类")})(a)},{default:n(()=>l[31]||(l[31]=[u("选择")])),_:2,__:[31]},1032,["onClick"])]),c(ul,{modelValue:e.quantity,"onUpdate:modelValue":l=>e.quantity=l,min:1,placeholder:"数量",class:"ingredient-input"},null,8,["modelValue","onUpdate:modelValue"]),c(i,{type:"danger",circle:"",onClick:e=>(e=>{ye.ingredients.splice(e,1),0===ye.ingredients.length&&Ke()})(a)},{default:n(()=>[c(ve,null,{default:n(()=>[c(v(V))]),_:1})]),_:2},1032,["onClick"])]))),128)),c(i,{type:"primary",icon:"Plus",onClick:Ke},{default:n(()=>l[32]||(l[32]=[u("添加材料")])),_:1,__:[32]})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),c(_l,{modelValue:fe.value,"onUpdate:modelValue":l[16]||(l[16]=e=>fe.value=e),title:"编辑配方",width:"600px"},{footer:n(()=>[c(i,{onClick:l[15]||(l[15]=e=>fe.value=!1)},{default:n(()=>l[37]||(l[37]=[u("取消")])),_:1,__:[37]}),c(i,{type:"primary",onClick:Xe,loading:we.value},{default:n(()=>l[38]||(l[38]=[u("确定")])),_:1,__:[38]},8,["loading"])]),default:n(()=>[c(U,{model:be,rules:Ve,ref_key:"editFormRef",ref:ke,"label-width":"100px"},{default:n(()=>[c(q,{label:"分类",prop:"card_collection_id"},{default:n(()=>[c(I,{modelValue:be.card_collection_id,"onUpdate:modelValue":l[11]||(l[11]=e=>be.card_collection_id=e),placeholder:"请输入分类",disabled:""},null,8,["modelValue"])]),_:1}),c(q,{label:"卡包分类",prop:"pack_collection_id"},{default:n(()=>[c(I,{modelValue:be.pack_collection_id,"onUpdate:modelValue":l[12]||(l[12]=e=>be.pack_collection_id=e),placeholder:"请输入卡包分类",disabled:""},null,8,["modelValue"])]),_:1}),c(q,{label:"卡包ID",prop:"pack_id"},{default:n(()=>[r("div",G,[c(I,{modelValue:be.pack_id,"onUpdate:modelValue":l[13]||(l[13]=e=>be.pack_id=e),placeholder:"请输入卡包ID",disabled:"",class:"pack-id-input"},null,8,["modelValue"])])]),_:1}),c(q,{label:"目标卡牌",prop:"result_card_id"},{default:n(()=>[r("div",H,[c(I,{modelValue:be.result_card_id,"onUpdate:modelValue":l[14]||(l[14]=e=>be.result_card_id=e),placeholder:"请输入目标卡牌ID",disabled:"",class:"card-id-input"},null,8,["modelValue"])])]),_:1}),c(q,{label:"材料"},{default:n(()=>[(h(!0),t(_,null,s(be.ingredients,(e,a)=>(h(),t("div",{key:a,class:"ingredient-item"},[r("div",K,[c(I,{modelValue:e.card_id,"onUpdate:modelValue":l=>e.card_id=l,placeholder:"卡牌ID",class:"ingredient-input"},null,8,["modelValue","onUpdate:modelValue"]),c(i,{type:"primary",onClick:e=>(async e=>{Te.value=!1,Ae.value=!1,Ge.value=!0,Re.value=e,Be.value="选择材料卡牌",await Ye(be.card_collection_id)})(a)},{default:n(()=>l[35]||(l[35]=[u("选择")])),_:2,__:[35]},1032,["onClick"])]),c(ul,{modelValue:e.quantity,"onUpdate:modelValue":l=>e.quantity=l,min:1,placeholder:"数量",class:"ingredient-input"},null,8,["modelValue","onUpdate:modelValue"]),c(i,{type:"danger",circle:"",onClick:e=>(e=>{be.ingredients.splice(e,1),0===be.ingredients.length&&Me()})(a)},{default:n(()=>[c(ve,null,{default:n(()=>[c(v(V))]),_:1})]),_:2},1032,["onClick"])]))),128)),c(i,{type:"primary",icon:"Plus",onClick:Me},{default:n(()=>l[36]||(l[36]=[u("添加材料")])),_:1,__:[36]})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),c(_l,{modelValue:ze.value,"onUpdate:modelValue":l[20]||(l[20]=e=>ze.value=e),title:"选择卡包",width:"80%","destroy-on-close":!1},{default:n(()=>{var e;return[g((h(),t("div",null,[r("div",L,[c(U,{model:je,inline:""},{default:n(()=>[c(q,{label:"搜索"},{default:n(()=>[c(I,{modelValue:je.searchQuery,"onUpdate:modelValue":l[17]||(l[17]=e=>je.searchQuery=e),placeholder:"请输入卡包名称",clearable:"",onKeyup:k(ll,["enter"])},null,8,["modelValue"])]),_:1}),c(q,null,{default:n(()=>[c(i,{type:"primary",onClick:ll},{default:n(()=>l[39]||(l[39]=[u("查询")])),_:1,__:[39]}),c(i,{onClick:al},{default:n(()=>l[40]||(l[40]=[u("重置")])),_:1,__:[40]})]),_:1})]),_:1},8,["model"])]),Qe.value.packs&&Qe.value.packs.length>0?(h(),f(el,{key:0,data:Qe.value.packs,border:"",stripe:""},{default:n(()=>[c(z,{prop:"pack_id",label:"ID",width:"180"}),c(z,{prop:"pack_name",label:"名称"}),c(z,{prop:"fusion_count",label:"配方数量",width:"100"}),c(z,{label:"图片",width:"120"},{default:n(({row:e})=>[e.image_url?(h(),f(sl,{key:0,src:e.image_url,style:{width:"50px",height:"50px"},fit:"contain"},null,8,["src"])):(h(),t("span",M,"无图片"))]),_:1}),c(z,{label:"操作",width:"120",fixed:"right"},{default:n(({row:e})=>[c(i,{link:"",type:"primary",onClick:l=>{return a=e,ye.pack_id=a.pack_id,a.pack_id&&ye.pack_collection_id&&(ye.collection_metadata_id=ye.pack_collection_id),void(ze.value=!1);var a}},{default:n(()=>l[41]||(l[41]=[u("选择")])),_:2,__:[41]},1032,["onClick"])]),_:1})]),_:1},8,["data"])):(h(),t("div",X,[c(pl,{description:"暂无卡包数据"})])),Qe.value.pagination?(h(),t("div",W,[c(ol,{"current-page":Se.value,"onUpdate:currentPage":l[18]||(l[18]=e=>Se.value=e),"page-size":Ee.value,"onUpdate:pageSize":l[19]||(l[19]=e=>Ee.value=e),"page-sizes":[10,20,50,100],total:(null==(e=Qe.value.pagination)?void 0:e.total_items)||0,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:il,onCurrentChange:dl},null,8,["current-page","page-size","total"])])):m("",!0)])),[[vl,Pe.value]])]}),_:1},8,["modelValue"]),c(_l,{modelValue:Je.value,"onUpdate:modelValue":l[21]||(l[21]=e=>Je.value=e),title:Be.value,width:"80%","destroy-on-close":!1},{default:n(()=>[g((h(),t("div",null,[Oe.value.length>0?(h(),t("div",Y,[(h(!0),t(_,null,s(Oe.value,e=>(h(),t("div",{key:e.id,class:"card-item",onClick:l=>(e=>{Te.value?(ye.result_card_id=e.id,Fe.value=e):Ae.value?Re.value>=0&&Re.value<ye.ingredients.length&&(ye.ingredients[Re.value].card_id=e.id):Ge.value&&Re.value>=0&&Re.value<be.ingredients.length&&(be.ingredients[Re.value].card_id=e.id),Je.value=!1})(e)},[c(D,{"body-style":{padding:"0px"},shadow:"hover"},{default:n(()=>[e.image_url?(h(),t("img",{key:0,src:e.image_url,class:"card-image"},null,8,$)):(h(),t("div",ee,"无图片")),r("div",le,[r("div",ae,p(e.card_name),1),r("div",ie,"稀有度: "+p(e.rarity),1),r("div",de,"点数: "+p(e.point_worth),1)])]),_:2},1024)],8,Z))),128))])):(h(),t("div",te,[c(pl,{description:"暂无卡片数据"})]))])),[[vl,Ne.value]])]),_:1},8,["modelValue","title"])])}}}),[["__scopeId","data-v-dba4137e"]]);export{ce as default};
