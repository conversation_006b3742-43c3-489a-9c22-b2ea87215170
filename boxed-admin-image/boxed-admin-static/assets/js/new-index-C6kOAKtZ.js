import{x as e,X as a,r as l,h as t,a3 as r,Y as s,y as u,P as i,H as d,ag as n,L as o,I as p,A as c,aq as _,G as v,M as m,J as h,aB as b,z as f}from"./vue-DWGM1jOu.js";import{r as g}from"./request-ZE4ZUch8.js";import{E as y}from"./elementPlus-Bm-E4Nxd.js";import{_ as w}from"./index-Br8T7GvP.js";const k={class:"shipping-container"},V=["onClick"],x={class:"address-ellipsis"},C={class:"pagination-container"},I={class:"pagination-info"},S={class:"tracking-info"},O={key:0},B={key:1},q={key:0},D={key:1},$={class:"mt-20"},F={class:"action-area mt-20"},U={class:"dialog-footer"},j=w(e({name:"ShippingNewIndex",__name:"new-index",setup(e){b();const w=a({status:"",sortBy:"created_at",sortOrder:"desc",limit:10,cursor:""}),j=l("all"),z=l([]),J=l(!1),N=l(!1),P=l(!1),A=l(!1),E=l(!1),G=l(null),H=l(!1),L=l(!1),M=l(null),R=a({status:"",shipping_status:""}),T=async(e=!0)=>{e?(J.value=!0,w.cursor="",z.value=[]):N.value=!0;try{const a={limit:w.limit,sort_by:w.sortBy,sort_order:w.sortOrder};let l;w.status&&(a.status=w.status),w.cursor&&!e&&(a.cursor=w.cursor),l="inactive"===j.value?await(async e=>g.get("/shipping/withdraw-requests/inactive",{params:e}))(a):await(async e=>g.get("/shipping/withdraw-requests",{params:e}))(a),z.value=e?l.withdraw_requests:[...z.value,...l.withdraw_requests],w.cursor=l.pagination.next_cursor||"",P.value=l.pagination.has_more}catch(a){y.error("获取物流列表失败")}finally{J.value=!1,N.value=!1}},X=()=>{w.cursor&&T(!1)},Y=e=>{if(!e)return"";return{processing:"info",label_created:"warning",shipped:"primary",in_transit:"success",delivered:"success",cancelled:"danger"}[e]||""},K=e=>{if(!e)return"";return{processing:"处理中",label_created:"标签已创建",shipped:"已发货",in_transit:"运输中",delivered:"已送达",cancelled:"已取消"}[e]||e},Q=e=>e?`${e.street}, ${e.city}, ${e.state}, ${e.zip}, ${e.country}`:"",W=()=>{j.value="all",w.status="",w.sortBy="created_at",w.sortOrder="desc",w.cursor="",T()},Z=()=>{T()},ee=e=>{e.prop&&e.order&&(w.sortBy=e.prop,w.sortOrder="ascending"===e.order?"asc":"desc",T())},ae=e=>{navigator.clipboard.writeText(e).then(()=>{y.success("已复制到剪贴板")}).catch(()=>{y.error("复制失败")})},le=async e=>{A.value=!0,E.value=!0;try{const a=await(async(e,a)=>g.get(`/shipping/withdraw-requests/${e}/${a}`))(e.user_id,e.id);G.value=a}catch(a){y.error("获取物流详情失败")}finally{E.value=!1}},te=e=>{M.value=e,R.status=e.status,R.shipping_status=e.shipping_status,H.value=!0},re=async()=>{if(M.value){L.value=!0;try{await(async(e,a,l)=>g.put(`/shipping/withdraw-requests/${e}/${a}/status`,l))(M.value.user_id,M.value.id,{status:R.status,shipping_status:R.shipping_status}),y.success("更新物流状态成功"),H.value=!1,T()}catch(e){y.error("更新物流状态失败")}finally{L.value=!1}}};return t(()=>{T()}),r(()=>{localStorage.setItem("shipping_new_state",JSON.stringify({activeStatus:j.value,searchForm:{status:w.status,sortBy:w.sortBy,sortOrder:w.sortOrder,limit:w.limit}}))}),s(()=>{const e=localStorage.getItem("shipping_new_state");if(e){const a=JSON.parse(e);a.activeStatus&&(j.value=a.activeStatus),a.searchForm&&(w.status=a.searchForm.status||"",w.sortBy=a.searchForm.sortBy||"created_at",w.sortOrder=a.searchForm.sortOrder||"desc",w.limit=a.searchForm.limit||10)}T()}),(e,a)=>{const l=n("el-option"),t=n("el-select"),r=n("el-form-item"),s=n("el-button"),b=n("el-form"),g=n("el-card"),y=n("el-table-column"),j=n("el-tooltip"),T=n("el-tag"),se=n("el-table"),ue=n("el-descriptions-item"),ie=n("el-descriptions"),de=n("el-link"),ne=n("el-image"),oe=n("el-drawer"),pe=n("el-dialog"),ce=_("loading");return f(),u("div",k,[i(g,{class:"search-card"},{default:d(()=>[i(b,{ref:"searchFormRef",inline:""},{default:d(()=>[i(r,{label:"物流状态",prop:"status"},{default:d(()=>[i(t,{modelValue:w.status,"onUpdate:modelValue":a[0]||(a[0]=e=>w.status=e),placeholder:"请选择物流状态",clearable:"",style:{width:"220px"}},{default:d(()=>[i(l,{label:"处理中",value:"processing"}),i(l,{label:"标签已创建",value:"label_created"}),i(l,{label:"已发货",value:"shipped"}),i(l,{label:"运输中",value:"in_transit"}),i(l,{label:"已送达",value:"delivered"}),i(l,{label:"已取消",value:"cancelled"})]),_:1},8,["modelValue"])]),_:1}),i(r,{label:"排序字段",prop:"sortBy"},{default:d(()=>[i(t,{modelValue:w.sortBy,"onUpdate:modelValue":a[1]||(a[1]=e=>w.sortBy=e),placeholder:"请选择排序字段",style:{width:"150px"},clearable:""},{default:d(()=>[i(l,{label:"创建时间",value:"created_at"}),i(l,{label:"状态",value:"status"})]),_:1},8,["modelValue"])]),_:1}),i(r,{label:"排序方式",prop:"sortOrder"},{default:d(()=>[i(t,{modelValue:w.sortOrder,"onUpdate:modelValue":a[2]||(a[2]=e=>w.sortOrder=e),placeholder:"请选择排序方式",style:{width:"150px"},clearable:""},{default:d(()=>[i(l,{label:"升序",value:"asc"}),i(l,{label:"降序",value:"desc"})]),_:1},8,["modelValue"])]),_:1}),i(r,null,{default:d(()=>[i(s,{type:"primary",onClick:Z},{default:d(()=>a[11]||(a[11]=[o("查询")])),_:1,__:[11]}),i(s,{onClick:W},{default:d(()=>a[12]||(a[12]=[o("重置")])),_:1,__:[12]})]),_:1})]),_:1},512)]),_:1}),i(g,{class:"table-card"},{default:d(()=>[p((f(),v(se,{data:z.value,border:"",stripe:"",style:{width:"100%"},"default-sort":{prop:"created_at",order:"descending"},onSortChange:ee},{default:d(()=>[i(y,{prop:"id",label:"物流ID",width:"120",sortable:"custom"}),i(y,{prop:"user_id",label:"用户ID",width:"120",sortable:"custom"}),i(y,{prop:"tracking_number",label:"追踪号",width:"150",sortable:"custom"},{default:d(({row:e})=>[i(j,{effect:"dark",content:`点击复制: ${e.tracking_number}`,placement:"top"},{default:d(()=>[c("span",{class:"tracking-number",onClick:a=>ae(e.tracking_number)},m(e.tracking_number),9,V)]),_:2},1032,["content"])]),_:1}),i(y,{prop:"card_count",label:"卡片数量",width:"100",sortable:"custom"}),i(y,{prop:"status",label:"状态",width:"120",sortable:"custom"},{default:d(({row:e})=>[i(T,{type:Y(e.status)},{default:d(()=>[o(m(K(e.status)),1)]),_:2},1032,["type"])]),_:1}),i(y,{prop:"shipping_address.name",label:"收件人",width:"100",sortable:"custom"}),i(y,{label:"地址","min-width":"200"},{default:d(({row:e})=>[i(j,{effect:"dark",content:Q(e.shipping_address),placement:"top"},{default:d(()=>[c("div",x,m(Q(e.shipping_address)),1)]),_:2},1032,["content"])]),_:1}),i(y,{prop:"created_at",label:"创建时间",width:"180",sortable:"custom"}),i(y,{label:"操作",width:"280",fixed:"right"},{default:d(({row:e})=>[i(s,{link:"",type:"primary",onClick:a=>le(e)},{default:d(()=>a[13]||(a[13]=[o("查看")])),_:2,__:[13]},1032,["onClick"]),i(s,{link:"",type:"primary",onClick:a=>te(e)},{default:d(()=>a[14]||(a[14]=[o("更新状态")])),_:2,__:[14]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[ce,J.value]]),c("div",C,[c("div",I,[c("span",null,"每页 "+m(w.limit)+" 条，共 "+m(z.value.length)+" 条",1),P.value?(f(),v(s,{key:0,type:"primary",plain:"",size:"small",onClick:X,loading:N.value},{default:d(()=>a[15]||(a[15]=[o(" 加载更多 ")])),_:1,__:[15]},8,["loading"])):h("",!0)])])]),_:1}),i(oe,{modelValue:A.value,"onUpdate:modelValue":a[6]||(a[6]=e=>A.value=e),title:"物流详情",size:"50%","destroy-on-close":!1,direction:"rtl"},{default:d(()=>{var e;return[p((f(),u("div",null,[i(ie,{title:"基本信息",column:2,border:""},{default:d(()=>[i(ue,{label:"物流ID"},{default:d(()=>{var e;return[o(m(null==(e=G.value)?void 0:e.id),1)]}),_:1}),i(ue,{label:"用户ID"},{default:d(()=>{var e;return[o(m(null==(e=G.value)?void 0:e.user_id),1)]}),_:1}),i(ue,{label:"追踪号"},{default:d(()=>{var e,l;return[c("div",S,[c("span",null,m(null==(e=G.value)?void 0:e.tracking_number),1),(null==(l=G.value)?void 0:l.tracking_number)?(f(),v(s,{key:0,type:"primary",link:"",onClick:a[3]||(a[3]=e=>{var a;return ae(null==(a=G.value)?void 0:a.tracking_number)})},{default:d(()=>a[16]||(a[16]=[o(" 复制 ")])),_:1,__:[16]})):h("",!0)])]}),_:1}),i(ue,{label:"卡片数量"},{default:d(()=>{var e;return[o(m(null==(e=G.value)?void 0:e.card_count),1)]}),_:1}),i(ue,{label:"状态"},{default:d(()=>{var e;return[i(T,{type:Y(null==(e=G.value)?void 0:e.status)},{default:d(()=>{var e;return[o(m(K(null==(e=G.value)?void 0:e.status)),1)]}),_:1},8,["type"])]}),_:1}),i(ue,{label:"物流状态"},{default:d(()=>{var e;return[i(T,{type:Y(null==(e=G.value)?void 0:e.shipping_status)},{default:d(()=>{var e;return[o(m(K(null==(e=G.value)?void 0:e.shipping_status)),1)]}),_:1},8,["type"])]}),_:1}),i(ue,{label:"创建时间"},{default:d(()=>{var e;return[o(m(null==(e=G.value)?void 0:e.created_at),1)]}),_:1}),i(ue,{label:"请求时间"},{default:d(()=>{var e;return[o(m(null==(e=G.value)?void 0:e.request_date),1)]}),_:1})]),_:1}),i(ie,{title:"收件人信息",column:2,border:"",class:"mt-20"},{default:d(()=>[i(ue,{label:"收件人"},{default:d(()=>{var e,a;return[o(m(null==(a=null==(e=G.value)?void 0:e.shipping_address)?void 0:a.name),1)]}),_:1}),i(ue,{label:"地址ID"},{default:d(()=>{var e,a;return[o(m(null==(a=null==(e=G.value)?void 0:e.shipping_address)?void 0:a.id),1)]}),_:1}),i(ue,{label:"详细地址",span:2},{default:d(()=>{var e;return[o(m(Q(null==(e=G.value)?void 0:e.shipping_address)),1)]}),_:1})]),_:1}),i(ie,{title:"Shippo信息",column:2,border:"",class:"mt-20"},{default:d(()=>[i(ue,{label:"Shippo地址ID"},{default:d(()=>{var e;return[o(m(null==(e=G.value)?void 0:e.shippo_address_id),1)]}),_:1}),i(ue,{label:"Shippo包裹ID"},{default:d(()=>{var e;return[o(m(null==(e=G.value)?void 0:e.shippo_parcel_id),1)]}),_:1}),i(ue,{label:"Shippo运单ID"},{default:d(()=>{var e;return[o(m(null==(e=G.value)?void 0:e.shippo_shipment_id),1)]}),_:1}),i(ue,{label:"Shippo交易ID"},{default:d(()=>{var e;return[o(m(null==(e=G.value)?void 0:e.shippo_transaction_id),1)]}),_:1}),i(ue,{label:"运单标签",span:2},{default:d(()=>{var e,l;return[(null==(e=G.value)?void 0:e.shippo_label_url)?(f(),u("div",O,[i(de,{href:null==(l=G.value)?void 0:l.shippo_label_url,target:"_blank",type:"primary"},{default:d(()=>a[17]||(a[17]=[o("查看运单标签")])),_:1,__:[17]},8,["href"])])):(f(),u("span",B,"-"))]}),_:1}),i(ue,{label:"追踪链接",span:2},{default:d(()=>{var e,l;return[(null==(e=G.value)?void 0:e.tracking_url)?(f(),u("div",q,[i(de,{href:null==(l=G.value)?void 0:l.tracking_url,target:"_blank",type:"primary"},{default:d(()=>a[18]||(a[18]=[o("查看物流追踪")])),_:1,__:[18]},8,["href"])])):(f(),u("span",D,"-"))]}),_:1})]),_:1}),c("div",$,[a[19]||(a[19]=c("div",{class:"section-header"},[c("span",{class:"section-title"},"卡片信息")],-1)),i(se,{data:(null==(e=G.value)?void 0:e.cards)||[],border:"",stripe:"",style:{width:"100%"}},{default:d(()=>[i(y,{prop:"card_name",label:"卡片名称","min-width":"120"}),i(y,{prop:"id",label:"卡片ID","min-width":"120"}),i(y,{prop:"quantity",label:"数量",width:"80"}),i(y,{prop:"rarity",label:"稀有度",width:"80"}),i(y,{prop:"point_worth",label:"点数价值",width:"100"}),i(y,{label:"图片",width:"120"},{default:d(({row:e})=>[i(ne,{style:{width:"80px",height:"80px"},src:e.image_url,"preview-src-list":[e.image_url],fit:"cover"},null,8,["src","preview-src-list"])]),_:1}),i(y,{label:"获取时间","min-width":"180"},{default:d(({row:e})=>[o(m(e.date_got||"-"),1)]),_:1})]),_:1},8,["data"])]),c("div",F,[i(s,{type:"primary",onClick:a[4]||(a[4]=e=>te(G.value))},{default:d(()=>a[20]||(a[20]=[o("更新状态")])),_:1,__:[20]}),i(s,{onClick:a[5]||(a[5]=e=>A.value=!1)},{default:d(()=>a[21]||(a[21]=[o("关闭")])),_:1,__:[21]})])])),[[ce,E.value]])]}),_:1},8,["modelValue"]),i(pe,{modelValue:H.value,"onUpdate:modelValue":a[10]||(a[10]=e=>H.value=e),title:"更新物流状态",width:"400px"},{footer:d(()=>[c("span",U,[i(s,{onClick:a[9]||(a[9]=e=>H.value=!1)},{default:d(()=>a[22]||(a[22]=[o("取消")])),_:1,__:[22]}),i(s,{type:"primary",onClick:re,loading:L.value},{default:d(()=>a[23]||(a[23]=[o("确认更新")])),_:1,__:[23]},8,["loading"])])]),default:d(()=>[i(b,{model:R,"label-width":"100px"},{default:d(()=>[i(r,{label:"当前状态"},{default:d(()=>{var e;return[i(T,{type:Y(null==(e=M.value)?void 0:e.status)},{default:d(()=>{var e;return[o(m(K(null==(e=M.value)?void 0:e.status)),1)]}),_:1},8,["type"])]}),_:1}),i(r,{label:"新状态",prop:"status"},{default:d(()=>[i(t,{modelValue:R.status,"onUpdate:modelValue":a[7]||(a[7]=e=>R.status=e),placeholder:"请选择新状态",style:{width:"100%"}},{default:d(()=>[i(l,{label:"处理中",value:"processing"}),i(l,{label:"标签已创建",value:"label_created"}),i(l,{label:"已发货",value:"shipped"}),i(l,{label:"运输中",value:"in_transit"}),i(l,{label:"已送达",value:"delivered"}),i(l,{label:"已取消",value:"cancelled"})]),_:1},8,["modelValue"])]),_:1}),i(r,{label:"物流状态",prop:"shipping_status"},{default:d(()=>[i(t,{modelValue:R.shipping_status,"onUpdate:modelValue":a[8]||(a[8]=e=>R.shipping_status=e),placeholder:"请选择物流状态",style:{width:"100%"}},{default:d(()=>[i(l,{label:"处理中",value:"processing"}),i(l,{label:"标签已创建",value:"label_created"}),i(l,{label:"已发货",value:"shipped"}),i(l,{label:"运输中",value:"in_transit"}),i(l,{label:"已送达",value:"delivered"}),i(l,{label:"已取消",value:"cancelled"})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-7b23db16"]]);export{j as default};
