const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/js/packs-CZud9J-M.js","assets/js/request-ZE4ZUch8.js"])))=>i.map(i=>d[i]);
import{a as e,_ as a}from"./index-Br8T7GvP.js";import{x as l,r as i,aw as t,h as u,y as o,P as d,H as r,ag as n,I as s,aq as c,J as v,A as p,M as _,L as m,G as f,O as y,a6 as g,a4 as b,aB as h,z as w}from"./vue-DWGM1jOu.js";import{getPackDetail as k,getPackCards as V,createCard as x,deleteCard as C,updatePackActiveStatus as U,updatePackMaxWin as q,updatePackMinWin as z,updatePackPrice as W}from"./packs-CZud9J-M.js";import{h as I,a as j}from"./storage-BmuEW5sW.js";import{E as D,x as P}from"./elementPlus-Bm-E4Nxd.js";import"./request-ZE4ZUch8.js";const $={class:"pack-detail-container"},B={class:"card-header"},E={key:0,class:"pack-info"},S={class:"pack-image"},T=["src"],A={class:"pack-details"},L={class:"divider"},O={class:"card-actions"},R={class:"select-card-container"},F={class:"filter-section"},G={key:0,class:"card-grid"},H=["onClick"],J=["src"],K={key:1,class:"card-image-placeholder"},M={class:"card-info"},N={class:"card-name"},Q={class:"card-rarity"},X={class:"card-points"},Y={class:"card-quantity"},Z={key:1,class:"no-cards"},ee={class:"pagination"},ae={key:0,class:"selected-card-info"},le={class:"selected-card-image"},ie={class:"dialog-footer"},te={class:"dialog-footer"},ue={class:"dialog-footer"},oe={class:"dialog-footer"},de=a(l({__name:"detail",setup(a){const l=t(),de=h(),re=i(!1),ne=i(!1),se=i(!1),ce=i(!1),ve=i(null),pe=i([]),_e=i(!1),me=i(!1),fe=i(!1),ye=i(!1),ge=i(!1),be=i(),he=i({maxWin:0}),we=i({minWin:0}),ke=i({price:0}),Ve=i([]),xe=i(""),Ce=i(""),Ue=i([]),qe=i(1),ze=i(10),We=i(0),Ie=i(null),je=i({collection_metadata_id:"",document_id:"",probability:0,condition:"new"}),De=i({collection_metadata_id:[{required:!0,message:"请输入卡片元数据ID",trigger:"blur"}],document_id:[{required:!0,message:"请输入文档ID",trigger:"blur"}],condition:[{required:!0,message:"请选择状态",trigger:"change"}]}),Pe=l.params.id,$e=i(""),Be=async()=>{if($e.value){ne.value=!0;try{pe.value=await V($e.value,Pe,{sort_by:"point_worth"})}catch(e){D.error("获取卡片列表失败")}finally{ne.value=!1}}},Ee=()=>{me.value=!0,Ie.value=null,Se(),qe.value=1,Te()},Se=async()=>{try{const e=await I();Ve.value=e,e.length>0&&!xe.value&&(xe.value=e[0].id)}catch(e){D.error("获取卡片分类失败")}},Te=async()=>{se.value=!0;try{const e={page:qe.value,per_page:ze.value,collectionName:xe.value||void 0,search_query:Ce.value||void 0},a=await j(e);Ue.value=a.cards,We.value=a.pagination.total_items}catch(e){D.error("获取可选卡片列表失败")}finally{se.value=!1}},Ae=e=>{Ie.value=e,me.value=!1,_e.value=!0,je.value={collection_metadata_id:Ie.value?xe.value:"",document_id:Ie.value?Ie.value.id:"",probability:0,condition:"new"}},Le=()=>{Te()},Oe=()=>{qe.value=1,Te()},Re=()=>{Ce.value="",qe.value=1,Te()},Fe=e=>{qe.value=e,Te(),Ie.value=null,je.value={collection_metadata_id:"",document_id:"",probability:0,condition:"new"}},Ge=async()=>{be.value&&await be.value.validate(async e=>{if(e){ce.value=!0;try{await x($e.value,Pe,je.value),D.success("添加卡片成功"),_e.value=!1,Be(),je.value={collection_metadata_id:"",document_id:"",probability:0,condition:"new"}}catch(a){D.error("添加卡片失败")}finally{ce.value=!1}}})},He=async()=>{if(!ve.value)return;const e=!ve.value.is_active,a=e?"激活":"未激活";try{await U($e.value,Pe,e),D.success(`设置卡包为${a}状态成功`),ve.value&&(ve.value.is_active=e)}catch(l){D.error(`设置卡包为${a}状态失败`)}},Je=()=>{ve.value&&(he.value.maxWin=ve.value.max_win,fe.value=!0)},Ke=async()=>{if(ve.value){ce.value=!0;try{await q($e.value,Pe,he.value.maxWin),D.success("最大中奖金额修改成功"),ve.value.max_win=he.value.maxWin,fe.value=!1}catch(e){D.error("修改最大中奖金额失败")}finally{ce.value=!1}}},Me=()=>{ve.value&&(we.value.minWin=ve.value.min_win||0,ye.value=!0)},Ne=async()=>{if(ve.value){ce.value=!0;try{await z($e.value,Pe,we.value.minWin),D.success("最小中奖金额修改成功"),ve.value.min_win=we.value.minWin,ye.value=!1}catch(e){D.error("修改最小中奖金额失败")}finally{ce.value=!1}}},Qe=()=>{ve.value&&(ke.value.price=ve.value.price,ge.value=!0)},Xe=async()=>{if(ve.value){ce.value=!0;try{await W($e.value,Pe,ke.value.price),D.success("价格修改成功"),ve.value.price=ke.value.price,ge.value=!1}catch(e){D.error("修改价格失败")}finally{ce.value=!1}}},Ye=()=>{$e.value?de.push(`/storage/packs?collectionId=${$e.value}`):de.push("/storage/packs")};return u(()=>{(async()=>{re.value=!0;try{const a=l.query.collectionId;if(a)$e.value=a;else{const a=await e(()=>import("./packs-CZud9J-M.js"),__vite__mapDeps([0,1])).then(e=>e.getPackCollections());if(!(a&&a.length>0))return void D.error("获取卡包集合失败");$e.value=a[0].id.toString()}ve.value=await k(Pe,$e.value),Be()}catch(a){D.error("获取卡包详情失败")}finally{re.value=!1}})()}),(e,a)=>{const l=n("el-button"),i=n("el-descriptions-item"),t=n("el-tag"),u=n("el-descriptions"),h=n("el-divider"),k=n("el-table-column"),V=n("el-image"),x=n("el-table"),U=n("el-card"),q=n("el-option"),z=n("el-select"),W=n("el-form-item"),I=n("el-input"),j=n("el-form"),de=n("el-empty"),Se=n("el-pagination"),Te=n("el-dialog"),Ze=n("el-input-number"),ea=c("loading");return w(),o("div",$,[d(U,{class:"box-card"},{header:r(()=>[p("div",B,[a[20]||(a[20]=p("span",null,"卡包详情",-1)),d(l,{onClick:Ye},{default:r(()=>a[19]||(a[19]=[m("返回")])),_:1,__:[19]})])]),default:r(()=>[s((w(),o("div",null,[ve.value?(w(),o("div",E,[p("div",S,[p("img",{src:ve.value.image_url,alt:"卡包图片"},null,8,T)]),p("div",A,[p("h2",null,_(ve.value.name),1),d(u,{column:2,border:""},{default:r(()=>[d(i,{label:"ID"},{default:r(()=>[m(_(ve.value.id),1)]),_:1}),d(i,{label:"价格"},{default:r(()=>[m(_(ve.value.price)+" ",1),d(l,{size:"small",type:"primary",style:{"margin-left":"10px"},onClick:Qe},{default:r(()=>a[21]||(a[21]=[m(" 修改价格 ")])),_:1,__:[21]})]),_:1}),d(i,{label:"中奖率"},{default:r(()=>[m(_(ve.value.win_rate)+"%",1)]),_:1}),d(i,{label:"最大中奖金额"},{default:r(()=>[m(_(ve.value.max_win)+" ",1),d(l,{size:"small",type:"primary",style:{"margin-left":"10px"},onClick:Je},{default:r(()=>a[22]||(a[22]=[m(" 修改最大中奖金额 ")])),_:1,__:[22]})]),_:1}),d(i,{label:"最小中奖金额"},{default:r(()=>[m(_(ve.value.min_win)+" ",1),d(l,{size:"small",type:"primary",style:{"margin-left":"10px"},onClick:Me},{default:r(()=>a[23]||(a[23]=[m(" 修改最小中奖金额 ")])),_:1,__:[23]})]),_:1}),d(i,{label:"热度"},{default:r(()=>[m(_(ve.value.popularity),1)]),_:1}),d(i,{label:"创建时间"},{default:r(()=>[m(_(ve.value.created_at),1)]),_:1}),d(i,{label:"状态"},{default:r(()=>[d(t,{type:ve.value.is_active?"success":"danger"},{default:r(()=>[m(_(ve.value.is_active?"激活":"未激活"),1)]),_:1},8,["type"]),d(l,{size:"small",type:ve.value.is_active?"danger":"success",style:{"margin-left":"10px"},onClick:He},{default:r(()=>[m(_(ve.value.is_active?"设为未激活":"设为激活"),1)]),_:1},8,["type"])]),_:1})]),_:1})])])):v("",!0),p("div",L,[d(h,{"content-position":"center"},{default:r(()=>a[24]||(a[24]=[m("卡片列表")])),_:1,__:[24]})]),p("div",O,[d(l,{type:"primary",onClick:Ee},{default:r(()=>a[25]||(a[25]=[m("添加卡片")])),_:1,__:[25]})]),s((w(),f(x,{data:pe.value,style:{width:"100%"}},{default:r(()=>[d(k,{prop:"id",label:"ID",width:"80"}),d(k,{prop:"card_name",label:"卡片名称"}),d(k,{prop:"rarity",label:"稀有度"}),d(k,{prop:"point_worth",label:"积分价值"}),d(k,{prop:"condition",label:"状态"}),d(k,{label:"图片",width:"100"},{default:r(e=>[d(V,{style:{width:"50px",height:"50px"},src:e.row.image_url,"preview-src-list":[e.row.image_url]},null,8,["src","preview-src-list"])]),_:1}),d(k,{prop:"probability",label:"概率"}),d(k,{label:"操作",width:"120",fixed:"right"},{default:r(({row:e})=>[d(l,{type:"danger",size:"small",onClick:a=>(async e=>{P.confirm(`确定要删除卡片 "${e.card_name}" 吗？此操作不可逆`,"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{await C($e.value,Pe,e.id),D.success("删除卡片成功"),Be()}catch(a){D.error("删除卡片失败")}}).catch(()=>{})})(e)},{default:r(()=>a[26]||(a[26]=[m("删除")])),_:2,__:[26]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[ea,ne.value]])])),[[ea,re.value]])]),_:1}),d(Te,{modelValue:me.value,"onUpdate:modelValue":a[5]||(a[5]=e=>me.value=e),title:"选择卡片",width:"80%"},{default:r(()=>[s((w(),o("div",R,[p("div",F,[d(j,{inline:!0},{default:r(()=>[d(W,{label:"卡片分类"},{default:r(()=>[d(z,{modelValue:xe.value,"onUpdate:modelValue":a[0]||(a[0]=e=>xe.value=e),placeholder:"请选择卡片分类",onChange:Le},{default:r(()=>[(w(!0),o(y,null,g(Ve.value,e=>(w(),f(q,{key:e.firestoreCollection,label:e.name,value:e.firestoreCollection},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),d(W,{label:"搜索"},{default:r(()=>[d(I,{modelValue:Ce.value,"onUpdate:modelValue":a[1]||(a[1]=e=>Ce.value=e),placeholder:"请输入卡片名称",clearable:"",onKeyup:b(Oe,["enter"]),onClear:Oe},null,8,["modelValue"])]),_:1}),d(W,null,{default:r(()=>[d(l,{type:"primary",onClick:Oe},{default:r(()=>a[27]||(a[27]=[m("搜索")])),_:1,__:[27]}),d(l,{onClick:Re},{default:r(()=>a[28]||(a[28]=[m("重置")])),_:1,__:[28]})]),_:1})]),_:1})]),Ue.value.length>0?(w(),o("div",G,[(w(!0),o(y,null,g(Ue.value,e=>(w(),o("div",{key:e.id,class:"card-item",onClick:a=>Ae(e)},[d(U,{"body-style":{padding:"0px"},shadow:"hover"},{default:r(()=>[e.image_url?(w(),o("img",{key:0,src:e.image_url,class:"card-image"},null,8,J)):(w(),o("div",K,"无图片")),p("div",M,[p("div",N,_(e.card_name),1),p("div",Q,"稀有度: "+_(e.rarity),1),p("div",X,"点数: "+_(e.point_worth),1),p("div",Y,"库存: "+_(e.quantity),1)])]),_:2},1024)],8,H))),128))])):(w(),o("div",Z,[d(de,{description:"暂无卡片数据"})])),p("div",ee,[d(Se,{"current-page":qe.value,"onUpdate:currentPage":a[2]||(a[2]=e=>qe.value=e),"page-size":ze.value,"onUpdate:pageSize":a[3]||(a[3]=e=>ze.value=e),"page-sizes":[10,20,30,50],layout:"total, sizes, prev, pager, next, jumper",total:We.value,onSizeChange:a[4]||(a[4]=e=>{ze.value.value=e,Fe(1)}),onCurrentChange:Fe},null,8,["current-page","page-size","total"])])])),[[ea,se.value]])]),_:1},8,["modelValue"]),d(Te,{modelValue:_e.value,"onUpdate:modelValue":a[9]||(a[9]=e=>_e.value=e),title:"添加卡片",width:"50%"},{footer:r(()=>[p("span",ie,[d(l,{onClick:a[8]||(a[8]=e=>_e.value=!1)},{default:r(()=>a[30]||(a[30]=[m("取消")])),_:1,__:[30]}),d(l,{type:"primary",onClick:Ge,loading:ce.value},{default:r(()=>a[31]||(a[31]=[m("确认")])),_:1,__:[31]},8,["loading"])])]),default:r(()=>[Ie.value?(w(),o("div",ae,[a[29]||(a[29]=p("h3",null,"已选择卡片",-1)),d(u,{column:2,border:""},{default:r(()=>[d(i,{label:"ID"},{default:r(()=>[m(_(Ie.value.id),1)]),_:1}),d(i,{label:"名称"},{default:r(()=>[m(_(Ie.value.card_name),1)]),_:1}),d(i,{label:"稀有度"},{default:r(()=>[m(_(Ie.value.rarity),1)]),_:1}),d(i,{label:"积分价值"},{default:r(()=>[m(_(Ie.value.point_worth),1)]),_:1})]),_:1}),p("div",le,[d(V,{style:{width:"100px",height:"100px"},src:Ie.value.image_url,"preview-src-list":[Ie.value.image_url]},null,8,["src","preview-src-list"])])])):v("",!0),d(j,{model:je.value,rules:De.value,ref_key:"cardFormRef",ref:be,"label-width":"120px"},{default:r(()=>[d(W,{label:"概率",prop:"probability"},{default:r(()=>[d(Ze,{modelValue:je.value.probability,"onUpdate:modelValue":a[6]||(a[6]=e=>je.value.probability=e),min:0,max:100,precision:2,step:.1},null,8,["modelValue"])]),_:1}),d(W,{label:"状态",prop:"condition"},{default:r(()=>[d(z,{modelValue:je.value.condition,"onUpdate:modelValue":a[7]||(a[7]=e=>je.value.condition=e),placeholder:"请选择状态"},{default:r(()=>[d(q,{label:"全新",value:"new"}),d(q,{label:"良好",value:"good"}),d(q,{label:"一般",value:"fair"}),d(q,{label:"差",value:"poor"})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"]),d(Te,{modelValue:fe.value,"onUpdate:modelValue":a[12]||(a[12]=e=>fe.value=e),title:"修改最大中奖金额",width:"30%"},{footer:r(()=>[p("span",te,[d(l,{onClick:a[11]||(a[11]=e=>fe.value=!1)},{default:r(()=>a[32]||(a[32]=[m("取消")])),_:1,__:[32]}),d(l,{type:"primary",onClick:Ke,loading:ce.value},{default:r(()=>a[33]||(a[33]=[m("确认")])),_:1,__:[33]},8,["loading"])])]),default:r(()=>[d(j,{model:he.value,"label-width":"120px"},{default:r(()=>[d(W,{label:"最大中奖金额",required:""},{default:r(()=>[d(Ze,{modelValue:he.value.maxWin,"onUpdate:modelValue":a[10]||(a[10]=e=>he.value.maxWin=e),min:0,precision:0,step:100,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),d(Te,{modelValue:ye.value,"onUpdate:modelValue":a[15]||(a[15]=e=>ye.value=e),title:"修改最小中奖金额",width:"30%"},{footer:r(()=>[p("span",ue,[d(l,{onClick:a[14]||(a[14]=e=>ye.value=!1)},{default:r(()=>a[34]||(a[34]=[m("取消")])),_:1,__:[34]}),d(l,{type:"primary",onClick:Ne,loading:ce.value},{default:r(()=>a[35]||(a[35]=[m("确认")])),_:1,__:[35]},8,["loading"])])]),default:r(()=>[d(j,{model:we.value,"label-width":"120px"},{default:r(()=>[d(W,{label:"最小中奖金额",required:""},{default:r(()=>[d(Ze,{modelValue:we.value.minWin,"onUpdate:modelValue":a[13]||(a[13]=e=>we.value.minWin=e),min:0,precision:0,step:100,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),d(Te,{modelValue:ge.value,"onUpdate:modelValue":a[18]||(a[18]=e=>ge.value=e),title:"修改价格",width:"30%"},{footer:r(()=>[p("span",oe,[d(l,{onClick:a[17]||(a[17]=e=>ge.value=!1)},{default:r(()=>a[36]||(a[36]=[m("取消")])),_:1,__:[36]}),d(l,{type:"primary",onClick:Xe,loading:ce.value},{default:r(()=>a[37]||(a[37]=[m("确认")])),_:1,__:[37]},8,["loading"])])]),default:r(()=>[d(j,{model:ke.value,"label-width":"120px"},{default:r(()=>[d(W,{label:"价格",required:""},{default:r(()=>[d(Ze,{modelValue:ke.value.price,"onUpdate:modelValue":a[16]||(a[16]=e=>ke.value.price=e),min:0,precision:0,step:100,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-9c96e05b"]]);export{de as default};
