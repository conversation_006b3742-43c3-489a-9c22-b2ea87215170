import{r as e}from"./request-ZE4ZUch8.js";const a=async()=>e.get("/packs/packs_collection"),n=async()=>{const e=await a();let n=null;const c=/pokemon|pokemen/i;for(const a of e)if(c.test(a.name)){n=a.id.toString();break}return!n&&e.length>0&&(n=e[0].id.toString()),{collections:e,pokemenCollection:n}},c=async(a,n)=>e.get(`/packs/collection/${a}`,{params:n}),t=async a=>e.get(`/packs/collection/${a}/inactive`),o=async(a,n)=>e.get(`/packs/${a}`,{params:{collection_id:n}}),s=async a=>{if(a.image_file&&a.image_file.length>0&&a.image_file.length>1048576){const n=new FormData;for(const e in a)a.hasOwnProperty(e)&&void 0!==a[e]&&n.append(e,a[e].toString());return e.post("/packs/",n)}{const n=new URLSearchParams;for(const e in a)a.hasOwnProperty(e)&&void 0!==a[e]&&n.append(e,a[e].toString());return e.post("/packs/",n,{headers:{"Content-Type":"application/x-www-form-urlencoded"}})}},p=async(a,n,c)=>{const t=c?`/packs/${a}/${n}/activate`:`/packs/${a}/${n}/inactivate`;return e.patch(t,{is_active:c},{headers:{"Content-Type":"application/x-www-form-urlencoded"}})},i=async(a,n,c)=>e.patch(`/packs/${a}/${n}/max_win`,{max_win:c},{headers:{"Content-Type":"application/x-www-form-urlencoded"}}),r=async(a,n,c)=>e.patch(`/packs/${a}/${n}/min_win`,{min_win:c},{headers:{"Content-Type":"application/x-www-form-urlencoded"}}),l=async(a,n,c)=>e.patch(`/packs/${a}/${n}/price`,{price:c},{headers:{"Content-Type":"application/x-www-form-urlencoded"}}),d=async(a,n)=>e.delete(`/packs/${a}/${n}`),m=async(a,n,c)=>e.get(`/packs/${a}/${n}/cards`,{params:c}),w=async(a,n,c)=>e.post(`/packs/${a}/${n}/cards`,c),$=async(a,n)=>e.delete(`/packs/${a}/${n}/cards`),y=async(a,n,c)=>e.delete(`/packs/${a}/${n}/cards`,{data:{document_id:c}});export{w as createCard,s as createPack,y as deleteCard,$ as deleteCards,d as deletePack,t as getInactivePackList,m as getPackCards,a as getPackCollections,n as getPackCollectionsWithPokemen,o as getPackDetail,c as getPackList,p as updatePackActiveStatus,i as updatePackMaxWin,r as updatePackMinWin,l as updatePackPrice};
