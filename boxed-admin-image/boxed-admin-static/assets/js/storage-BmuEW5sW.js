import{r as a}from"./request-ZE4ZUch8.js";const e=async e=>a.get("/storage/cards",{params:e}),t=async(e,t)=>a.get(`/storage/cards/${e}`,{params:{collection_metadata_id:t}}),o=async(e,t)=>{const{collection_metadata_id:o,...s}=t,n={...s,date_got_in_stock:(new Date).toISOString()};return a.put(`/storage/cards/${e}?collection_metadata_id=${o}`,n,{headers:{"Content-Type":"application/json"}})},s=async(e,t)=>a.delete(`/storage/cards/${e}`,{data:{collection_metadata_id:t}}),n=async(e,t)=>a.get(`/storage/cards/${e}/${t}/fusions`),c=async(e,t,o)=>a.patch(`/storage/cards/${e}/quantity?collection_metadata_id=${t}`,{quantity_change:o},{headers:{"Content-Type":"application/x-www-form-urlencoded"}}),r=async()=>await a.get("/storage/collection-metadata"),i=async()=>{const e=await a.get("/storage/collection-metadata"),t=e.find(a=>a.name.toLowerCase().includes("pokemon")||a.name.toLowerCase().includes("pokemen")||a.firestoreCollection.toLowerCase().includes("pokemon")||a.firestoreCollection.toLowerCase().includes("pokemen"));return{collections:e,pokemenCollection:t?t.name:e.length>0?e[0].name:""}},l=async e=>a.post("/storage/collection-metadata",e),d=async e=>a.get(`/storage/collection-metadata/${e}`),m=async e=>a.delete(`/storage/collection-metadata/${e}`),g=async e=>{if(e.image_file){let a=e.image_file;a.startsWith("data:")&&(a=a.split(",")[1]);const t=a.length%4;t>0&&(a+="=".repeat(4-t)),a="data:image/png;base64,"+a,e={...e,image_base64:a}}const t=new FormData;for(const a in e)e.hasOwnProperty(a)&&void 0!==e[a]&&t.append(a,e[a].toString());return a.post("/storage/upload_card",t,{headers:{"Content-Type":"multipart/form-data"}})};export{e as a,t as b,g as c,c as d,s as e,n as f,i as g,r as h,l as i,d as j,m as k,o as u};
