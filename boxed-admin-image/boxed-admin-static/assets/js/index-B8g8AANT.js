import{d as e}from"./elementPlus-Bm-E4Nxd.js";import{x as a,r as l,y as t,P as s,H as d,ag as n,L as r,M as o,u as p,A as u,z as i}from"./vue-DWGM1jOu.js";import{_ as c}from"./index-Br8T7GvP.js";const m={class:"permissions-container"},_={class:"card-header"},y=c(a({__name:"index",setup(a){const c=l([{id:1,name:"系统管理",code:"system",type:"menu",path:"/system",children:[{id:2,name:"用户管理",code:"system:user",type:"menu",path:"/system/users",children:[{id:3,name:"新增用户",code:"system:user:add",type:"button",path:""}]}]}]),y=()=>{};return(a,l)=>{const f=n("el-button"),h=n("el-table-column"),b=n("el-tag"),k=n("el-icon"),v=n("el-button-group"),x=n("el-table"),C=n("el-card");return i(),t("div",m,[s(C,null,{header:d(()=>[u("div",_,[l[1]||(l[1]=u("span",null,"权限列表",-1)),s(f,{type:"primary",onClick:y},{default:d(()=>l[0]||(l[0]=[r("新增权限")])),_:1,__:[0]})])]),default:d(()=>[s(x,{data:c.value,"row-key":"id","default-expand-all":"","tree-props":{children:"children"}},{default:d(()=>[s(h,{prop:"name",label:"权限名称"}),s(h,{prop:"code",label:"权限标识"}),s(h,{prop:"type",label:"类型"},{default:d(({row:e})=>[s(b,{type:"menu"===e.type?"success":"info"},{default:d(()=>[r(o("menu"===e.type?"菜单":"按钮"),1)]),_:2},1032,["type"])]),_:1}),s(h,{prop:"path",label:"路由路径"}),s(h,{label:"操作",width:"200"},{default:d(({row:a})=>[s(v,null,{default:d(()=>[s(f,{type:"primary",link:"",onClick:e=>{}},{default:d(()=>l[2]||(l[2]=[r("编辑")])),_:2,__:[2]},1032,["onClick"]),s(f,{type:"danger",link:"",onClick:e=>{}},{default:d(()=>[s(k,null,{default:d(()=>[s(p(e))]),_:1}),l[3]||(l[3]=r(" 删除 "))]),_:2,__:[3]},1032,["onClick"])]),_:2},1024)]),_:1})]),_:1},8,["data"])]),_:1})])}}}),[["__scopeId","data-v-d301d369"]]);export{y as default};
