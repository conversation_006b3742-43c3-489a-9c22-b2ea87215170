import{g as e}from"./fusion-LqssYdbi.js";import{g as a}from"./storage-BmuEW5sW.js";import{E as l,y as t}from"./elementPlus-Bm-E4Nxd.js";import{x as d,X as r,r as o,c,h as u,y as n,P as s,A as i,H as _,ag as p,O as m,a6 as f,L as v,M as h,J as g,u as b,z as y,G as w}from"./vue-DWGM1jOu.js";import{_ as k}from"./index-Br8T7GvP.js";import"./request-ZE4ZUch8.js";const I={class:"requests-container"},V={class:"expanded-recipes"},x={key:0,class:"card-detail-content"},z={class:"pagination-container"},C=k(d({__name:"requests",setup(d){const k=r({collectionId:"",userId:"",searchQuery:""}),C=o(!1),q=o(1),j=o(10),D=o({collections:[],pagination:{total_items:0,total_pages:0,current_page:1,per_page:10},filters:{sort_by:"result_card_id",sort_order:"desc",search_query:""}}),U=o(!1),Q=o([]),P=async()=>{U.value=!0;try{D.value=await e({collection_id:k.collectionId,user_id:k.userId,page:q.value,per_page:j.value,search_query:k.searchQuery})}catch(a){l.error("获取配方列表失败")}finally{U.value=!1}},$=()=>{q.value=1,P()},E=o(!1),S=o(null),A=c(()=>{var e;return(null==(e=D.value.pagination)?void 0:e.total_items)||0}),G=c(()=>{var e;const a=[];return null==(e=D.value.collections)||e.forEach(e=>{var l;null==(l=e.packs)||l.forEach(l=>{a.push({pack_id:l.pack_id,collection_id:e.collection_id,cards_count:l.cards_count||l.cards.length,cards:l.cards})})}),a}),H=()=>{q.value=1,P()},J=()=>{k.collectionId="",k.userId="",k.searchQuery="",q.value=1,P()},L=e=>{j.value=e,P()},M=e=>{q.value=e,P()};return u(()=>{(async()=>{try{const{collections:e,pokemenCollection:l}=await a();Q.value=e,l&&(k.collectionId=l,C.value&&P())}catch(e){l.error("获取分类列表失败")}})(),C.value=!0}),(e,a)=>{const l=p("el-option"),d=p("el-select"),r=p("el-form-item"),o=p("el-input"),c=p("el-button"),u=p("el-form"),C=p("el-card"),D=p("el-table-column"),U=p("el-tag"),P=p("el-progress"),O=p("el-table"),X=p("el-descriptions-item"),B=p("el-descriptions"),F=p("el-drawer"),K=p("el-pagination");return y(),n("div",I,[s(C,{class:"search-card"},{default:_(()=>[s(u,{model:k,inline:""},{default:_(()=>[s(r,{label:"分类"},{default:_(()=>[s(d,{style:{width:"150px"},modelValue:k.collectionId,"onUpdate:modelValue":a[0]||(a[0]=e=>k.collectionId=e),placeholder:"请选择分类",clearable:"",onChange:$},{default:_(()=>[(y(!0),n(m,null,f(Q.value,e=>(y(),w(l,{key:e.collection_id,label:e.name,value:e.storagePrefix},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),s(r,{label:"用户ID"},{default:_(()=>[s(o,{modelValue:k.userId,"onUpdate:modelValue":a[1]||(a[1]=e=>k.userId=e),placeholder:"请输入用户ID",clearable:""},null,8,["modelValue"])]),_:1}),s(r,{label:"目标卡牌"},{default:_(()=>[s(o,{modelValue:k.searchQuery,"onUpdate:modelValue":a[2]||(a[2]=e=>k.searchQuery=e),placeholder:"请输入目标卡牌",clearable:""},null,8,["modelValue"])]),_:1}),s(r,null,{default:_(()=>[s(c,{type:"primary",onClick:H},{default:_(()=>a[6]||(a[6]=[v("查询")])),_:1,__:[6]}),s(c,{onClick:J},{default:_(()=>a[7]||(a[7]=[v("重置")])),_:1,__:[7]})]),_:1})]),_:1},8,["model"])]),_:1}),s(C,{class:"pack-recipe-list-card"},{default:_(()=>[s(O,{data:G.value,border:"",stripe:""},{default:_(()=>[s(D,{type:"expand"},{default:_(({row:e})=>[i("div",V,[i("h4",null,h(e.pack_id)+" - 配方列表",1),s(O,{data:e.cards,border:"",style:{width:"100%"}},{default:_(()=>[s(D,{prop:"card_reference",label:"卡牌名称","min-width":"150"}),s(D,{prop:"result_card_id",label:"卡牌ID","min-width":"120"}),s(D,{label:"所需材料","min-width":"200"},{default:_(({row:e})=>[(y(!0),n(m,null,f(e.ingredients,e=>(y(),w(U,{key:e.card_id,class:"mr-2",type:"success",size:"small"},{default:_(()=>[v(h(e.card_reference)+" x"+h(e.quantity),1)]),_:2},1024))),128))]),_:1}),s(D,{label:"合成进度","min-width":"120"},{default:_(({row:e})=>[s(P,{percentage:e.cards_needed>0?e.cards_needed/e.total_cards_needed*100:0,format:()=>`${e.cards_needed}/${e.total_cards_needed}`,"stroke-width":8},null,8,["percentage","format"])]),_:1}),s(D,{label:"操作",width:"100",fixed:"right"},{default:_(({row:l})=>[s(c,{link:"",type:"primary",onClick:a=>((e,a)=>{S.value={...e,pack_id:(null==a?void 0:a.pack_id)||"",collection_id:(null==a?void 0:a.collection_id)||""},E.value=!0})(l,e)},{default:_(()=>a[8]||(a[8]=[v("查看详情")])),_:2,__:[8]},1032,["onClick"])]),_:2},1024)]),_:2},1032,["data"])])]),_:1}),s(D,{prop:"pack_id",label:"卡包ID","min-width":"150"}),s(D,{prop:"collection_id",label:"分类","min-width":"120"}),s(D,{prop:"cards_count",label:"配方数量",width:"100"})]),_:1},8,["data"])]),_:1}),s(F,{modelValue:E.value,"onUpdate:modelValue":a[3]||(a[3]=e=>E.value=e),title:"卡片详情",direction:"rtl",size:"50%"},{default:_(()=>[S.value?(y(),n("div",x,[s(B,{title:"基本信息",column:2,border:""},{default:_(()=>[s(X,{label:"卡牌名称"},{default:_(()=>[v(h(S.value.card_reference),1)]),_:1}),s(X,{label:"卡牌ID"},{default:_(()=>[v(h(S.value.result_card_id),1)]),_:1}),s(X,{label:"卡包"},{default:_(()=>[v(h(S.value.pack_id),1)]),_:1}),s(X,{label:"分类"},{default:_(()=>[v(h(S.value.collection_id),1)]),_:1}),s(X,{label:"合成进度",span:2},{default:_(()=>[s(P,{percentage:S.value.cards_needed>0?S.value.cards_needed/S.value.total_cards_needed*100:0,format:()=>`${S.value.cards_needed}/${S.value.total_cards_needed}`,"stroke-width":12},null,8,["percentage","format"])]),_:1})]),_:1}),s(b(t),null,{default:_(()=>a[9]||(a[9]=[v("所需材料")])),_:1,__:[9]}),s(O,{data:S.value.ingredients,border:"",style:{width:"100%"}},{default:_(()=>[s(D,{prop:"card_reference",label:"材料名称","min-width":"120"}),s(D,{prop:"card_id",label:"材料ID","min-width":"120"}),s(D,{prop:"quantity",label:"数量",width:"80"})]),_:1},8,["data"])])):g("",!0)]),_:1},8,["modelValue"]),i("div",z,[s(K,{"current-page":q.value,"onUpdate:currentPage":a[4]||(a[4]=e=>q.value=e),"page-size":j.value,"onUpdate:pageSize":a[5]||(a[5]=e=>j.value=e),"page-sizes":[10,20,50,100],small:!1,disabled:!1,background:!0,layout:"total, sizes, prev, pager, next, jumper",total:A.value,onSizeChange:L,onCurrentChange:M},null,8,["current-page","page-size","total"])])])}}}),[["__scopeId","data-v-0f1f9a79"]]);export{C as default};
