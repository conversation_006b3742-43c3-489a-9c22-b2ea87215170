import{x as e,X as a,r as l,y as t,P as d,H as o,ag as u,L as r,I as n,A as s,aq as p,G as i,M as c,J as m,z as f}from"./vue-DWGM1jOu.js";import{_}from"./index-Br8T7GvP.js";import"./elementPlus-Bm-E4Nxd.js";const b={class:"orders-container"},g={class:"pagination-container"},h=_(e({__name:"orders",setup(e){const _=a({orderNo:"",userId:"",status:"",paymentMethod:"",dateRange:[]}),h=l([]),v=l(!1),y=l(1),V=l(10),w=l(0),k=e=>({pending:"待支付",success:"支付成功",failed:"支付失败",refunded:"已退款"}[e]||e),I=()=>{v.value=!0,setTimeout(()=>{v.value=!1},1e3)},C=()=>{_.orderNo="",_.userId="",_.status="",_.paymentMethod="",_.dateRange=[],I()},z=e=>{V.value=e,I()},U=e=>{y.value=e,I()};return I(),(e,a)=>{const l=u("el-input"),x=u("el-form-item"),M=u("el-option"),N=u("el-select"),j=u("el-date-picker"),R=u("el-button"),D=u("el-form"),P=u("el-card"),T=u("el-table-column"),S=u("el-tag"),q=u("el-table"),A=u("el-pagination"),F=p("loading");return f(),t("div",b,[d(P,{class:"search-card"},{default:o(()=>[d(D,{model:_,inline:""},{default:o(()=>[d(x,{label:"订单号"},{default:o(()=>[d(l,{modelValue:_.orderNo,"onUpdate:modelValue":a[0]||(a[0]=e=>_.orderNo=e),placeholder:"请输入订单号",clearable:""},null,8,["modelValue"])]),_:1}),d(x,{label:"用户ID"},{default:o(()=>[d(l,{modelValue:_.userId,"onUpdate:modelValue":a[1]||(a[1]=e=>_.userId=e),placeholder:"请输入用户ID",clearable:""},null,8,["modelValue"])]),_:1}),d(x,{label:"订单状态"},{default:o(()=>[d(N,{modelValue:_.status,"onUpdate:modelValue":a[2]||(a[2]=e=>_.status=e),placeholder:"请选择订单状态",clearable:""},{default:o(()=>[d(M,{label:"待支付",value:"pending"}),d(M,{label:"支付成功",value:"success"}),d(M,{label:"支付失败",value:"failed"}),d(M,{label:"已退款",value:"refunded"})]),_:1},8,["modelValue"])]),_:1}),d(x,{label:"支付方式"},{default:o(()=>[d(N,{modelValue:_.paymentMethod,"onUpdate:modelValue":a[3]||(a[3]=e=>_.paymentMethod=e),placeholder:"请选择支付方式",clearable:""},{default:o(()=>[d(M,{label:"支付宝",value:"alipay"}),d(M,{label:"微信",value:"wechat"}),d(M,{label:"银联",value:"unionpay"})]),_:1},8,["modelValue"])]),_:1}),d(x,{label:"创建时间"},{default:o(()=>[d(j,{modelValue:_.dateRange,"onUpdate:modelValue":a[4]||(a[4]=e=>_.dateRange=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},null,8,["modelValue"])]),_:1}),d(x,null,{default:o(()=>[d(R,{type:"primary",onClick:I},{default:o(()=>a[7]||(a[7]=[r("查询")])),_:1,__:[7]}),d(R,{onClick:C},{default:o(()=>a[8]||(a[8]=[r("重置")])),_:1,__:[8]})]),_:1})]),_:1},8,["model"])]),_:1}),d(P,{class:"table-card"},{default:o(()=>[n((f(),i(q,{data:h.value,border:"",stripe:""},{default:o(()=>[d(T,{prop:"orderNo",label:"订单号",width:"180"}),d(T,{prop:"userId",label:"用户ID",width:"120"}),d(T,{prop:"amount",label:"金额",width:"120"},{default:o(({row:e})=>[r(" ¥"+c(e.amount.toFixed(2)),1)]),_:1}),d(T,{prop:"status",label:"订单状态",width:"120"},{default:o(({row:e})=>{return[d(S,{type:(a=e.status,{pending:"warning",success:"success",failed:"danger",refunded:"info"}[a]||"info")},{default:o(()=>[r(c(k(e.status)),1)]),_:2},1032,["type"])];var a}),_:1}),d(T,{prop:"paymentMethod",label:"支付方式",width:"120"}),d(T,{prop:"createTime",label:"创建时间",width:"180"}),d(T,{prop:"payTime",label:"支付时间",width:"180"}),d(T,{label:"操作",fixed:"right",width:"150"},{default:o(({row:e})=>[d(R,{link:"",type:"primary",onClick:e=>{}},{default:o(()=>a[9]||(a[9]=[r("查看详情")])),_:2,__:[9]},1032,["onClick"]),"success"===e.status?(f(),i(R,{key:0,link:"",type:"warning",onClick:e=>{}},{default:o(()=>a[10]||(a[10]=[r("申请退款")])),_:2,__:[10]},1032,["onClick"])):m("",!0)]),_:1})]),_:1},8,["data"])),[[F,v.value]]),s("div",g,[d(A,{"current-page":y.value,"onUpdate:currentPage":a[5]||(a[5]=e=>y.value=e),"page-size":V.value,"onUpdate:pageSize":a[6]||(a[6]=e=>V.value=e),"page-sizes":[10,20,50,100],total:w.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:z,onCurrentChange:U},null,8,["current-page","page-size","total"])])]),_:1})])}}}),[["__scopeId","data-v-a1e75daa"]]);export{h as default};
