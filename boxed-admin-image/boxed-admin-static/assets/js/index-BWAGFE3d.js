import{x as e,r as a,y as l,P as s,H as r,ag as o,aB as u,z as t,a4 as n,u as d,L as i,A as p}from"./vue-DWGM1jOu.js";import{u as m,l as c}from"./elementPlus-Bm-E4Nxd.js";import{_ as f}from"./index-Br8T7GvP.js";const g={class:"login-container"},_=f(e({__name:"index",setup(e){const f=u(),_=a(!1),v=a({username:"",password:""}),w={username:[{required:!0,message:"请输入用户名",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"}]},x=async()=>{_.value=!0;try{localStorage.setItem("token","4874874"),await f.push("/storage/packs")}finally{_.value=!1}};return(e,a)=>{const u=o("el-input"),f=o("el-form-item"),y=o("el-button"),h=o("el-form"),V=o("el-card");return t(),l("div",g,[s(V,{class:"login-card"},{header:r(()=>a[2]||(a[2]=[p("h2",{class:"login-title"},"系统登录",-1)])),default:r(()=>[s(h,{ref:"loginFormRef",model:v.value,rules:w,onKeyup:n(x,["enter"])},{default:r(()=>[s(f,{prop:"username"},{default:r(()=>[s(u,{modelValue:v.value.username,"onUpdate:modelValue":a[0]||(a[0]=e=>v.value.username=e),placeholder:"用户名","prefix-icon":d(m)},null,8,["modelValue","prefix-icon"])]),_:1}),s(f,{prop:"password"},{default:r(()=>[s(u,{modelValue:v.value.password,"onUpdate:modelValue":a[1]||(a[1]=e=>v.value.password=e),type:"password",placeholder:"密码","prefix-icon":d(c),"show-password":""},null,8,["modelValue","prefix-icon"])]),_:1}),s(f,null,{default:r(()=>[s(y,{type:"primary",loading:_.value,class:"login-button",onClick:x},{default:r(()=>a[3]||(a[3]=[i(" 登录 ")])),_:1,__:[3]},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1})])}}}),[["__scopeId","data-v-fe599e8c"]]);export{_ as default};
