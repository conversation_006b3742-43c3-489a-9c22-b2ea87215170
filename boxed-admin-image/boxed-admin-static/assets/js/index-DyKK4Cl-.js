import{E as e,d as a}from"./elementPlus-Bm-E4Nxd.js";import{r as l}from"./request-ZE4ZUch8.js";import{x as r,X as t,r as d,h as s,a3 as u,Y as i,y as o,P as n,H as p,ag as _,L as c,A as m,I as g,aq as v,G as h,M as b,u as y,O as f,a6 as V,z as k}from"./vue-DWGM1jOu.js";import{_ as w}from"./index-Br8T7GvP.js";const U=async e=>l.get("/shipping/shipments",{params:e}),C=async e=>l.get(`/shipping/shipments/${e}`),Y={class:"shipping-container"},D={class:"header-actions"},q=["onClick"],x={class:"address-ellipsis"},O={class:"pagination-container"},S={class:"dialog-footer"},I={class:"dialog-footer"},M=w(r({name:"ShippingIndex",__name:"index",setup(r){const w=t({status:"",sortBy:"created_at",sortOrder:"desc",searchQuery:""}),M=d([]),B=d(!1),F=d(1),z=d(10),Q=d(0),$=d(!1),j=d(!1),E=d(),P=t({order_id:"",tracking_number:"",carrier:"",status:"processing",estimated_delivery_date:"",shipping_address:{recipient_name:"",street_address:"",city:"",state:"",postal_code:"",country:"",phone_number:""}}),L={order_id:[{required:!0,message:"请输入订单ID",trigger:"blur"}],tracking_number:[{required:!0,message:"请输入追踪号",trigger:"blur"}],carrier:[{required:!0,message:"请输入物流公司",trigger:"blur"}],status:[{required:!0,message:"请选择物流状态",trigger:"change"}],estimated_delivery_date:[{required:!0,message:"请选择预计送达日期",trigger:"change"}],"shipping_address.recipient_name":[{required:!0,message:"请输入收件人姓名",trigger:"blur"}],"shipping_address.street_address":[{required:!0,message:"请输入街道地址",trigger:"blur"}],"shipping_address.city":[{required:!0,message:"请输入城市",trigger:"blur"}],"shipping_address.state":[{required:!0,message:"请输入省/州",trigger:"blur"}],"shipping_address.postal_code":[{required:!0,message:"请输入邮政编码",trigger:"blur"}],"shipping_address.country":[{required:!0,message:"请输入国家",trigger:"blur"}],"shipping_address.phone_number":[{required:!0,message:"请输入电话号码",trigger:"blur"}]},R=d(!1),A=d(!1),H=d(),J=d(null),N=t({tracking_number:"",carrier:"",status:"",estimated_delivery_date:"",actual_delivery_date:"",shipping_address:{recipient_name:"",street_address:"",city:"",state:"",postal_code:"",country:"",phone_number:""}}),T={tracking_number:[{required:!0,message:"请输入追踪号",trigger:"blur"}],carrier:[{required:!0,message:"请输入物流公司",trigger:"blur"}],status:[{required:!0,message:"请选择物流状态",trigger:"change"}],"shipping_address.recipient_name":[{required:!0,message:"请输入收件人姓名",trigger:"blur"}]},G=d(!1),X=d(null),K=d(!1),W=d(""),Z=d(!1),ee=[{label:"顺丰速运",value:"顺丰速运"},{label:"中通快递",value:"中通快递"},{label:"圆通速递",value:"圆通速递"},{label:"申通快递",value:"申通快递"},{label:"韵达速递",value:"韵达速递"},{label:"百世快递",value:"百世快递"},{label:"EMS",value:"EMS"},{label:"京东物流",value:"京东物流"},{label:"德邦快递",value:"德邦快递"},{label:"天天快递",value:"天天快递"},{label:"FedEx",value:"FedEx"},{label:"UPS",value:"UPS"},{label:"DHL",value:"DHL"},{label:"其他",value:"其他"}],ae=async()=>{B.value=!0;try{const e={page:F.value,per_page:z.value,sort_by:w.sortBy,sort_order:w.sortOrder,search_query:w.searchQuery};w.status&&(e.status=w.status);const a=await U(e);M.value=a.data.shipments,Q.value=a.data.pagination.total_items}catch(a){e.error("获取物流列表失败")}finally{B.value=!1}},le=e=>{if(!e)return"";return{processing:"info",shipped:"warning",in_transit:"primary",delivered:"success",cancelled:"danger"}[e]||""},re=e=>{if(!e)return"";return{processing:"处理中",shipped:"已发货",in_transit:"运输中",delivered:"已送达",cancelled:"已取消"}[e]||e},te=e=>e?`${e.street_address}, ${e.city}, ${e.state}, ${e.postal_code}, ${e.country}`:"",de=()=>{w.status="",w.sortBy="created_at",w.sortOrder="desc",w.searchQuery="",F.value=1,ae()},se=()=>{F.value=1,ae()},ue=e=>{z.value=e,ae()},ie=e=>{F.value=e,ae()},oe=e=>{e.prop&&e.order&&(w.sortBy=e.prop,w.sortOrder="ascending"===e.order?"asc":"desc",ae())},ne=()=>{$.value=!0},pe=async()=>{E.value&&await E.value.validate(async a=>{if(!a)return!1;j.value=!0;try{await(async e=>l.post("/shipping/shipments",e))(P),e.success("添加物流信息成功"),$.value=!1,_e(),ae()}catch(r){e.error("添加物流信息失败")}finally{j.value=!1}})},_e=()=>{E.value&&(E.value.resetFields(),P.order_id="",P.tracking_number="",P.carrier="",P.status="processing",P.estimated_delivery_date="",P.shipping_address={recipient_name:"",street_address:"",city:"",state:"",postal_code:"",country:"",phone_number:""})},ce=async()=>{H.value&&J.value&&await H.value.validate(async a=>{if(!a)return!1;A.value=!0;try{await(async(e,a)=>l.put(`/shipping/shipments/${e}`,a))(J.value.id,N),e.success("更新物流信息成功"),R.value=!1,ae()}catch(r){e.error("更新物流信息失败")}finally{A.value=!1}})},me=async()=>{if(W.value)try{await(async e=>l.delete(`/shipping/shipments/${e}`))(W.value),e.success("删除物流信息成功"),K.value=!1,ae()}catch(a){e.error("删除物流信息失败")}},ge=async()=>{Z.value=!0;try{const a={sort_by:w.sortBy,sort_order:w.sortOrder,search_query:w.searchQuery};w.status&&(a.status=w.status);const l=await U({...a,page:1,per_page:1e3});if(!l.data.shipments||0===l.data.shipments.length)return e.warning("没有数据可导出"),void(Z.value=!1);const r=["物流ID","订单ID","追踪号","物流公司","状态","预计送达日期","实际送达日期","收件人","地址","电话号码","创建时间","更新时间"],t=l.data.shipments.map(e=>[e.id,e.order_id,e.tracking_number,e.carrier,re(e.status),e.estimated_delivery_date,e.actual_delivery_date||"",e.shipping_address.recipient_name,te(e.shipping_address),e.shipping_address.phone_number,e.created_at,e.updated_at]);let d=r.join(",")+"\n";t.forEach(e=>{const a=e.map(e=>{const a=String(e);return a.includes(",")||a.includes('"')||a.includes("\n")?'"'+a.replace(/"/g,'""')+'"':a});d+=a.join(",")+"\n"});const s=new Blob([d],{type:"text/csv;charset=utf-8;"}),u=document.createElement("a"),i=URL.createObjectURL(s);u.setAttribute("href",i),u.setAttribute("download",`物流信息_${(new Date).toISOString().split("T")[0]}.csv`),u.style.visibility="hidden",document.body.appendChild(u),u.click(),document.body.removeChild(u),e.success("导出成功")}catch(a){e.error("导出物流信息失败")}finally{Z.value=!1}};return s(()=>{ae()}),u(()=>{localStorage.setItem("shipping_state",JSON.stringify({searchForm:{status:w.status,sortBy:w.sortBy,sortOrder:w.sortOrder,searchQuery:w.searchQuery},pagination:{currentPage:F.value,pageSize:z.value}}))}),i(()=>{const e=localStorage.getItem("shipping_state");if(e){const a=JSON.parse(e);a.searchForm&&(w.status=a.searchForm.status||"",w.sortBy=a.searchForm.sortBy||"created_at",w.sortOrder=a.searchForm.sortOrder||"desc",w.searchQuery=a.searchForm.searchQuery||""),a.pagination&&(F.value=a.pagination.currentPage||1,z.value=a.pagination.pageSize||10)}ae()}),(l,r)=>{const t=_("el-option"),d=_("el-select"),s=_("el-form-item"),u=_("el-input"),i=_("el-button"),U=_("el-form"),ae=_("el-card"),ve=_("el-table-column"),he=_("el-tooltip"),be=_("el-tag"),ye=_("el-icon"),fe=_("el-table"),Ve=_("el-pagination"),ke=_("el-date-picker"),we=_("el-divider"),Ue=_("el-drawer"),Ce=_("el-descriptions-item"),Ye=_("el-descriptions"),De=_("el-dialog"),qe=v("loading");return k(),o("div",Y,[n(ae,{class:"search-card"},{default:p(()=>[n(U,{ref:"searchFormRef",inline:""},{default:p(()=>[n(s,{label:"物流状态",prop:"status"},{default:p(()=>[n(d,{modelValue:w.status,"onUpdate:modelValue":r[0]||(r[0]=e=>w.status=e),placeholder:"请选择物流状态",clearable:"",style:{width:"220px"}},{default:p(()=>[n(t,{label:"处理中",value:"processing"}),n(t,{label:"已发货",value:"shipped"}),n(t,{label:"运输中",value:"in_transit"}),n(t,{label:"已送达",value:"delivered"}),n(t,{label:"已取消",value:"cancelled"})]),_:1},8,["modelValue"])]),_:1}),n(s,{label:"排序字段",prop:"sortBy"},{default:p(()=>[n(d,{modelValue:w.sortBy,"onUpdate:modelValue":r[1]||(r[1]=e=>w.sortBy=e),placeholder:"请选择排序字段",style:{width:"150px"},clearable:""},{default:p(()=>[n(t,{label:"创建时间",value:"created_at"}),n(t,{label:"状态",value:"status"}),n(t,{label:"物流公司",value:"carrier"})]),_:1},8,["modelValue"])]),_:1}),n(s,{label:"排序方式",prop:"sortOrder"},{default:p(()=>[n(d,{modelValue:w.sortOrder,"onUpdate:modelValue":r[2]||(r[2]=e=>w.sortOrder=e),placeholder:"请选择排序方式",style:{width:"150px"},clearable:""},{default:p(()=>[n(t,{label:"升序",value:"asc"}),n(t,{label:"降序",value:"desc"})]),_:1},8,["modelValue"])]),_:1}),n(s,{label:"搜索",prop:"searchQuery"},{default:p(()=>[n(u,{modelValue:w.searchQuery,"onUpdate:modelValue":r[3]||(r[3]=e=>w.searchQuery=e),placeholder:"请输入订单号或追踪号",clearable:""},null,8,["modelValue"])]),_:1}),n(s,null,{default:p(()=>[n(i,{type:"primary",onClick:se},{default:p(()=>r[37]||(r[37]=[c("查询")])),_:1,__:[37]}),n(i,{onClick:de},{default:p(()=>r[38]||(r[38]=[c("重置")])),_:1,__:[38]})]),_:1})]),_:1},512)]),_:1}),n(ae,{class:"table-card"},{default:p(()=>[m("div",D,[n(i,{type:"primary",onClick:ne},{default:p(()=>r[39]||(r[39]=[c("添加物流信息")])),_:1,__:[39]}),n(i,{type:"success",onClick:ge,loading:Z.value},{default:p(()=>r[40]||(r[40]=[c("导出物流信息")])),_:1,__:[40]},8,["loading"])]),g((k(),h(fe,{data:M.value,border:"",stripe:"",style:{width:"100%"},"default-sort":{prop:"created_at",order:"descending"},onSortChange:oe},{default:p(()=>[n(ve,{prop:"id",label:"物流ID",width:"120",sortable:"custom"}),n(ve,{prop:"order_id",label:"订单ID",width:"120",sortable:"custom"}),n(ve,{prop:"tracking_number",label:"追踪号",width:"150",sortable:"custom"},{default:p(({row:a})=>[n(he,{effect:"dark",content:`点击复制: ${a.tracking_number}`,placement:"top"},{default:p(()=>[m("span",{class:"tracking-number",onClick:l=>{return r=a.tracking_number,void navigator.clipboard.writeText(r).then(()=>{e.success("已复制到剪贴板")}).catch(()=>{e.error("复制失败")});var r}},b(a.tracking_number),9,q)]),_:2},1032,["content"])]),_:1}),n(ve,{prop:"carrier",label:"物流公司",width:"120",sortable:"custom"}),n(ve,{prop:"status",label:"状态",width:"100",sortable:"custom"},{default:p(({row:e})=>[n(be,{type:le(e.status)},{default:p(()=>[c(b(re(e.status)),1)]),_:2},1032,["type"])]),_:1}),n(ve,{prop:"estimated_delivery_date",label:"预计送达日期",width:"120",sortable:"custom"}),n(ve,{prop:"actual_delivery_date",label:"实际送达日期",width:"120",sortable:"custom"},{default:p(({row:e})=>[c(b(e.actual_delivery_date||"-"),1)]),_:1}),n(ve,{prop:"shipping_address.recipient_name",label:"收件人",width:"100",sortable:"custom"}),n(ve,{label:"地址","min-width":"200"},{default:p(({row:e})=>[n(he,{effect:"dark",content:te(e.shipping_address),placement:"top"},{default:p(()=>[m("div",x,b(te(e.shipping_address)),1)]),_:2},1032,["content"])]),_:1}),n(ve,{prop:"created_at",label:"创建时间",width:"180",sortable:"custom"}),n(ve,{label:"操作",width:"200",fixed:"right"},{default:p(({row:l})=>[n(i,{link:"",type:"primary",onClick:a=>(async a=>{try{const e=await C(a.id);X.value=e.data,G.value=!0}catch(l){e.error("获取物流详情失败")}})(l)},{default:p(()=>r[41]||(r[41]=[c("查看")])),_:2,__:[41]},1032,["onClick"]),n(i,{link:"",type:"primary",onClick:a=>(async a=>{try{const e=await C(a.id);J.value=e.data,N.tracking_number=e.data.tracking_number,N.carrier=e.data.carrier,N.status=e.data.status,N.estimated_delivery_date=e.data.estimated_delivery_date,N.actual_delivery_date=e.data.actual_delivery_date||"",N.shipping_address={...e.data.shipping_address},R.value=!0}catch(l){e.error("获取物流详情失败")}})(l)},{default:p(()=>r[42]||(r[42]=[c("编辑")])),_:2,__:[42]},1032,["onClick"]),n(i,{link:"",type:"danger",onClick:e=>(e=>{W.value=e.id,K.value=!0})(l)},{default:p(()=>[n(ye,null,{default:p(()=>[n(y(a))]),_:1}),r[43]||(r[43]=c(" 删除 "))]),_:2,__:[43]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[qe,B.value]]),m("div",O,[n(Ve,{"current-page":F.value,"onUpdate:currentPage":r[4]||(r[4]=e=>F.value=e),"page-size":z.value,"onUpdate:pageSize":r[5]||(r[5]=e=>z.value=e),"page-sizes":[10,20,50,100],total:Q.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:ue,onCurrentChange:ie},null,8,["current-page","page-size","total"])])]),_:1}),n(Ue,{modelValue:$.value,"onUpdate:modelValue":r[18]||(r[18]=e=>$.value=e),title:"添加物流信息",size:"50%","destroy-on-close":!1},{default:p(()=>[g((k(),h(U,{ref_key:"addFormRef",ref:E,model:P,rules:L,"label-width":"120px"},{default:p(()=>[n(s,{label:"订单ID",prop:"order_id"},{default:p(()=>[n(u,{modelValue:P.order_id,"onUpdate:modelValue":r[6]||(r[6]=e=>P.order_id=e),placeholder:"请输入订单ID"},null,8,["modelValue"])]),_:1}),n(s,{label:"追踪号",prop:"tracking_number"},{default:p(()=>[n(u,{modelValue:P.tracking_number,"onUpdate:modelValue":r[7]||(r[7]=e=>P.tracking_number=e),placeholder:"请输入追踪号"},null,8,["modelValue"])]),_:1}),n(s,{label:"物流公司",prop:"carrier"},{default:p(()=>[n(d,{modelValue:P.carrier,"onUpdate:modelValue":r[8]||(r[8]=e=>P.carrier=e),placeholder:"请选择物流公司",filterable:"","allow-create":""},{default:p(()=>[(k(),o(f,null,V(ee,e=>n(t,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),n(s,{label:"状态",prop:"status"},{default:p(()=>[n(d,{modelValue:P.status,"onUpdate:modelValue":r[9]||(r[9]=e=>P.status=e),placeholder:"请选择物流状态"},{default:p(()=>[n(t,{label:"处理中",value:"processing"}),n(t,{label:"已发货",value:"shipped"}),n(t,{label:"运输中",value:"in_transit"}),n(t,{label:"已送达",value:"delivered"}),n(t,{label:"已取消",value:"cancelled"})]),_:1},8,["modelValue"])]),_:1}),n(s,{label:"预计送达日期",prop:"estimated_delivery_date"},{default:p(()=>[n(ke,{modelValue:P.estimated_delivery_date,"onUpdate:modelValue":r[10]||(r[10]=e=>P.estimated_delivery_date=e),type:"date",placeholder:"选择日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),n(we,null,{default:p(()=>r[44]||(r[44]=[c("收件人信息")])),_:1,__:[44]}),n(s,{label:"收件人姓名",prop:"shipping_address.recipient_name"},{default:p(()=>[n(u,{modelValue:P.shipping_address.recipient_name,"onUpdate:modelValue":r[11]||(r[11]=e=>P.shipping_address.recipient_name=e),placeholder:"请输入收件人姓名"},null,8,["modelValue"])]),_:1}),n(s,{label:"街道地址",prop:"shipping_address.street_address"},{default:p(()=>[n(u,{modelValue:P.shipping_address.street_address,"onUpdate:modelValue":r[12]||(r[12]=e=>P.shipping_address.street_address=e),placeholder:"请输入街道地址"},null,8,["modelValue"])]),_:1}),n(s,{label:"城市",prop:"shipping_address.city"},{default:p(()=>[n(u,{modelValue:P.shipping_address.city,"onUpdate:modelValue":r[13]||(r[13]=e=>P.shipping_address.city=e),placeholder:"请输入城市"},null,8,["modelValue"])]),_:1}),n(s,{label:"省/州",prop:"shipping_address.state"},{default:p(()=>[n(u,{modelValue:P.shipping_address.state,"onUpdate:modelValue":r[14]||(r[14]=e=>P.shipping_address.state=e),placeholder:"请输入省/州"},null,8,["modelValue"])]),_:1}),n(s,{label:"邮政编码",prop:"shipping_address.postal_code"},{default:p(()=>[n(u,{modelValue:P.shipping_address.postal_code,"onUpdate:modelValue":r[15]||(r[15]=e=>P.shipping_address.postal_code=e),placeholder:"请输入邮政编码"},null,8,["modelValue"])]),_:1}),n(s,{label:"国家",prop:"shipping_address.country"},{default:p(()=>[n(u,{modelValue:P.shipping_address.country,"onUpdate:modelValue":r[16]||(r[16]=e=>P.shipping_address.country=e),placeholder:"请输入国家"},null,8,["modelValue"])]),_:1}),n(s,{label:"电话号码",prop:"shipping_address.phone_number"},{default:p(()=>[n(u,{modelValue:P.shipping_address.phone_number,"onUpdate:modelValue":r[17]||(r[17]=e=>P.shipping_address.phone_number=e),placeholder:"请输入电话号码"},null,8,["modelValue"])]),_:1}),n(s,null,{default:p(()=>[n(i,{type:"primary",onClick:pe},{default:p(()=>r[45]||(r[45]=[c("添加")])),_:1,__:[45]}),n(i,{onClick:_e},{default:p(()=>r[46]||(r[46]=[c("重置")])),_:1,__:[46]})]),_:1})]),_:1},8,["model"])),[[qe,j.value]])]),_:1},8,["modelValue"]),n(Ue,{modelValue:R.value,"onUpdate:modelValue":r[32]||(r[32]=e=>R.value=e),title:"编辑物流信息",size:"50%","destroy-on-close":!1},{default:p(()=>[g((k(),h(U,{ref_key:"editFormRef",ref:H,model:N,rules:T,"label-width":"120px"},{default:p(()=>[n(s,{label:"追踪号",prop:"tracking_number"},{default:p(()=>[n(u,{modelValue:N.tracking_number,"onUpdate:modelValue":r[19]||(r[19]=e=>N.tracking_number=e),placeholder:"请输入追踪号"},null,8,["modelValue"])]),_:1}),n(s,{label:"物流公司",prop:"carrier"},{default:p(()=>[n(d,{modelValue:N.carrier,"onUpdate:modelValue":r[20]||(r[20]=e=>N.carrier=e),placeholder:"请选择物流公司",filterable:"","allow-create":""},{default:p(()=>[(k(),o(f,null,V(ee,e=>n(t,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),n(s,{label:"状态",prop:"status"},{default:p(()=>[n(d,{modelValue:N.status,"onUpdate:modelValue":r[21]||(r[21]=e=>N.status=e),placeholder:"请选择物流状态"},{default:p(()=>[n(t,{label:"处理中",value:"processing"}),n(t,{label:"已发货",value:"shipped"}),n(t,{label:"运输中",value:"in_transit"}),n(t,{label:"已送达",value:"delivered"}),n(t,{label:"已取消",value:"cancelled"})]),_:1},8,["modelValue"])]),_:1}),n(s,{label:"预计送达日期",prop:"estimated_delivery_date"},{default:p(()=>[n(ke,{modelValue:N.estimated_delivery_date,"onUpdate:modelValue":r[22]||(r[22]=e=>N.estimated_delivery_date=e),type:"date",placeholder:"选择日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),n(s,{label:"实际送达日期",prop:"actual_delivery_date"},{default:p(()=>[n(ke,{modelValue:N.actual_delivery_date,"onUpdate:modelValue":r[23]||(r[23]=e=>N.actual_delivery_date=e),type:"date",placeholder:"选择日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),n(we,null,{default:p(()=>r[47]||(r[47]=[c("收件人信息")])),_:1,__:[47]}),n(s,{label:"收件人姓名",prop:"shipping_address.recipient_name"},{default:p(()=>[n(u,{modelValue:N.shipping_address.recipient_name,"onUpdate:modelValue":r[24]||(r[24]=e=>N.shipping_address.recipient_name=e),placeholder:"请输入收件人姓名"},null,8,["modelValue"])]),_:1}),n(s,{label:"街道地址",prop:"shipping_address.street_address"},{default:p(()=>[n(u,{modelValue:N.shipping_address.street_address,"onUpdate:modelValue":r[25]||(r[25]=e=>N.shipping_address.street_address=e),placeholder:"请输入街道地址"},null,8,["modelValue"])]),_:1}),n(s,{label:"城市",prop:"shipping_address.city"},{default:p(()=>[n(u,{modelValue:N.shipping_address.city,"onUpdate:modelValue":r[26]||(r[26]=e=>N.shipping_address.city=e),placeholder:"请输入城市"},null,8,["modelValue"])]),_:1}),n(s,{label:"省/州",prop:"shipping_address.state"},{default:p(()=>[n(u,{modelValue:N.shipping_address.state,"onUpdate:modelValue":r[27]||(r[27]=e=>N.shipping_address.state=e),placeholder:"请输入省/州"},null,8,["modelValue"])]),_:1}),n(s,{label:"邮政编码",prop:"shipping_address.postal_code"},{default:p(()=>[n(u,{modelValue:N.shipping_address.postal_code,"onUpdate:modelValue":r[28]||(r[28]=e=>N.shipping_address.postal_code=e),placeholder:"请输入邮政编码"},null,8,["modelValue"])]),_:1}),n(s,{label:"国家",prop:"shipping_address.country"},{default:p(()=>[n(u,{modelValue:N.shipping_address.country,"onUpdate:modelValue":r[29]||(r[29]=e=>N.shipping_address.country=e),placeholder:"请输入国家"},null,8,["modelValue"])]),_:1}),n(s,{label:"电话号码",prop:"shipping_address.phone_number"},{default:p(()=>[n(u,{modelValue:N.shipping_address.phone_number,"onUpdate:modelValue":r[30]||(r[30]=e=>N.shipping_address.phone_number=e),placeholder:"请输入电话号码"},null,8,["modelValue"])]),_:1}),n(s,null,{default:p(()=>[n(i,{type:"primary",onClick:ce},{default:p(()=>r[48]||(r[48]=[c("保存")])),_:1,__:[48]}),n(i,{onClick:r[31]||(r[31]=e=>R.value=!1)},{default:p(()=>r[49]||(r[49]=[c("取消")])),_:1,__:[49]})]),_:1})]),_:1},8,["model"])),[[qe,A.value]])]),_:1},8,["modelValue"]),n(De,{modelValue:G.value,"onUpdate:modelValue":r[34]||(r[34]=e=>G.value=e),title:"物流详情",width:"600px"},{footer:p(()=>[m("span",S,[n(i,{onClick:r[33]||(r[33]=e=>G.value=!1)},{default:p(()=>r[50]||(r[50]=[c("关闭")])),_:1,__:[50]})])]),default:p(()=>[n(Ye,{column:1,border:""},{default:p(()=>[n(Ce,{label:"物流ID"},{default:p(()=>{var e;return[c(b(null==(e=X.value)?void 0:e.id),1)]}),_:1}),n(Ce,{label:"订单ID"},{default:p(()=>{var e;return[c(b(null==(e=X.value)?void 0:e.order_id),1)]}),_:1}),n(Ce,{label:"追踪号"},{default:p(()=>{var e;return[c(b(null==(e=X.value)?void 0:e.tracking_number),1)]}),_:1}),n(Ce,{label:"物流公司"},{default:p(()=>{var e;return[c(b(null==(e=X.value)?void 0:e.carrier),1)]}),_:1}),n(Ce,{label:"状态"},{default:p(()=>{var e;return[n(be,{type:le(null==(e=X.value)?void 0:e.status)},{default:p(()=>{var e;return[c(b(re(null==(e=X.value)?void 0:e.status)),1)]}),_:1},8,["type"])]}),_:1}),n(Ce,{label:"预计送达日期"},{default:p(()=>{var e;return[c(b(null==(e=X.value)?void 0:e.estimated_delivery_date),1)]}),_:1}),n(Ce,{label:"实际送达日期"},{default:p(()=>{var e;return[c(b((null==(e=X.value)?void 0:e.actual_delivery_date)||"-"),1)]}),_:1}),n(Ce,{label:"收件人"},{default:p(()=>{var e,a;return[c(b(null==(a=null==(e=X.value)?void 0:e.shipping_address)?void 0:a.recipient_name),1)]}),_:1}),n(Ce,{label:"地址"},{default:p(()=>{var e;return[c(b(te(null==(e=X.value)?void 0:e.shipping_address)),1)]}),_:1}),n(Ce,{label:"电话号码"},{default:p(()=>{var e,a;return[c(b(null==(a=null==(e=X.value)?void 0:e.shipping_address)?void 0:a.phone_number),1)]}),_:1}),n(Ce,{label:"创建时间"},{default:p(()=>{var e;return[c(b(null==(e=X.value)?void 0:e.created_at),1)]}),_:1}),n(Ce,{label:"更新时间"},{default:p(()=>{var e;return[c(b(null==(e=X.value)?void 0:e.updated_at),1)]}),_:1})]),_:1})]),_:1},8,["modelValue"]),n(De,{modelValue:K.value,"onUpdate:modelValue":r[36]||(r[36]=e=>K.value=e),title:"确认删除",width:"400px"},{footer:p(()=>[m("span",I,[n(i,{onClick:r[35]||(r[35]=e=>K.value=!1)},{default:p(()=>r[51]||(r[51]=[c("取消")])),_:1,__:[51]}),n(i,{type:"danger",onClick:me},{default:p(()=>[n(ye,null,{default:p(()=>[n(y(a))]),_:1}),r[52]||(r[52]=c(" 确认删除 "))]),_:1,__:[52]})])]),default:p(()=>[r[53]||(r[53]=m("p",null,"确定要删除该物流信息吗？此操作不可恢复。",-1))]),_:1,__:[53]},8,["modelValue"])])}}}),[["__scopeId","data-v-2fafc760"]]);export{M as default};
