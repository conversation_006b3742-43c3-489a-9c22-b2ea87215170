import{x as e,r as a,h as l,aw as u,y as t,A as i,P as o,H as n,ag as r,O as d,I as s,a6 as v,L as c,aq as p,G as _,M as m,u as f,aB as y,z as b}from"./vue-DWGM1jOu.js";import{getPackCollectionsWithPokemen as g,getInactivePackList as h,getPackList as w,updatePackPrice as V,updatePackMaxWin as k,updatePackMinWin as x,updatePackActiveStatus as C}from"./packs-CZud9J-M.js";import{E as q,r as W}from"./elementPlus-Bm-E4Nxd.js";import{_ as U}from"./index-Br8T7GvP.js";import"./request-ZE4ZUch8.js";const z={class:"packs-container"},S={class:"card-header"},j={class:"search-bar"},I={class:"operation-buttons"},P={class:"pagination"},A={class:"dialog-footer"},B={class:"dialog-footer"},D={class:"dialog-footer"},E=U(e({__name:"index",setup(e){const U=y(),E=u(),G=a(!1),H=a(!1),L=a([]),M=a([]),O=a(""),$=a("active"),F=a(!1),J=a(1),K=a(10),N=a(0),Q=a({search_query:"",sort_by:"created_at",sort_order:"desc"}),R=a(!1),T=a(!1),X=a(!1),Y=a(null),Z=a({price:0}),ee=a({maxWin:0}),ae=a({minWin:0}),le=()=>{J.value=1,te()},ue=()=>{J.value=1,te()},te=async()=>{if(!O.value)return L.value=[],void(N.value=0);G.value=!0;try{if("inactive"===$.value){const e=await h(O.value);L.value=e.packs,N.value=e.pagination.total_items}else{const e={page:J.value,per_page:K.value,search_query:Q.value.search_query,sort_by:Q.value.sort_by,sort_order:Q.value.sort_order},a=await w(O.value,e);"active"===$.value?(L.value=a.packs.filter(e=>e.is_active),N.value=L.value.length):(L.value=a.packs,N.value=a.pagination.total_items)}}catch(e){q.error("获取卡包列表失败")}finally{G.value=!1}},ie=()=>{J.value=1,te()},oe=()=>{Q.value.search_query="",$.value="all",J.value=1,te()},ne=e=>{J.value=e,te()},re=e=>{K.value=e,J.value=1,te()},de=()=>{U.push({path:"/storage/packs/create",query:{collectionId:O.value}})},se=async()=>{if(Y.value&&O.value){H.value=!0;try{await V(O.value,Y.value.id.toString(),Z.value.price),q.success("价格修改成功"),R.value=!1,te()}catch(e){q.error("修改价格失败")}finally{H.value=!1}}},ve=async()=>{if(Y.value&&O.value){H.value=!0;try{await k(O.value,Y.value.id.toString(),ee.value.maxWin),q.success("最大中奖金额修改成功"),T.value=!1,te()}catch(e){q.error("修改最大中奖金额失败")}finally{H.value=!1}}},ce=async()=>{if(Y.value&&O.value){H.value=!0;try{await x(O.value,Y.value.id.toString(),ae.value.minWin),q.success("最小中奖金额修改成功"),X.value=!1,te()}catch(e){q.error("修改最小中奖金额失败")}finally{H.value=!1}}},pe=(e,a)=>{switch(e){case"price":(e=>{Y.value=e,Z.value.price=e.price||0,R.value=!0})(a);break;case"maxWin":(e=>{Y.value=e,ee.value.maxWin=e.max_win||0,T.value=!0})(a);break;case"minWin":(e=>{Y.value=e,ae.value.minWin=e.min_win||0,X.value=!0})(a)}};return l(()=>{const e=E.query.collectionId;(async()=>{try{const{collections:e,pokemenCollection:a}=await g();M.value=e,a&&(O.value=a,F.value&&te())}catch(e){q.error("获取卡包集合失败")}})().then(()=>{e&&M.value.some(a=>a.id.toString()===e)&&(O.value=e),te(),F.value=!0})}),(e,a)=>{const l=r("el-button"),u=r("el-option"),y=r("el-select"),g=r("el-form-item"),h=r("el-input"),w=r("el-form"),V=r("el-table-column"),k=r("el-tag"),x=r("el-icon"),E=r("el-dropdown-item"),F=r("el-dropdown-menu"),_e=r("el-dropdown"),me=r("el-table"),fe=r("el-pagination"),ye=r("el-card"),be=r("el-input-number"),ge=r("el-dialog"),he=p("loading");return b(),t(d,null,[i("div",z,[o(ye,{class:"box-card"},{header:n(()=>[i("div",S,[a[15]||(a[15]=i("span",null,"卡包管理",-1)),o(l,{type:"primary",onClick:de},{default:n(()=>a[14]||(a[14]=[c("创建卡包")])),_:1,__:[14]})])]),default:n(()=>[i("div",j,[o(w,{inline:!0,model:Q.value,class:"demo-form-inline"},{default:n(()=>[o(g,{label:"卡包分类",required:""},{default:n(()=>[o(y,{modelValue:O.value,"onUpdate:modelValue":a[0]||(a[0]=e=>O.value=e),placeholder:"请选择卡包分类",onChange:le,style:{width:"180px"}},{default:n(()=>[(b(!0),t(d,null,v(M.value,e=>(b(),_(u,{key:e.id,label:e.name,value:e.id.toString()},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(g,{label:"卡片状态"},{default:n(()=>[o(y,{modelValue:$.value,"onUpdate:modelValue":a[1]||(a[1]=e=>$.value=e),placeholder:"请选择卡片状态",onChange:ue,style:{width:"120px"}},{default:n(()=>[o(u,{label:"激活",value:"active"}),o(u,{label:"未激活",value:"inactive"})]),_:1},8,["modelValue"])]),_:1}),o(g,{label:"卡包名称"},{default:n(()=>[o(h,{modelValue:Q.value.search_query,"onUpdate:modelValue":a[2]||(a[2]=e=>Q.value.search_query=e),placeholder:"请输入卡包名称",clearable:""},null,8,["modelValue"])]),_:1}),o(g,null,{default:n(()=>[o(l,{type:"primary",onClick:ie,disabled:!O.value},{default:n(()=>a[16]||(a[16]=[c("搜索")])),_:1,__:[16]},8,["disabled"]),o(l,{onClick:oe},{default:n(()=>a[17]||(a[17]=[c("重置")])),_:1,__:[17]})]),_:1})]),_:1},8,["model"])]),s((b(),_(me,{data:L.value,style:{width:"100%"}},{default:n(()=>[o(V,{prop:"id",label:"ID",width:"80"}),o(V,{prop:"name",label:"卡包名称"}),o(V,{prop:"price",label:"价格"}),o(V,{prop:"max_win",label:"最大中奖金额"}),o(V,{prop:"min_win",label:"最小中奖金额"}),o(V,{prop:"win_rate",label:"中奖率"}),o(V,{prop:"popularity",label:"热度"}),o(V,{prop:"created_at",label:"创建时间"}),o(V,{prop:"is_active",label:"状态"},{default:n(e=>[o(k,{type:e.row.is_active?"success":"danger"},{default:n(()=>[c(m(e.row.is_active?"激活":"未激活"),1)]),_:2},1032,["type"])]),_:1}),o(V,{label:"操作",width:"250"},{default:n(e=>[i("div",I,[o(l,{size:"small",onClick:a=>{return l=e.row,void U.push({path:`/storage/packs/${l.id}`,query:{collectionId:O.value}});var l}},{default:n(()=>a[18]||(a[18]=[c("查看")])),_:2,__:[18]},1032,["onClick"]),o(l,{size:"small",type:e.row.is_active?"danger":"success",onClick:a=>(async e=>{if(O.value)try{await C(O.value,e.id.toString(),!e.is_active),q.success("卡包已"+(e.is_active?"停用":"激活")),te()}catch(a){q.error("修改激活状态失败")}})(e.row)},{default:n(()=>[c(m(e.row.is_active?"停用":"激活"),1)]),_:2},1032,["type","onClick"]),o(_e,{onCommand:a=>pe(a,e.row)},{dropdown:n(()=>[o(F,null,{default:n(()=>[o(E,{command:"price"},{default:n(()=>a[20]||(a[20]=[c("修改价格")])),_:1,__:[20]}),o(E,{command:"maxWin"},{default:n(()=>a[21]||(a[21]=[c("修改最大中奖")])),_:1,__:[21]}),o(E,{command:"minWin"},{default:n(()=>a[22]||(a[22]=[c("修改最小中奖")])),_:1,__:[22]})]),_:1})]),default:n(()=>[o(l,{size:"small",type:"primary"},{default:n(()=>[a[19]||(a[19]=c(" 更多")),o(x,{class:"el-icon--right"},{default:n(()=>[o(f(W))]),_:1})]),_:1,__:[19]})]),_:2},1032,["onCommand"])])]),_:1})]),_:1},8,["data"])),[[he,G.value]]),i("div",P,[o(fe,{"current-page":J.value,"onUpdate:currentPage":a[3]||(a[3]=e=>J.value=e),"page-size":K.value,"onUpdate:pageSize":a[4]||(a[4]=e=>K.value=e),"page-sizes":[10,20,30,50],layout:"total, sizes, prev, pager, next, jumper",total:N.value,onSizeChange:re,onCurrentChange:ne},null,8,["current-page","page-size","total"])])]),_:1})]),o(ge,{modelValue:R.value,"onUpdate:modelValue":a[7]||(a[7]=e=>R.value=e),title:"修改卡包价格",width:"30%"},{footer:n(()=>[i("span",A,[o(l,{onClick:a[6]||(a[6]=e=>R.value=!1)},{default:n(()=>a[23]||(a[23]=[c("取消")])),_:1,__:[23]}),o(l,{type:"primary",onClick:se,loading:H.value},{default:n(()=>a[24]||(a[24]=[c("确认")])),_:1,__:[24]},8,["loading"])])]),default:n(()=>[o(w,{model:Z.value,"label-width":"100px"},{default:n(()=>[o(g,{label:"卡包名称"},{default:n(()=>{var e;return[i("span",null,m(null==(e=Y.value)?void 0:e.name),1)]}),_:1}),o(g,{label:"当前价格"},{default:n(()=>{var e;return[i("span",null,m(null==(e=Y.value)?void 0:e.price),1)]}),_:1}),o(g,{label:"新价格",required:""},{default:n(()=>[o(be,{modelValue:Z.value.price,"onUpdate:modelValue":a[5]||(a[5]=e=>Z.value.price=e),min:0,precision:2,step:.1},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),o(ge,{modelValue:T.value,"onUpdate:modelValue":a[10]||(a[10]=e=>T.value=e),title:"修改最大中奖金额",width:"30%"},{footer:n(()=>[i("span",B,[o(l,{onClick:a[9]||(a[9]=e=>T.value=!1)},{default:n(()=>a[25]||(a[25]=[c("取消")])),_:1,__:[25]}),o(l,{type:"primary",onClick:ve,loading:H.value},{default:n(()=>a[26]||(a[26]=[c("确认")])),_:1,__:[26]},8,["loading"])])]),default:n(()=>[o(w,{model:ee.value,"label-width":"120px"},{default:n(()=>[o(g,{label:"卡包名称"},{default:n(()=>{var e;return[i("span",null,m(null==(e=Y.value)?void 0:e.name),1)]}),_:1}),o(g,{label:"当前最大中奖"},{default:n(()=>{var e;return[i("span",null,m(null==(e=Y.value)?void 0:e.max_win),1)]}),_:1}),o(g,{label:"新最大中奖金额",required:""},{default:n(()=>[o(be,{modelValue:ee.value.maxWin,"onUpdate:modelValue":a[8]||(a[8]=e=>ee.value.maxWin=e),min:0,precision:2,step:.1},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),o(ge,{modelValue:X.value,"onUpdate:modelValue":a[13]||(a[13]=e=>X.value=e),title:"修改最小中奖金额",width:"30%"},{footer:n(()=>[i("span",D,[o(l,{onClick:a[12]||(a[12]=e=>X.value=!1)},{default:n(()=>a[27]||(a[27]=[c("取消")])),_:1,__:[27]}),o(l,{type:"primary",onClick:ce,loading:H.value},{default:n(()=>a[28]||(a[28]=[c("确认")])),_:1,__:[28]},8,["loading"])])]),default:n(()=>[o(w,{model:ae.value,"label-width":"120px"},{default:n(()=>[o(g,{label:"卡包名称"},{default:n(()=>{var e;return[i("span",null,m(null==(e=Y.value)?void 0:e.name),1)]}),_:1}),o(g,{label:"当前最小中奖"},{default:n(()=>{var e;return[i("span",null,m(null==(e=Y.value)?void 0:e.min_win),1)]}),_:1}),o(g,{label:"新最小中奖金额",required:""},{default:n(()=>[o(be,{modelValue:ae.value.minWin,"onUpdate:modelValue":a[11]||(a[11]=e=>ae.value.minWin=e),min:0,precision:2,step:.1},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])],64)}}}),[["__scopeId","data-v-a30f3e81"]]);export{E as default};
