<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Boxed Admin</title>
    <style>
      .app-loading {
        display: flex;
        width: 100%;
        height: 100%;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        background-color: #f0f2f5;
      }
      
      .app-loading .app-loading-wrap {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
      
      .app-loading .app-loading-title {
        display: flex;
        margin-top: 30px;
        font-size: 20px;
        color: rgba(0, 0, 0, 0.85);
        justify-content: center;
        align-items: center;
      }
      
      .app-loading .app-loading-logo {
        display: block;
        width: 90px;
        margin: 0 auto;
        margin-bottom: 20px;
      }
      
      .app-loading .app-loading-item {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
      }
      
      .dot {
        position: relative;
        display: inline-block;
        width: 48px;
        height: 48px;
        margin-top: 30px;
        font-size: 32px;
        transform: rotate(45deg);
        box-sizing: border-box;
        animation: antRotate 1.2s infinite linear;
      }
      
      .dot i {
        position: absolute;
        display: block;
        width: 20px;
        height: 20px;
        background-color: #0065cc;
        border-radius: 100%;
        opacity: 0.3;
        transform: scale(0.75);
        animation: antSpinMove 1s infinite linear alternate;
        transform-origin: 50% 50%;
      }
      
      .dot i:nth-child(1) {
        top: 0;
        left: 0;
      }
      
      .dot i:nth-child(2) {
        top: 0;
        right: 0;
        animation-delay: 0.4s;
      }
      
      .dot i:nth-child(3) {
        right: 0;
        bottom: 0;
        animation-delay: 0.8s;
      }
      
      .dot i:nth-child(4) {
        bottom: 0;
        left: 0;
        animation-delay: 1.2s;
      }
      
      @keyframes antRotate {
        to {
          transform: rotate(405deg);
        }
      }
      
      @keyframes antSpinMove {
        to {
          opacity: 1;
        }
      }
    </style>
    <script type="module" crossorigin src="/assets/js/index-Br8T7GvP.js"></script>
    <link rel="modulepreload" crossorigin href="/assets/js/vue-DWGM1jOu.js">
    <link rel="modulepreload" crossorigin href="/assets/js/elementPlus-Bm-E4Nxd.js">
    <link rel="stylesheet" crossorigin href="/assets/css/index-CfDUajZj.css">
  </head>
  <body>
    <div id="app">
      <div class="app-loading">
        <div class="app-loading-wrap">
          <div class="app-loading-item">
            <div class="dot">
              <i></i>
              <i></i>
              <i></i>
              <i></i>
            </div>
          </div>
          <div class="app-loading-title">Boxed Admin</div>
        </div>
      </div>
    </div>
  </body>
</html>
