#!/usr/bin/env python3
"""
Clean up signed Google Cloud Storage URLs across all packs to permanent R2 URLs.

Scope:
- Iterate packs/{collection}/{collection}/{pack_id}
  - Update pack document image_url
  - For each card in packs/{...}/{pack_id}/cards/{card_id}
    - Update image_url
    - Update used_in_fusion[].result_card_image_url
      * Prefer authoritative image from the card document at {collection}/{result_card_id}
      * Fallback to GCS→R2 conversion for any leftover signed URLs

Collections processed by default: magic, one_piece, pokemon

Usage:
  python migrate_packs_signed_urls_cleanup.py            # perform updates
  python migrate_packs_signed_urls_cleanup.py --dry-run  # print planned changes only
  python migrate_packs_signed_urls_cleanup.py --collections pokemon one_piece
"""

import argparse
import os
import re
from typing import Dict, Any, List, Optional, Tuple
from google.cloud import firestore
from google.oauth2 import service_account

# --------------------------- Firestore client setup ---------------------------

def get_firestore_client():
    if os.getenv('K_SERVICE'):
        return firestore.Client()
    creds_path = os.getenv('GOOGLE_APPLICATION_CREDENTIALS')
    if creds_path and os.path.exists(creds_path):
        credentials = service_account.Credentials.from_service_account_file(creds_path)
        return firestore.Client(credentials=credentials)
    return firestore.Client()

DB = get_firestore_client()

# --------------------------- URL helpers -------------------------------------

R2_BUCKET_MAPPING = {
    'zapull-achievement': 'achievement.zapull.fun',
    'zapull-packs': 'pack.zapull.fun',
    'zapull-cards': 'card.zapull.fun',
    'zapull-avatar': 'avatar.zapull.fun',
    'zapull-avator': 'avatar.zapull.fun',
    'pack_covers': 'pack.zapull.fun',
    'pack_covers_production': 'pack.zapull.fun',
    'pokemon_cards_pull': 'card.zapull.fun',
    'pokemon_cards_pull_production': 'card.zapull.fun',
}


def is_r2_url(url: str) -> bool:
    return bool(url) and 'zapull.fun' in url


def gcs_to_r2(url: str) -> Optional[str]:
    if not url or is_r2_url(url):
        return None

    # gs://bucket/path
    m = re.match(r'gs://([^/]+)/(.+)', url)
    if m:
        bucket, path = m.group(1), m.group(2)
        r2 = R2_BUCKET_MAPPING.get(bucket)
        if r2:
            return f'https://{r2}/{path}'

    # https://storage.googleapis.com/bucket/path[?query]
    m = re.match(r'https://storage\.googleapis\.com/([^/]+)/([^?]+)', url)
    if m:
        bucket, path = m.group(1), m.group(2)
        r2 = R2_BUCKET_MAPPING.get(bucket)
        if r2:
            return f'https://{r2}/{path}'

    return None

# --------------------------- Card lookup -------------------------------------


def get_card_image_url(collection_id: str, card_id: str) -> Optional[str]:
    try:
        doc = DB.collection(collection_id).document(card_id).get()
        if not doc.exists:
            return None
        data = doc.to_dict() or {}
        return data.get('image_url')
    except Exception:
        return None

# --------------------------- Migration logic ----------------------------------


def update_pack_images(collection_id: str, *, dry_run: bool) -> Tuple[int, int, int]:
    """
    Process all packs under packs/{collection_id}/{collection_id}/*
    Returns (packs_updated, cards_updated, fusion_image_updates)
    """
    packs_root = DB.collection('packs').document(collection_id).collection(collection_id)

    packs_updated = 0
    cards_updated = 0
    fusion_image_updates = 0

    for pack_doc in packs_root.stream():
        pack_id = pack_doc.id
        pack_data = pack_doc.to_dict() or {}
        pack_updates: Dict[str, Any] = {}

        # Pack image_url
        old_pack_url = pack_data.get('image_url')
        new_pack_url = gcs_to_r2(old_pack_url) if old_pack_url else None
        if new_pack_url and new_pack_url != old_pack_url:
            pack_updates['image_url'] = new_pack_url

        if pack_updates:
            if dry_run:
                print(f"DRY-RUN pack update packs/{collection_id}/{collection_id}/{pack_id}: {pack_updates}")
                packs_updated += 1
            else:
                pack_doc.reference.update(pack_updates)
                print(f"Updated pack: packs/{collection_id}/{collection_id}/{pack_id}")
                packs_updated += 1

        # Cards subcollection
        cards_ref = pack_doc.reference.collection('cards')
        for card_doc in cards_ref.stream():
            card_id = card_doc.id
            card_data = card_doc.to_dict() or {}
            card_updates: Dict[str, Any] = {}

            # Card image_url
            old_card_url = card_data.get('image_url')
            new_card_url = gcs_to_r2(old_card_url) if old_card_url else None
            if new_card_url and new_card_url != old_card_url:
                card_updates['image_url'] = new_card_url

            # used_in_fusion array cleanup
            used_in_fusion = card_data.get('used_in_fusion')
            if isinstance(used_in_fusion, list) and used_in_fusion:
                updated_list = []
                list_changed = False
                for entry in used_in_fusion:
                    entry = dict(entry)
                    # Determine result card collection from pack_reference
                    pack_ref = entry.get('pack_reference', '')  # "/packs/{coll}/{coll}/{pack}"
                    parts = pack_ref.split('/')
                    inferred_coll = None
                    if len(parts) >= 4 and parts[1] == 'packs':
                        inferred_coll = parts[2]
                    result_card_id = entry.get('result_card_id')

                    desired_url = None
                    if inferred_coll and result_card_id:
                        desired_url = get_card_image_url(inferred_coll, result_card_id)
                    if not desired_url:
                        desired_url = gcs_to_r2(entry.get('result_card_image_url', ''))

                    if desired_url and desired_url != entry.get('result_card_image_url'):
                        if dry_run:
                            print(
                                f"DRY-RUN used_in_fusion image fix packs/{collection_id}/{collection_id}/{pack_id}/cards/{card_id}: {entry.get('result_card_image_url')} -> {desired_url}"
                            )
                        entry['result_card_image_url'] = desired_url
                        list_changed = True
                        fusion_image_updates += 1
                    updated_list.append(entry)

                if list_changed:
                    card_updates['used_in_fusion'] = updated_list

            if card_updates:
                if dry_run:
                    print(
                        f"DRY-RUN card update packs/{collection_id}/{collection_id}/{pack_id}/cards/{card_id}: {list(card_updates.keys())}"
                    )
                    cards_updated += 1
                else:
                    card_doc.reference.update(card_updates)
                    print(
                        f"Updated card: packs/{collection_id}/{collection_id}/{pack_id}/cards/{card_id}"
                    )
                    cards_updated += 1

    return packs_updated, cards_updated, fusion_image_updates


DEFAULT_COLLECTIONS = ['magic', 'one_piece', 'pokemon']


def main():
    parser = argparse.ArgumentParser(description='Clean signed URLs in packs to R2')
    parser.add_argument('--dry-run', action='store_true', help='Print planned changes, perform no writes')
    parser.add_argument('--collections', nargs='*', default=DEFAULT_COLLECTIONS, help='Subset of collections to process')
    args = parser.parse_args()

    total_packs = total_cards = total_fusions = 0
    print(f"Starting packs cleanup (dry-run={args.dry_run}) over: {', '.join(args.collections)}")

    for coll in args.collections:
        print('\n' + '='*80)
        print(f'Processing packs for collection: {coll}')
        print('='*80)
        p, c, f = update_pack_images(coll, dry_run=args.dry_run)
        print(f"Summary {coll}: packs_updated={p}, cards_updated={c}, fusion_result_images_fixed={f}")
        total_packs += p
        total_cards += c
        total_fusions += f

    print('\n' + '='*80)
    print('Done')
    print('='*80)
    print(f"Totals: packs_updated={total_packs}, cards_updated={total_cards}, fusion_result_images_fixed={total_fusions}")


if __name__ == '__main__':
    main()

