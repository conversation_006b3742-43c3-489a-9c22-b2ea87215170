# 测试环境构建配置
VITE_APP_TITLE=Boxed Admin Test
VITE_API_URL=https://backend-351785787544.us-central1.run.app/gacha/api/v1
VITE_BASE_URL=/

# Firebase Configuration - Test
VITE_FIREBASE_API_KEY=AIzaSyBfbEQUIGs-0rGMMw2GLFkcq6EvlG4ID40
VITE_FIREBASE_AUTH_DOMAIN=seventh-program-433718-h8.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=seventh-program-433718-h8
VITE_FIREBASE_STORAGE_BUCKET=seventh-program-433718-h8.firebasestorage.app
VITE_FIREBASE_MESSAGING_SENDER_ID=351785787544
VITE_FIREBASE_APP_ID=1:351785787544:web:eeb0ca41aa9ffa0354f0ed
VITE_FIREBASE_MEASUREMENT_ID=G-X53524FJ7B

# Stripe Configuration
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_51RRaUk4STmMQIYMZsYur9duoaVJenyC3SYWSyc5lqQYXCUjYY0SxFAvOEqFsjUPVaTiRVPrSuYnVcrElRDaZtNkM00sRDGfQKb
STRIPE_SECRET_KEY=sk_test_51RRaUk4STmMQIYMZsYur9duoaVJenyC3SYWSyc5lqQYXCUjYY0SxFAvOEqFsjUPVaTiRVPrSuYnVcrElRDaZtNkM00sRDGfQKb

# HTTPS Configuration
VITE_USE_HTTPS=true