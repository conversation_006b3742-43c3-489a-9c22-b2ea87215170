# Auto detect text files and perform LF normalization
* text=auto

# Explicitly declare text files you want to always be normalized and converted
# to native line endings on checkout.
*.html text
*.css text
*.js text
*.json text
*.py text
*.yaml text
*.yml text
*.md text
*.txt text
*.sh text eol=lf
*.dockerfile text

# Declare files that will always have CRLF line endings on checkout.
*.bat text eol=crlf

# Denote all files that are truly binary and should not be modified.
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.ico binary
*.mp4 binary
*.mov binary
*.pdf binary
*.zip binary
*.tar binary
*.gz binary
