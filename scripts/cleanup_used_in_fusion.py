#!/usr/bin/env python3
"""
Cleanup script for correcting used_in_fusion entries on card documents.

Problem addressed:
- Some card documents under /packs/{brand}/{brand}/{packId}/cards/{cardId}
  contain used_in_fusion entries where pack_reference points to a different pack
  (or has trailing whitespace). This script normalizes those entries so that
  only entries whose pack_reference matches the card's own pack path remain.

Behavior:
- Iterates brands: one_piece, pokemon, magic (configurable via --brand).
- For each pack and each card, reads used_in_fusion (array of maps).
- Normalizes pack_reference by stripping whitespace.
- Computes the canonical pack path: /packs/{brand}/{brand}/{packId}.
- Filters the array to only entries matching the canonical path.
- Deduplicates entries by fusion_id while preserving first occurrence.
- Writes back the cleaned array (or [] if none remain) when a change is needed.
- Supports --dry-run to preview changes without writing.

Usage:
  python scripts/cleanup_used_in_fusion.py --dry-run
  python scripts/cleanup_used_in_fusion.py --brand one_piece
  python scripts/cleanup_used_in_fusion.py --brand all --limit 1000

Prerequisites:
- Google Cloud credentials available to this environment (e.g., via
  GOOGLE_APPLICATION_CREDENTIALS or application-default credentials).
- Package: google-cloud-firestore
    pip install google-cloud-firestore

Notes:
- The script is idempotent and safe to run multiple times.
- It uses batched writes (commit every N updates) to be efficient.
"""

from __future__ import annotations

import argparse
import sys
from typing import Any, Dict, List, Optional, Tuple

try:
    from google.cloud import firestore  # type: ignore
    from google.cloud.firestore_v1 import DELETE_FIELD  # type: ignore
except Exception as e:  # pragma: no cover
    print(
        "ERROR: google-cloud-firestore is required. Install with: pip install google-cloud-firestore",
        file=sys.stderr,
    )
    raise


BRANDS = ["one_piece", "pokemon", "magic"]
BATCH_SIZE = 400  # keep a safe margin below the 500 writes/batch limit


def normalize_pack_reference(value: Optional[str]) -> Optional[str]:
    if value is None:
        return None
    if not isinstance(value, str):
        return None
    # Strip whitespace; do not alter internal characters
    norm = value.strip()
    # Normalize any duplicate slashes (defensive), but preserve leading slash
    while "//" in norm:
        norm = norm.replace("//", "/")
    return norm


def canonical_pack_path(brand: str, pack_id: str) -> str:
    # Matches structure: /packs/{brand}/{brand}/{packId}
    return f"/packs/{brand}/{brand}/{pack_id}"


def clean_used_in_fusion(
    arr: Any, canonical_path: str
) -> Tuple[List[Dict[str, Any]], int, int]:
    """
    Returns (cleaned_list, removed_count, deduped_count)
    - Keep entries whose normalized pack_reference == canonical_path
    - Remove others
    - Deduplicate by fusion_id while preserving order
    """
    if not isinstance(arr, list):
        return ([], 0, 0)

    # First pass: normalize and filter
    filtered: List[Dict[str, Any]] = []
    removed = 0
    for item in arr:
        if not isinstance(item, dict):
            removed += 1
            continue
        pr = normalize_pack_reference(item.get("pack_reference"))
        if pr == canonical_path:
            # Optionally write back normalized pack_reference (trimmed)
            fixed = dict(item)
            fixed["pack_reference"] = canonical_path
            filtered.append(fixed)
        else:
            removed += 1

    # Second pass: dedupe by fusion_id
    seen = set()
    deduped: List[Dict[str, Any]] = []
    deduped_count = 0
    for item in filtered:
        fid = item.get("fusion_id")
        key = (fid,)  # future-proof if we add more fields
        if key in seen:
            deduped_count += 1
            continue
        seen.add(key)
        deduped.append(item)

    return deduped, removed, deduped_count


def process_brand(db: firestore.Client, brand: str, dry_run: bool, limit: Optional[int]) -> Dict[str, int]:
    parent = db.collection("packs").document(brand)
    packs_iter = parent.collection(brand).list_documents(page_size=1000)

    stats = {
        "packs_seen": 0,
        "cards_seen": 0,
        "cards_updated": 0,
        "entries_removed": 0,
        "entries_deduped": 0,
    }

    batch = db.batch()
    batch_count = 0

    for pack_ref in packs_iter:
        stats["packs_seen"] += 1
        pack_id = pack_ref.id
        cpath = canonical_pack_path(brand, pack_id)

        card_docs = pack_ref.collection("cards").list_documents(page_size=2000)
        for card_ref in card_docs:
            stats["cards_seen"] += 1
            try:
                snap = card_ref.get()
            except Exception as e:
                print(f"WARN: failed to read {card_ref.path}: {e}")
                continue

            data = snap.to_dict() or {}
            used = data.get("used_in_fusion")

            cleaned, removed, deduped = clean_used_in_fusion(used, cpath)

            if removed or deduped:
                stats["entries_removed"] += removed
                stats["entries_deduped"] += deduped

                if not dry_run:
                    batch.update(card_ref, {"used_in_fusion": cleaned})
                    batch_count += 1
                    stats["cards_updated"] += 1

                    if batch_count >= BATCH_SIZE:
                        batch.commit()
                        batch = db.batch()
                        batch_count = 0
                else:
                    # In dry-run, still count as would-update
                    stats["cards_updated"] += 1

            if limit is not None and stats["cards_seen"] >= limit:
                break

        if limit is not None and stats["cards_seen"] >= limit:
            break

    if not dry_run and batch_count > 0:
        batch.commit()

    return stats


def main() -> None:
    parser = argparse.ArgumentParser(description="Cleanup used_in_fusion pack_reference mismatches.")
    parser.add_argument(
        "--brand",
        choices=BRANDS + ["all"],
        default="all",
        help="Which brand to process (default: all)",
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Do not write any changes; just report what would change.",
    )
    parser.add_argument(
        "--limit",
        type=int,
        default=None,
        help="Optional limit on number of cards to process (for testing).",
    )

    args = parser.parse_args()

    db = firestore.Client()

    brands = BRANDS if args.brand == "all" else [args.brand]

    total = {
        "packs_seen": 0,
        "cards_seen": 0,
        "cards_updated": 0,
        "entries_removed": 0,
        "entries_deduped": 0,
    }

    for b in brands:
        print(f"Processing brand: {b} (dry_run={args.dry_run})")
        stats = process_brand(db, b, args.dry_run, args.limit)
        print(
            f"Brand {b}: packs_seen={stats['packs_seen']}, cards_seen={stats['cards_seen']}, "
            f"cards_updated={stats['cards_updated']}, entries_removed={stats['entries_removed']}, "
            f"entries_deduped={stats['entries_deduped']}"
        )
        for k in total:
            total[k] += stats[k]

    print("--- Summary ---")
    print(
        f"Total: packs_seen={total['packs_seen']}, cards_seen={total['cards_seen']}, "
        f"cards_updated={total['cards_updated']}, entries_removed={total['entries_removed']}, "
        f"entries_deduped={total['entries_deduped']}"
    )


if __name__ == "__main__":
    main()
