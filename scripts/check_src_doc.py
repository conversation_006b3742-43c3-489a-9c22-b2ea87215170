#!/usr/bin/env python3
from google.cloud import firestore

SRC_PROJECT = "zapull-production"
DOC_PATH = "official_listing/pokemon/cards/193f4682-c30d-4e1d-adad-fa9478aa34cb"

if __name__ == "__main__":
    client = firestore.Client(project=SRC_PROJECT)
    snap = client.document(DOC_PATH).get()
    print({
        "project": SRC_PROJECT,
        "path": DOC_PATH,
        "exists": snap.exists,
        "data_keys": list((snap.to_dict() or {}).keys())[:20]
    })

