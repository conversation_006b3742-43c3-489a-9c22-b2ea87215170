#!/usr/bin/env python3
import argparse
import json
import os
import random
import signal
import sys
import time
from typing import Any, Dict, Optional, Tuple

from google.cloud import firestore
from google.api_core.exceptions import Aborted, DeadlineExceeded, ServiceUnavailable, TooManyRequests, GoogleAPICallError

STATE_DEFAULT = ".copy_state/official_listing_copy_state.json"
ROOT_COLLECTION = "official_listing"
SUBCOLLECTION = "cards"


def ensure_dir(path: str) -> None:
    d = os.path.dirname(path)
    if d and not os.path.exists(d):
        os.makedirs(d, exist_ok=True)


def write_json(path: str, data: Dict[str, Any]) -> None:
    ensure_dir(path)
    tmp = path + ".tmp"
    with open(tmp, "w", encoding="utf-8") as f:
        json.dump(data, f, indent=2, sort_keys=True, default=str)
        f.flush()
        os.fsync(f.fileno())
    os.replace(tmp, path)


def load_json(path: str) -> Optional[Dict[str, Any]]:
    if not path or not os.path.exists(path):
        return None
    with open(path, "r", encoding="utf-8") as f:
        return json.load(f)


def commit_with_retry(batch: firestore.WriteBatch, max_retries: int = 7) -> None:
    for attempt in range(max_retries):
        is_last = (attempt + 1) == max_retries
        try:
            batch.commit()
            return
        except (Aborted, DeadlineExceeded, ServiceUnavailable, TooManyRequests, GoogleAPICallError):
            if is_last:
                raise
            sleep_base = 1.0
            sleep_time = sleep_base * (2 ** attempt)
            jitter = 0.2 * random.random()
            time.sleep(min(sleep_time * (0.9 + jitter), 30.0))


def copy_official_listing(
    src_project: str,
    dst_project: str,
    dry_run: bool,
    batch_size: int,
    state_path: str,
    log_every: int,
    yes: bool,
    parents_csv: Optional[str] = None,
) -> Dict[str, Any]:
    if not (1 <= batch_size <= 500):
        raise ValueError("batch-size must be between 1 and 500")

    print("[debug] Initializing Firestore clients...")
    src = firestore.Client(project=src_project)
    dst = firestore.Client(project=dst_project)
    print(f"[debug] Source client project: {src.project}")
    print(f"[debug] Destination client project: {dst.project}")

    if not yes:
        print("About to " + ("simulate" if dry_run else "copy") + " Firestore official listings")
        print(f"Source project: {src.project}")
        print(f"Destination project: {dst.project}")
        print(f"Root collection: {ROOT_COLLECTION}")
        confirm = input("Proceed y or n: ").strip().lower()
        if confirm not in ("y", "yes"):
            print("Aborted by user")
            sys.exit(1)

    state = load_json(state_path) or {}
    resume_parent_id: Optional[str] = state.get("resume_parent_id")
    resume_card_id: Optional[str] = state.get("resume_card_id")
    resumed = bool(state)
    if resumed:
        print(f"[debug] Resume state found at {state_path}: resume_parent_id={resume_parent_id}, resume_card_id={resume_card_id}")
    else:
        print(f"[debug] No resume state at {state_path}")

    interrupted = {"value": False}

    def on_sigint(sig, frame):
        print("Interrupted, will save state and exit after current batch...")
        interrupted["value"] = True

    signal.signal(signal.SIGINT, on_sigint)

    # Determine parent list
    explicit_parents = []
    if parents_csv:
        explicit_parents = [p.strip() for p in parents_csv.split(",") if p.strip()]
        print(f"[debug] Using explicit parents (count={len(explicit_parents)}): {explicit_parents}")
    else:
        print("[debug] No explicit parents provided; will attempt to list parent IDs from source.")

    parent_ids = []
    if explicit_parents:
        parent_ids = explicit_parents
    else:
        # First attempt: list parent documents directly under ROOT_COLLECTION
        print(f"[debug] Listing parents from source collection '{ROOT_COLLECTION}'...")
        try:
            for snap in src.collection(ROOT_COLLECTION).stream():
                parent_ids.append(snap.id)
        except Exception as e:
            print(f"[error] Failed to list parent IDs: {e.__class__.__name__}: {e}")

        if len(parent_ids) == 0:
            print("[warn] No parent documents found under root. Falling back to discovering parents via collection group query on subcollection 'cards'.")
            try:
                # Use collection group query to find cards and derive parent IDs, even if parent docs are missing
                derived = set()
                for doc in src.collection_group(SUBCOLLECTION).stream():
                    parent_ref = doc.reference.parent.parent  # official_listing/{parent_id}
                    if parent_ref is not None and parent_ref.parent is not None and parent_ref.parent.id == ROOT_COLLECTION:
                        derived.add(parent_ref.id)
                parent_ids = sorted(list(derived))
                print(f"[debug] Derived {len(parent_ids)} parent IDs from collection group query.")
            except Exception as e:
                print(f"[error] Failed during collection group discovery: {e.__class__.__name__}: {e}")

    if len(parent_ids) == 0:
        print("[warn] Could not determine any parent IDs. Aborting early.")
        return {
            "processed_parents": 0,
            "processed_cards_total": 0,
            "written_cards_total": 0,
            "resumed": resumed,
            "state_file": state_path,
            "duration_sec": 0,
        }

    processed_parents = 0
    processed_cards_total = 0
    written_cards_total = 0
    started_at = time.time()

    def save_state(parent_id: Optional[str], card_id: Optional[str]):
        payload = {
            "src_project": src.project,
            "dst_project": dst.project,
            "root_collection": ROOT_COLLECTION,
            "subcollection": SUBCOLLECTION,
            "resume_parent_id": parent_id,
            "resume_card_id": card_id,
            "processed_parents": processed_parents,
            "processed_cards_total": processed_cards_total,
            "written_cards_total": written_cards_total,
            "dry_run": dry_run,
            "updated_at": time.time(),
        }
        write_json(state_path, payload)

    batch = dst.batch()
    in_batch = 0

    parents_seen_preview = 0

    for parent_id in parent_ids:

        if resume_parent_id and parent_id < resume_parent_id:
            continue

        processed_parents += 1
        if parents_seen_preview < 3:
            print(f"[debug] Parent[{processed_parents}] id: {parent_id}")
            parents_seen_preview += 1

        # Copy/ensure the parent document exists with same data
        # Parent doc might not exist in source; still create/update a stub in destination.
        parent_dst_ref = dst.collection(ROOT_COLLECTION).document(parent_id)
        if not dry_run:
            # Try to copy data if parent exists; else write an empty dict to ensure parent exists
            try:
                # Attempt fetching parent data (non-fatal if not found)
                src_parent_snap = src.collection(ROOT_COLLECTION).document(parent_id).get()
                parent_data = src_parent_snap.to_dict() or {}
            except Exception as e:
                print(f"[warn] Failed to read parent data for {parent_id}: {e.__class__.__name__}: {e}; writing empty parent doc")
                parent_data = {}
            batch.set(parent_dst_ref, parent_data)
            in_batch += 1

        # Now copy the cards subcollection
        try:
            cards_iter = src.collection(ROOT_COLLECTION).document(parent_id).collection(SUBCOLLECTION).stream()
        except Exception as e:
            print(f"[error] Failed to stream cards for parent {parent_id}: {e.__class__.__name__}: {e}")
            continue

        cards_in_this_parent = 0
        try:
            for card_doc in cards_iter:
                card_id = card_doc.id
                if resume_parent_id and parent_id == resume_parent_id and resume_card_id and card_id <= resume_card_id:
                    # resume within this parent
                    continue

                processed_cards_total += 1
                cards_in_this_parent += 1

                if dry_run:
                    # No writes, just count
                    pass
                else:
                    dest_ref = parent_dst_ref.collection(SUBCOLLECTION).document(card_id)
                    data = card_doc.to_dict()
                    batch.set(dest_ref, data)
                    in_batch += 1
                    if in_batch >= batch_size:
                        commit_with_retry(batch)
                        written_cards_total += in_batch
                        batch = dst.batch()
                        in_batch = 0
                        save_state(parent_id, card_id)

                if processed_cards_total % log_every == 0:
                    print(f"Processed {processed_cards_total} card docs (parent {parent_id})")

                if interrupted["value"]:
                    save_state(parent_id, card_id)
                    if not dry_run and in_batch > 0:
                        commit_with_retry(batch)
                        written_cards_total += in_batch
                    print("Interrupted. State saved.")
                    return {
                        "processed_parents": processed_parents,
                        "processed_cards_total": processed_cards_total,
                        "written_cards_total": written_cards_total,
                        "resumed": resumed,
                        "state_file": state_path,
                        "duration_sec": int(time.time() - started_at),
                    }
        except Exception as e:
            print(f"[error] Error while iterating cards for parent {parent_id}: {e.__class__.__name__}: {e}")
            continue

        if cards_in_this_parent == 0:
            print(f"[debug] No cards found under {ROOT_COLLECTION}/{parent_id}/{SUBCOLLECTION}")

        # Finished this parent; reset resume_card_id for subsequent parents
        resume_card_id = None

    if processed_parents == 0:
        print("[warn] No parents were processed. Possible reasons: no access to list parents, no data in source, or an error occurred earlier.")

    # Flush remaining writes
    if not dry_run and in_batch > 0:
        commit_with_retry(batch)
        written_cards_total += in_batch
        in_batch = 0

    # Clear resume state on success
    save_state(None, None)

    return {
        "processed_parents": processed_parents,
        "processed_cards_total": processed_cards_total,
        "written_cards_total": written_cards_total,
        "resumed": resumed,
        "state_file": state_path,
        "duration_sec": int(time.time() - started_at),
    }


def main():
    parser = argparse.ArgumentParser(description="Copy Firestore official_listing/{collection_id}/cards/* from one project to another")
    parser.add_argument("--source-project", required=True, help="Source GCP project id (e.g., zapull-production)")
    parser.add_argument("--dest-project", required=True, help="Destination GCP project id (e.g., seventh-program-433718-h8)")
    parser.add_argument("--dry-run", action="store_true", help="Simulate only; do not write")
    parser.add_argument("--batch-size", type=int, default=400, help="Writes per batch (max 500)")
    parser.add_argument("--state", default=STATE_DEFAULT, help="Path to resume state JSON file")
    parser.add_argument("--log-every", type=int, default=200, help="Log progress every N card docs processed")
    parser.add_argument("--yes", action="store_true", help="Skip interactive confirmation")
    parser.add_argument("--parents", default=None, help="Comma-separated parent collection_ids to copy (e.g., 'pokemon,one_piece'). If omitted, will try to list parents.")

    args = parser.parse_args()

    summary = copy_official_listing(
        src_project=args.source_project,
        dst_project=args.dest_project,
        dry_run=args.dry_run,
        batch_size=args.batch_size,
        state_path=args.state,
        log_every=args.log_every,
        yes=args.yes,
        parents_csv=args.parents,
    )

    print(json.dumps(summary, indent=2))


if __name__ == "__main__":
    main()

