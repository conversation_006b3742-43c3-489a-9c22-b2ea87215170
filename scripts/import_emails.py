#!/usr/bin/env python3
"""
Import emails from scripts/email.csv into Firestore collection "emails".

- Uses Application Default Credentials (ADC). Ensure GOOGLE_APPLICATION_CREDENTIALS is set
  or you are logged in with gcloud locally.
- CSV expected headers: INS ID,EMAIL (case-insensitive; extra whitespace tolerated)
- Upserts each row with document ID = INS ID to avoid duplicates.

Usage:
  python scripts/import_emails.py [path_to_csv]

Example:
  python scripts/import_emails.py  # defaults to scripts/email.csv
"""

import csv
import os
import re
import sys
from typing import Tuple

from google.cloud import firestore

DEFAULT_CSV_PATH = os.path.join("email.csv")
COLLECTION_NAME = "emails"

EMAIL_REGEX = re.compile(r"^[^@\s]+@[^@\s]+\.[^@\s]+$")


def normalize_header(name: str) -> str:
    return (name or "").strip().lower().replace(" ", "_")


def parse_row(row: dict) -> Tuple[str, str]:
    # Normalize headers to handle variants like "INS ID" vs "ins_id"
    normalized = {normalize_header(k): (v or "").strip() for k, v in row.items()}
    ins_id = normalized.get("ins_id") or normalized.get("insid") or normalized.get("ins_id,")
    email = normalized.get("email") or normalized.get("emails")
    return ins_id, email


def is_valid_email(email: str) -> bool:
    if not email:
        return False
    return EMAIL_REGEX.match(email) is not None


def main():
    csv_path = sys.argv[1] if len(sys.argv) > 1 else DEFAULT_CSV_PATH
    if not os.path.exists(csv_path):
        print(f"CSV file not found: {csv_path}")
        sys.exit(1)

    # Initialize Firestore client (ADC)
    try:
        db = firestore.Client()
    except Exception as e:
        print(f"Failed to initialize Firestore client: {e}")
        sys.exit(1)

    total = 0
    written = 0
    skipped = 0

    batch = db.batch()
    batch_ops = 0
    BATCH_SIZE = 400  # stay below Firestore's 500 write-per-batch limit

    with open(csv_path, newline="", encoding="utf-8") as f:
        reader = csv.DictReader(f)
        if not reader.fieldnames:
            print("CSV has no header row.")
            sys.exit(1)

        for row in reader:
            total += 1
            ins_id, email = parse_row(row)

            # Some CSVs may quote values; strip surrounding quotes
            ins_id = (ins_id or "").strip().strip('"')
            email = (email or "").strip().strip('"')

            if not is_valid_email(email):
                skipped += 1
                print(f"[skip] Invalid email '{email}' on row {total}")
                continue

            # Choose document reference: use INS ID if present, otherwise auto-ID
            if ins_id:
                doc_ref = db.collection(COLLECTION_NAME).document(ins_id)
            else:
                doc_ref = db.collection(COLLECTION_NAME).document()  # auto-generated ID

            data = {
                "email": email,
                "updated_at": firestore.SERVER_TIMESTAMP,
            }
            if ins_id:
                data["ins_id"] = ins_id

            # Upsert document
            batch.set(doc_ref, data, merge=True)
            batch_ops += 1
            written += 1

            if batch_ops >= BATCH_SIZE:
                batch.commit()
                batch = db.batch()
                batch_ops = 0

    if batch_ops:
        batch.commit()

    print(f"Done. total_rows={total} written={written} skipped={skipped} -> collection='{COLLECTION_NAME}'")


if __name__ == "__main__":
    main()

