#!/usr/bin/env bash
set -euo pipefail

# Resolve paths relative to this script's directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

SRC_PROJECT="zapull-production"
DST_PROJECT="seventh-program-433718-h8"
STATE_FILE="$PROJECT_ROOT/.copy_state/official_listing_copy_state.json"
BATCH_SIZE=400

MODE="${1:-dry-run}"

if [[ "$MODE" == "dry-run" ]]; then
  echo "Running dry-run (no writes) for copying official_listing/*/cards/* from $SRC_PROJECT to $DST_PROJECT"
  python3 "$SCRIPT_DIR/firestore_copy_official_listing.py" \
    --source-project "$SRC_PROJECT" \
    --dest-project "$DST_PROJECT" \
    --batch-size "$BATCH_SIZE" \
    --dry-run \
    --state "$STATE_FILE" \
    --log-every 200 \
    --yes
elif [[ "$MODE" == "run" ]]; then
  echo "Running actual copy for official_listing/*/cards/* from $SRC_PROJECT to $DST_PROJECT (overwrite)"
  python3 "$SCRIPT_DIR/firestore_copy_official_listing.py" \
    --source-project "$SRC_PROJECT" \
    --dest-project "$DST_PROJECT" \
    --batch-size "$BATCH_SIZE" \
    --state "$STATE_FILE" \
    --log-every 200 \
    --yes
else
  echo "Usage: $0 [dry-run|run]"
  exit 1
fi

