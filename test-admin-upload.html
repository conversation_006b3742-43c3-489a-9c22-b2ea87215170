<!DOCTYPE html>
<html>
<head>
    <title>Test Admin Upload</title>
</head>
<body>
    <h1>Test Admin Panel Upload</h1>
    <form id="testForm">
        <div>
            <label>Card Name:</label>
            <input type="text" id="card_name" value="Test Card" required>
        </div>
        <div>
            <label>Collection:</label>
            <input type="text" id="collection_metadata_id" value="test_collection" required>
        </div>
        <div>
            <label>Rarity:</label>
            <input type="number" id="rarity" value="1" required>
        </div>
        <div>
            <label>Point Worth:</label>
            <input type="number" id="point_worth" value="100" required>
        </div>
        <div>
            <label>File:</label>
            <input type="file" id="image_file" accept="image/*">
        </div>
        <div>
            <button type="button" onclick="uploadCard()">Upload Card</button>
        </div>
    </form>

    <div id="result"></div>

    <script>
        async function uploadCard() {
            const formData = new FormData();
            const fileInput = document.getElementById('image_file');
            const file = fileInput.files[0];

            if (!file) {
                alert('Please select a file');
                return;
            }

            // Check file size (2.5MB limit like admin panel)
            const maxSize = 2.5 * 1024 * 1024;
            if (file.size > maxSize) {
                alert(`File too large: ${(file.size / (1024*1024)).toFixed(2)}MB. Max: 2.5MB`);
                return;
            }

            // Convert to base64 like admin panel does
            const reader = new FileReader();
            reader.onload = async function(e) {
                const base64Result = e.target.result;
                
                // Add form fields
                formData.append('card_name', document.getElementById('card_name').value);
                formData.append('collection_metadata_id', document.getElementById('collection_metadata_id').value);
                formData.append('rarity', document.getElementById('rarity').value);
                formData.append('point_worth', document.getElementById('point_worth').value);
                formData.append('image_base64', base64Result);
                formData.append('quantity', '0');
                formData.append('condition', 'mint');

                try {
                    document.getElementById('result').innerHTML = 'Uploading...';
                    
                    const response = await fetch('https://backend-769075815684.us-central1.run.app/gacha/api/v1/storage/upload_card', {
                        method: 'POST',
                        body: formData
                    });

                    const result = await response.text();
                    
                    if (response.ok) {
                        document.getElementById('result').innerHTML = `<div style="color: green;">Success: ${result}</div>`;
                    } else {
                        document.getElementById('result').innerHTML = `<div style="color: red;">Error ${response.status}: ${result}</div>`;
                    }
                } catch (error) {
                    document.getElementById('result').innerHTML = `<div style="color: red;">Network Error: ${error.message}</div>`;
                }
            };

            reader.readAsDataURL(file);
        }
    </script>
</body>
</html>
