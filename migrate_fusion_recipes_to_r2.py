#!/usr/bin/env python3
"""
Migrate fusion_recipes image URLs from GCS signed URLs to Cloudflare R2 URLs.

Structure to traverse:
- fusion_recipes/{collection}/{collection}/{pack_id}/cards/{result_card_id}

For each recipe document:
- Update result_card_image_url to the image_url from the canonical card document
  at {collection}/{result_card_id}, if available.
- For each ingredient, update ingredient.image_url to the image_url from the
  canonical card document at {ingredient.card_collection_id}/{ingredient.card_id},
  if available.

Collections covered: magic, one_piece, pokemon

This script prefers authoritative R2 URLs stored on the card documents, and only
falls back to converting signed GCS URLs -> R2 when the card document is missing.

Usage:
  python migrate_fusion_recipes_to_r2.py            # perform updates
  python migrate_fusion_recipes_to_r2.py --dry-run  # only print what would change
"""

import re
import os
import argparse
from typing import Optional, Dict, Any, List, Tuple
from google.cloud import firestore
from google.oauth2 import service_account

# --------------------------- Firestore client setup ---------------------------

def get_firestore_client():
    # Use default credentials in Cloud Run, or local service account if provided
    if os.getenv("K_SERVICE"):
        return firestore.Client()
    creds_path = os.getenv("GOOGLE_APPLICATION_CREDENTIALS")
    if creds_path and os.path.exists(creds_path):
        credentials = service_account.Credentials.from_service_account_file(creds_path)
        return firestore.Client(credentials=credentials)
    return firestore.Client()

DB = get_firestore_client()

# --------------------------- URL helper functions ----------------------------

R2_BUCKET_MAPPING = {
    "zapull-achievement": "achievement.zapull.fun",
    "zapull-packs": "pack.zapull.fun",
    "zapull-cards": "card.zapull.fun",
    "zapull-avatar": "avatar.zapull.fun",
    "zapull-avator": "avatar.zapull.fun",  # historical typo
    "pack_covers": "pack.zapull.fun",
    "pack_covers_production": "pack.zapull.fun",
    "pokemon_cards_pull": "card.zapull.fun",
    "pokemon_cards_pull_production": "card.zapull.fun",
}


def is_r2_url(url: str) -> bool:
    return bool(url) and "zapull.fun" in url


def gcs_to_r2(url: str) -> Optional[str]:
    """Best-effort conversion from a GCS URL to an R2 URL based on known buckets."""
    if not url or is_r2_url(url):
        return None

    # gs://bucket/path
    m = re.match(r"gs://([^/]+)/(.+)", url)
    if m:
        bucket, path = m.group(1), m.group(2)
        r2_bucket = R2_BUCKET_MAPPING.get(bucket)
        if r2_bucket:
            return f"https://{r2_bucket}/{path}"

    # https://storage.googleapis.com/bucket/path[?query]
    m = re.match(r"https://storage\.googleapis\.com/([^/]+)/([^?]+)", url)
    if m:
        bucket, path = m.group(1), m.group(2)
        r2_bucket = R2_BUCKET_MAPPING.get(bucket)
        if r2_bucket:
            return f"https://{r2_bucket}/{path}"

    return None

# ------------------------------ Core migration -------------------------------

CARD_COLLECTIONS = ["magic", "one_piece", "pokemon"]


def get_card_image_url(collection_id: str, card_id: str) -> Optional[str]:
    """
    Fetch the authoritative image_url from the card document.
    Returns None if card not found or no image_url present.
    """
    try:
        doc_ref = DB.collection(collection_id).document(card_id)
        doc = doc_ref.get()
        if not doc.exists:
            return None
        data = doc.to_dict() or {}
        return data.get("image_url")
    except Exception:
        return None


def update_fusion_recipe_images(collection_id: str, *, dry_run: bool = False) -> Tuple[int, int, int]:
    """
    Update all fusion recipes under fusion_recipes/{collection_id}/{collection_id}/*
    Returns a tuple: (recipes_processed, recipes_updated, ingredient_urls_updated)

    If dry_run is True, no writes are performed; planned updates are printed instead.
    """
    recipes_processed = 0
    recipes_updated = 0
    ingredient_urls_updated = 0

    root = DB.collection("fusion_recipes").document(collection_id).collection(collection_id)

    # Iterate packs
    for pack_doc in root.stream():
        pack_id = pack_doc.id
        cards_ref = pack_doc.reference.collection("cards")

        # Iterate recipes in cards subcollection
        for recipe_doc in cards_ref.stream():
            recipes_processed += 1
            data = recipe_doc.to_dict() or {}

            updates: Dict[str, Any] = {}
            changed = False

            # Result card image
            result_card_id = data.get("result_card_id")
            if result_card_id:
                # Prefer authoritative URL from card doc
                card_url = get_card_image_url(collection_id, result_card_id)
                if card_url and card_url != data.get("result_card_image_url"):
                    updates["result_card_image_url"] = card_url
                    changed = True
                elif not card_url:
                    # Fallback: try converting existing URL if present
                    existing = data.get("result_card_image_url")
                    new_url = gcs_to_r2(existing) if existing else None
                    if new_url and new_url != existing:
                        updates["result_card_image_url"] = new_url
                        changed = True

            # Ingredient images
            ingredients: List[Dict[str, Any]] = data.get("ingredients", []) or []
            new_ingredients: List[Dict[str, Any]] = []
            ing_changed_any = False

            for ing in ingredients:
                ing = dict(ing)  # copy
                ing_coll = ing.get("card_collection_id") or collection_id
                ing_id = ing.get("card_id")
                if ing_id:
                    ing_card_url = get_card_image_url(ing_coll, ing_id)
                    if ing_card_url and ing_card_url != ing.get("image_url"):
                        if dry_run:
                            print(
                                f"DRY-RUN ingredient image_url change: {collection_id}/{pack_id}/cards/{recipe_doc.id} | {ing_coll}/{ing_id}: {ing.get('image_url')} -> {ing_card_url}"
                            )
                        ing["image_url"] = ing_card_url
                        ing_changed_any = True
                        ingredient_urls_updated += 1
                    elif not ing_card_url:
                        # Fallback convert
                        existing_ing_url = ing.get("image_url")
                        new_url = gcs_to_r2(existing_ing_url) if existing_ing_url else None
                        if new_url and new_url != existing_ing_url:
                            if dry_run:
                                print(
                                    f"DRY-RUN ingredient image_url change: {collection_id}/{pack_id}/cards/{recipe_doc.id} | {ing_coll}/{ing_id}: {existing_ing_url} -> {new_url}"
                                )
                            ing["image_url"] = new_url
                            ing_changed_any = True
                            ingredient_urls_updated += 1
                new_ingredients.append(ing)

            if ing_changed_any:
                updates["ingredients"] = new_ingredients
                changed = True

            if changed:
                if dry_run:
                    print(
                        f"DRY-RUN update fusion_recipes/{collection_id}/{collection_id}/{pack_id}/cards/{recipe_doc.id}: {list(updates.keys())}"
                    )
                    recipes_updated += 1
                else:
                    # Persist updates
                    recipe_doc.reference.update(updates)
                    recipes_updated += 1
                    print(
                        f"Updated fusion recipe: fusion_recipes/{collection_id}/{collection_id}/{pack_id}/cards/{recipe_doc.id}"
                    )

    return recipes_processed, recipes_updated, ingredient_urls_updated


def main():
    parser = argparse.ArgumentParser(description="Migrate fusion_recipes image URLs to R2")
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Only print planned changes without writing to Firestore",
    )
    args = parser.parse_args()

    print("Starting fusion_recipes image URL migration to R2... (dry-run=" + str(args.dry_run) + ")")
    total_processed = total_updated = total_ing_urls = 0

    for coll in CARD_COLLECTIONS:
        print("\n" + "=" * 80)
        print(f"Processing collection: {coll}")
        print("=" * 80)
        processed, updated, ing_urls = update_fusion_recipe_images(coll, dry_run=args.dry_run)
        print(
            f"Collection summary for {coll}: processed={processed}, recipes_with_changes={updated}, ingredient_urls_considered={ing_urls}"
        )
        total_processed += processed
        total_updated += updated
        total_ing_urls += ing_urls

    print("\n" + "=" * 80)
    print("Done")
    print("=" * 80)
    print(
        f"Totals: recipes_processed={total_processed}, recipes_with_changes={total_updated}, ingredient_urls_considered={total_ing_urls}"
    )


if __name__ == "__main__":
    main()

