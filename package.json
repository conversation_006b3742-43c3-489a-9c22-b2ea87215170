{"name": "boxed-admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "build:dev": "vue-tsc -b && vite build --mode development", "build:prod": "vue-tsc -b && vite build --mode production", "build:skip": "vite build --mode production.build", "build:skip:dev": "vite build --mode development", "build:skip:prod": "vite build --mode production", "build:test": "vue-tsc -b && vite build --mode test", "build:skip:test": "vite build --mode test", "build:fast": "node build/build.js", "build:fast:dev": "node build/build.js development", "build:fast:prod": "node build/build.js production", "preview": "vite preview", "preview:dist": "vite preview --outDir dist", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit -p tsconfig.app.json --composite false", "clean": "<PERSON><PERSON><PERSON> dist", "analyze": "vite build --mode production --outDir dist-analyze"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@stripe/stripe-js": "^7.5.0", "@vueuse/core": "^13.2.0", "axios": "^1.9.0", "echarts": "^5.6.0", "element-plus": "^2.9.11", "firebase": "^12.0.0", "pinia": "^3.0.2", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@types/node": "^24.0.4", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "@vitejs/plugin-vue": "^5.2.3", "@vue/eslint-config-typescript": "^13.0.0", "@vue/tsconfig": "^0.7.0", "compression-webpack-plugin": "^11.0.0", "esbuild-plugin-tsc": "^0.5.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^9.24.0", "prettier": "^3.2.5", "rimraf": "^5.0.5", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.89.0", "terser": "^5.29.2", "typescript": "~5.8.3", "vite": "^6.3.5", "vite-plugin-compression": "^0.5.1", "vue-tsc": "^2.2.8"}}