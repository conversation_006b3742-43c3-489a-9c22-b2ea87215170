{"name": "boxed-user", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "build:prod": "set NODE_ENV=production && next build", "build:test": "set NODE_ENV=test && next build --no-lint", "build:test-export": "set NODE_ENV=test && next build --no-lint && next export", "build:no-typecheck": "next build --no-lint", "build:fast": "set NODE_ENV=production && next build --no-lint", "deploy:test": "npm run build:test && node deploy-test.js", "package:test": "node deploy-test.js", "start": "next start", "start:test": "set NODE_ENV=test && next start", "lint": "next lint", "test:repro": "PLAYWRIGHT_BASE_URL=http://localhost:3000 playwright test --reporter=list", "test:repro:html": "PLAYWRIGHT_BASE_URL=http://localhost:3000 playwright test --reporter=html"}, "dependencies": {"@headlessui/react": "^2.2.7", "@heroicons/react": "^2.2.0", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.4.0", "@tanstack/react-query": "^5.85.5", "@tanstack/react-query-devtools": "^5.85.5", "axios": "^1.10.0", "canvas-confetti": "^1.9.3", "country-state-city": "^3.2.1", "firebase": "^11.8.1", "framer-motion": "^12.16.0", "google-libphonenumber": "^3.2.42", "lucide-react": "^0.525.0", "next": "15.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "stripe": "^18.3.0", "tailwind-scrollbar": "^3.0.0", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.54.2", "@types/canvas-confetti": "^1.9.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "archiver": "^7.0.1", "eslint": "^9", "eslint-config-next": "15.4", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}