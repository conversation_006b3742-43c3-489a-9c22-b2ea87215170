class StarrySkyPainter {
  static get inputProperties() {
    return [
      '--star-color',
      '--star-density',
      '--star-opacity'
    ];
  }

  paint(ctx, size, properties) {
    console.log('Paint Worklet called with size:', size.width, 'x', size.height);
    
    const starColor = properties.get('--star-color')?.toString().trim() || '#ffffff';
    const starDensity = parseFloat(properties.get('--star-density')?.toString()) || 0.001;
    const starOpacity = parseFloat(properties.get('--star-opacity')?.toString()) || 0.8;
    
    console.log('Star properties:', { starColor, starDensity, starOpacity });
    
    const width = size.width;
    const height = size.height;
    
    // 精简的星星位置分布，总共20个星星，保持均匀覆盖
    const fixedStars = [
      // 顶部边框区域 (5个)
      { x: 0.1, y: 0.03 }, { x: 0.3, y: 0.05 }, { x: 0.5, y: 0.04 }, { x: 0.7, y: 0.06 }, { x: 0.9, y: 0.03 },
      
      // 左边框区域 (3个)
      { x: 0.03, y: 0.25 }, { x: 0.04, y: 0.5 }, { x: 0.03, y: 0.75 },
      
      // 右边框区域 (3个)
      { x: 0.97, y: 0.25 }, { x: 0.96, y: 0.5 }, { x: 0.97, y: 0.75 },
      
      // 底部边框区域 (5个)
      { x: 0.1, y: 0.97 }, { x: 0.3, y: 0.96 }, { x: 0.5, y: 0.98 }, { x: 0.7, y: 0.96 }, { x: 0.9, y: 0.97 },
      
      // 四个角落的星星 (4个)
      { x: 0.02, y: 0.02 }, { x: 0.98, y: 0.02 }, { x: 0.02, y: 0.98 }, { x: 0.98, y: 0.98 }
    ];
    
    // 统一的星星大小
    const starSize = 1.5;
    
    // 设置星星样式
    ctx.fillStyle = starColor;
    ctx.globalAlpha = starOpacity;
    
    // 绘制固定的40个星星，所有星星大小一致
    // 使用固定的基准尺寸来确保星星位置在不同尺寸的卡牌中保持一致
    const baseWidth = 200; // 基准宽度
    const baseHeight = 280; // 基准高度
    
    for (let i = 0; i < fixedStars.length; i++) {
      const star = fixedStars[i];
      // 使用基准尺寸计算星星位置，确保在不同卡牌尺寸下位置一致
      const x = star.x * baseWidth;
      const y = star.y * baseHeight;
      
      // 如果星星位置超出当前容器范围，则跳过绘制
      if (x >= 0 && x <= width && y >= 0 && y <= height) {
        ctx.beginPath();
        ctx.arc(x, y, starSize, 0, Math.PI * 2);
        ctx.fill();
      }
    }
  }
}

// 注册Paint Worklet
registerPaint('starry-sky', StarrySkyPainter);