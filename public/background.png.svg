<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Dark gradient background -->
  <rect width="1920" height="1080" fill="url(#paint0_linear)"/>
  
  <!-- Stars -->
  <g opacity="0.8">
    <!-- Small stars -->
    <circle cx="200" cy="150" r="1" fill="white"/>
    <circle cx="400" cy="250" r="1" fill="white"/>
    <circle cx="600" cy="100" r="1" fill="white"/>
    <circle cx="800" cy="300" r="1" fill="white"/>
    <circle cx="1000" cy="200" r="1" fill="white"/>
    <circle cx="1200" cy="150" r="1" fill="white"/>
    <circle cx="1400" cy="250" r="1" fill="white"/>
    <circle cx="1600" cy="100" r="1" fill="white"/>
    <circle cx="1800" cy="300" r="1" fill="white"/>
    
    <!-- Medium stars -->
    <circle cx="300" cy="200" r="1.5" fill="white"/>
    <circle cx="700" cy="400" r="1.5" fill="white"/>
    <circle cx="1100" cy="300" r="1.5" fill="white"/>
    <circle cx="1500" cy="200" r="1.5" fill="white"/>
    <circle cx="1900" cy="400" r="1.5" fill="white"/>
    
    <!-- Large stars -->
    <circle cx="500" cy="500" r="2" fill="white"/>
    <circle cx="900" cy="600" r="2" fill="white"/>
    <circle cx="1300" cy="500" r="2" fill="white"/>
    <circle cx="1700" cy="600" r="2" fill="white"/>
    
    <!-- Extra stars scattered around -->
    <circle cx="250" cy="700" r="1" fill="white"/>
    <circle cx="450" cy="800" r="1" fill="white"/>
    <circle cx="650" cy="750" r="1" fill="white"/>
    <circle cx="850" cy="850" r="1" fill="white"/>
    <circle cx="1050" cy="700" r="1" fill="white"/>
    <circle cx="1250" cy="800" r="1" fill="white"/>
    <circle cx="1450" cy="750" r="1" fill="white"/>
    <circle cx="1650" cy="850" r="1" fill="white"/>
    <circle cx="1850" cy="700" r="1" fill="white"/>
    
    <!-- Twinkling effect with different opacities -->
    <circle cx="350" cy="300" r="1.5" fill="white" opacity="0.7"/>
    <circle cx="750" cy="200" r="1.5" fill="white" opacity="0.5"/>
    <circle cx="1150" cy="400" r="1.5" fill="white" opacity="0.7"/>
    <circle cx="1550" cy="300" r="1.5" fill="white" opacity="0.5"/>
  </g>
  
  <!-- Nebula effects -->
  <g opacity="0.15">
    <ellipse cx="500" cy="400" rx="300" ry="200" fill="url(#paint1_radial)"/>
    <ellipse cx="1400" cy="600" rx="400" ry="300" fill="url(#paint2_radial)"/>
    <ellipse cx="900" cy="900" rx="350" ry="250" fill="url(#paint3_radial)"/>
  </g>
  
  <!-- Gradient definitions -->
  <defs>
    <linearGradient id="paint0_linear" x1="960" y1="0" x2="960" y2="1080" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0F0B2A"/>
      <stop offset="1" stop-color="#1F1D2B"/>
    </linearGradient>
    
    <radialGradient id="paint1_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(500 400) rotate(90) scale(200 300)">
      <stop offset="0" stop-color="#6C5DD3" stop-opacity="0.8"/>
      <stop offset="1" stop-color="#6C5DD3" stop-opacity="0"/>
    </radialGradient>
    
    <radialGradient id="paint2_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(1400 600) rotate(90) scale(300 400)">
      <stop offset="0" stop-color="#FFD700" stop-opacity="0.5"/>
      <stop offset="1" stop-color="#FFD700" stop-opacity="0"/>
    </radialGradient>
    
    <radialGradient id="paint3_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(900 900) rotate(90) scale(250 350)">
      <stop offset="0" stop-color="#6C5DD3" stop-opacity="0.6"/>
      <stop offset="1" stop-color="#6C5DD3" stop-opacity="0"/>
    </radialGradient>
  </defs>
</svg>