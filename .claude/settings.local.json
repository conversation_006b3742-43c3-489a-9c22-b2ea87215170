{"permissions": {"allow": ["Bash(grep:*)", "Bash(rg:*)", "Bash(find:*)", "Bash(ls:*)", "Bash(npx:*)", "<PERSON><PERSON>(claude mcp:*)", "Bash(claude --version)", "Bash(echo $HOME)", "<PERSON><PERSON>(python3:*)", "Bash(rm:*)", "mcp__ide__getDiagnostics", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(touch:*)", "<PERSON><PERSON>(python:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "Bash(stripe payment_methods create:*)", "Bash(stripe listen:*)", "Bash(git add:*)", "Bash(git restore:*)", "Bash(git remote set-url:*)", "Bash(git push:*)", "Bash(gh auth:*)", "Bash(git fetch:*)", "Bash(gcloud run services describe:*)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(/doctor)", "<PERSON><PERSON>(chmod:*)", "Bash(# Test confirmation sound (urgent, needs attention)\n/Users/<USER>/.claude/hooks/notification-hook.sh \"\"confirmation\"\" \"\"Test: User confirmation needed\"\")", "WebFetch(domain:docs.anthropic.com)", "Bash(git checkout:*)", "Bash(pip install:*)", "<PERSON><PERSON>(test -d .git)", "<PERSON><PERSON>(test:*)", "Bash(npm run dev:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(cat:*)", "Bash(./build-and-deploy.sh:*)", "Bash(gcloud builds describe:*)", "Bash(node:*)", "Bash(firebase functions:list:*)", "<PERSON><PERSON>(curl:*)", "WebFetch(domain:github.com)", "WebFetch(domain:cloud.google.com)", "Bash(npm run build:*)", "Bash(npm install:*)", "Bash(gcloud builds submit:*)", "Bash(gcloud config get-value:*)", "<PERSON><PERSON>(od:*)", "Bash(npm run lint:*)", "Bash(npm uninstall:*)", "Bash(awk:*)"], "deny": [], "defaultMode": "acceptEdits"}}