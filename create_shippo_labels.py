#!/usr/bin/env python3
"""
Local script to create Shippo shipping labels and export results to Excel
"""

import os
import logging
from datetime import datetime
from typing import Dict, List, Any
import pandas as pd
from shippo import Shippo
import shippo.models.components as components

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# ==================== CONFIGURATION ====================
# Set your Shippo API key here
SHIPPO_API_KEY = "shippo_test_f722395e8ea267c139451e2d063d6cc76eea8e58"  # Replace with your actual Shippo API key

# Shippo from address (your address)
SHIPPO_FROM_ADDRESS = {
    'name': 'zapull inc',
    'street1': '7965 N High Street',
    'city': 'Columbus',
    'state': 'OH',
    'zip': '43235',
    'country': 'US',
    'phone': '6145580845',
    'email': '<EMAIL>'
}

# Standard parcel dimensions for cards
CARD_PARCEL_DIMENSIONS = {
    'length': '6',
    'width': '4',
    'height': '1',
    'distance_unit': 'in',
    'weight': '3',
    'mass_unit': 'oz'
}

# Insurance amount (set to 0 to disable insurance)
DEFAULT_INSURANCE_AMOUNT = 100

# Output Excel file
OUTPUT_FILE = "shippo_labels_output.xlsx"

# ==================== SAMPLE DATA ====================
# Add your shipments here - this is sample data
SAMPLE_SHIPMENTS = [
    {
        'shipment_id': 'SHIP001',
        'user_id': 'USER001',
        'name': 'Mia Garcia',
        'street': '8330 Thompson Lake Dr',
        'city': 'Missouri City',
        'state': 'TX',
        'zip': '77459',
        'country': 'US',
        'phone': '',  # No phone provided
        'email': ''
    },
    {
        'shipment_id': 'SHIP002',
        'user_id': 'USER002',
        'name': 'Kokoro Matsumoto',
        'street': '1525 10th Ave',
        'city': 'Honolulu',
        'state': 'HI',
        'zip': '96814',
        'country': 'US',
        'phone': '',  # No phone provided
        'email': ''
    },
    {
        'shipment_id': 'SHIP003',
        'user_id': 'USER003',
        'name': 'Rikki',
        'street': '8 Williamson Street',
        'city': 'East Rockaway',
        'state': 'NY',
        'zip': '11518',
        'country': 'US',
        'phone': '',  # No phone provided
        'email': ''
    },
    {
        'shipment_id': 'SHIP004',
        'user_id': 'USER004',
        'name': 'Joe Lee',
        'street': '875 Old Pelzer road lot#1',
        'city': 'Piedmont',
        'state': 'SC',
        'zip': '29673',
        'country': 'US',
        'phone': '',  # No phone provided
        'email': ''
    }
]

# ==================== FUNCTIONS ====================


def create_shippo_shipment(shipment_data: Dict[str, Any], shippo_sdk: Shippo) -> Dict[str, Any]:
    """Create Shippo shipment and purchase label"""
    try:
        logger.info(f"Processing shipment {shipment_data['shipment_id']} for {shipment_data['name']}")
        
        # Create address dict, only include phone if provided
        to_address_dict = {
            'name': shipment_data['name'],
            'street1': shipment_data['street'],
            'city': shipment_data['city'],
            'state': shipment_data['state'],
            'zip': shipment_data['zip'],
            'country': shipment_data['country']
        }
        
        # Add phone only if provided
        if shipment_data.get('phone'):
            to_address_dict['phone'] = shipment_data['phone']
        
        # Add email only if provided
        if shipment_data.get('email'):
            to_address_dict['email'] = shipment_data['email']
        
        # Create shipment request
        shipment_request = components.ShipmentCreateRequest(
            address_from=components.AddressCreateRequest(**SHIPPO_FROM_ADDRESS),
            address_to=components.AddressCreateRequest(**to_address_dict),
            parcels=[components.ParcelCreateRequest(**CARD_PARCEL_DIMENSIONS)],
            async_=False
        )
        
        # Create shipment
        shipment = shippo_sdk.shipments.create(shipment_request)
        
        if not shipment.rates:
            raise Exception("No shipping rates available")
        
        # Find the cheapest rate
        cheapest_rate = min(shipment.rates, key=lambda r: float(r.amount))
        
        # Create transaction (purchase label)
        transaction_request = components.TransactionCreateRequest(
            rate=cheapest_rate.object_id,
            label_file_type="PDF_4X6",
            async_=False,
            insurance_amount=DEFAULT_INSURANCE_AMOUNT
        )
        
        transaction = shippo_sdk.transactions.create(transaction_request)
        
        # Return result
        return {
            'shipment_id': shipment_data['shipment_id'],
            'user_id': shipment_data['user_id'],
            'recipient_name': shipment_data['name'],
            'recipient_address': f"{shipment_data['street']}, {shipment_data['city']}, {shipment_data['state']} {shipment_data['zip']}",
            'country': shipment_data['country'],
            'phone': shipment_data['phone'] or 'N/A',
            'shipping_cost': float(cheapest_rate.amount),
            'carrier': cheapest_rate.provider,
            'service': cheapest_rate.servicelevel.name if hasattr(cheapest_rate.servicelevel, 'name') else 'Standard',
            'tracking_number': transaction.tracking_number,
            'label_url': transaction.label_url,
            'tracking_url': transaction.tracking_url_provider,
            'status': 'Success',
            'error_message': '',
            'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
    except Exception as e:
        logger.error(f"Error processing shipment {shipment_data['shipment_id']}: {e}")
        return {
            'shipment_id': shipment_data['shipment_id'],
            'user_id': shipment_data['user_id'],
            'recipient_name': shipment_data['name'],
            'recipient_address': f"{shipment_data['street']}, {shipment_data['city']}, {shipment_data['state']} {shipment_data['zip']}",
            'country': shipment_data['country'],
            'phone': shipment_data['phone'] or 'N/A',
            'shipping_cost': 0,
            'carrier': '',
            'service': '',
            'tracking_number': '',
            'label_url': '',
            'tracking_url': '',
            'status': 'Failed',
            'error_message': str(e),
            'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

def main():
    """Main function to process shipments and create Excel output"""
    print("\n=== Shippo Label Creator ===\n")
    
    # Check API key
    if SHIPPO_API_KEY == "YOUR_SHIPPO_API_KEY_HERE":
        print("ERROR: Please set your Shippo API key in the script!")
        print("Edit the SHIPPO_API_KEY variable at the top of the script.")
        return
    
    # Initialize Shippo SDK
    shippo_sdk = Shippo(api_key_header=SHIPPO_API_KEY)
    
    # Process shipments
    results = []
    total = len(SAMPLE_SHIPMENTS)
    
    print(f"Processing {total} shipments...\n")
    
    for i, shipment in enumerate(SAMPLE_SHIPMENTS, 1):
        print(f"[{i}/{total}] Processing shipment {shipment['shipment_id']}...")
        result = create_shippo_shipment(shipment, shippo_sdk)
        results.append(result)
        
        if result['status'] == 'Success':
            print(f"  ✓ Success! Tracking: {result['tracking_number']}")
        else:
            print(f"  ✗ Failed: {result['error_message']}")
    
    # Create DataFrame and save to Excel
    df = pd.DataFrame(results)
    
    # Reorder columns for better readability
    column_order = [
        'shipment_id', 'user_id', 'recipient_name', 'recipient_address',
        'country', 'phone', 'shipping_cost',
        'carrier', 'service', 'tracking_number', 'label_url',
        'tracking_url', 'status', 'error_message', 'created_at'
    ]
    df = df[column_order]
    
    # Save to Excel with formatting
    with pd.ExcelWriter(OUTPUT_FILE, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='Shipping Labels', index=False)
        
        # Auto-adjust column widths
        worksheet = writer.sheets['Shipping Labels']
        for column in df:
            column_length = max(df[column].astype(str).map(len).max(), len(column))
            col_idx = df.columns.get_loc(column)
            worksheet.column_dimensions[chr(65 + col_idx)].width = min(column_length + 2, 50)
    
    print(f"\n✓ Results saved to: {OUTPUT_FILE}")
    
    # Summary
    successful = len([r for r in results if r['status'] == 'Success'])
    failed = len([r for r in results if r['status'] == 'Failed'])
    
    print(f"\nSummary:")
    print(f"  Total processed: {total}")
    print(f"  Successful: {successful}")
    print(f"  Failed: {failed}")
    
    if successful > 0:
        total_shipping = sum(r['shipping_cost'] for r in results if r['status'] == 'Success')
        print(f"  Total shipping cost: ${total_shipping:.2f}")

if __name__ == "__main__":
    main()