import requests
import json

# API endpoint
url = "https://user-backend-351785787544.us-central1.run.app/users/api/v1/achievements/achievements"

# Headers
headers = {
    "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************",
    "Content-Type": "application/json"
}

# Query parameters
params = {
    "sort_by": "created_at",
    "per_page": 10,
    "page": 1,
    "sort_order": "desc"
}

# Make the request
response = requests.get(url, headers=headers, params=params)

# Print the result
print(f"Status Code: {response.status_code}")
print("\nResponse:")
print(json.dumps(response.json(), indent=2))
