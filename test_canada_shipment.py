#!/usr/bin/env python3
"""
Test script for shipping to Canadian addresses using Shippo API.
This script simulates the cloud function behavior for Canadian shipments.
"""

import logging
import os
import shippo
from shippo import Shippo
import shippo.models.components as components
from datetime import datetime
import json

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_canada_shipment():
    """
    Test shipping to a Canadian address using Shippo API.
    This simulates the cloud function process for Canadian addresses.
    """
    try:
        logging.info("Starting Canadian shipment test")
        
        # Get Shippo API key from environment variables
        shippo_api_key = os.getenv('SHIPPO_API_KEY')
        if not shippo_api_key:
            logging.error("SHIPPO_API_KEY environment variable not set")
            logging.info("Please set SHIPPO_API_KEY environment variable with your Shippo API key")
            return
        
        # Initialize Shippo SDK
        shippo_sdk = Shippo(api_key_header=shippo_api_key)
        
        # Test Canadian shipping address (using a real, well-known address)
        canadian_address = {
            'name': '<PERSON>',
            'street': '100 Queen Street West',  # Real street in Toronto
            'city': 'Toronto',
            'state': 'ON',  # Ontario
            'zip': 'M5H 2N2',  # Valid Toronto postal code
            'country': 'CA'  # Canada
        }
        
        # Test phone number (Canadian format)
        phone_number = '******-555-0123'
        
        logging.info(f"Testing shipment to Canadian address: {canadian_address['city']}, {canadian_address['state']}, {canadian_address['country']}")
        
        # Create Shippo address object for Canadian address
        logging.info("Creating Shippo address object...")
        shippo_address = shippo_sdk.addresses.create(
            components.AddressCreateRequest(
                name=canadian_address.get('name', ''),
                street1=canadian_address.get('street', ''),
                city=canadian_address.get('city', ''),
                state=canadian_address.get('state', ''),
                zip=canadian_address.get('zip', ''),
                country=canadian_address.get('country', 'CA'),
                phone=phone_number,
                validate=True
            )
        )
        
        logging.info(f"✅ Address created successfully: {shippo_address.object_id}")

        # Debug: Print the full address object structure
        logging.info(f"Address object type: {type(shippo_address)}")

        # Check if validation results exist and handle them safely
        if hasattr(shippo_address, 'validation_results') and shippo_address.validation_results:
            validation_results = shippo_address.validation_results
            logging.info(f"Validation results type: {type(validation_results)}")

            is_valid = getattr(validation_results, 'is_valid', None)
            logging.info(f"Address validation: {'Valid' if is_valid else 'Invalid' if is_valid is not None else 'Unknown'}")

            # Try to get validation messages safely
            messages = getattr(validation_results, 'messages', None)
            if not is_valid and messages:
                logging.warning("Address validation failed:")
                try:
                    for message in messages:
                        if hasattr(message, 'text'):
                            logging.warning(f"  - {message.text}")
                        else:
                            logging.warning(f"  - {message}")
                except Exception as msg_error:
                    logging.warning(f"Could not iterate validation messages: {msg_error}")
            elif not is_valid:
                logging.warning("Address validation failed but no detailed messages available")
        else:
            logging.info("Address validation: No validation results available")

        # Continue with shipment creation even if validation failed
        logging.info("Continuing with shipment creation...")
        
        # Create Shippo parcel object (standard card package dimensions)
        logging.info("Creating Shippo parcel object...")
        shippo_parcel = shippo_sdk.parcels.create(
            components.ParcelCreateRequest(
                length="6",
                width="4", 
                height="1",
                distance_unit="in",
                weight="4",
                mass_unit="oz"
            )
        )
        
        logging.info(f"✅ Parcel created successfully: {shippo_parcel.object_id}")
        
        # Create Shippo shipment
        logging.info("Creating Shippo shipment...")
        shippo_shipment = shippo_sdk.shipments.create(
            components.ShipmentCreateRequest(
                address_from=components.AddressCreateRequest(
                    name="zapull.fun",
                    street1="10716 Pearl Creek Drive",
                    city="Plain City",
                    state="OH",
                    zip="43064",
                    country="US",
                    phone="2202461193",
                    email="<EMAIL>"
                ),
                address_to=components.AddressCreateRequest(
                    name=canadian_address.get('name', ''),
                    street1=canadian_address.get('street', ''),
                    city=canadian_address.get('city', ''),
                    state=canadian_address.get('state', ''),
                    zip=canadian_address.get('zip', ''),
                    country=canadian_address.get('country', 'CA'),
                    phone=phone_number
                ),
                parcels=[components.ParcelCreateRequest(
                    length="6",
                    width="4",
                    height="1", 
                    distance_unit="in",
                    weight="4",
                    mass_unit="oz"
                )],
                async_=False
            )
        )
        
        logging.info(f"✅ Shipment created successfully: {shippo_shipment.object_id}")
        
        # Find available shipping rates
        if not shippo_shipment.rates:
            logging.error("❌ No shipping rates available for Canadian shipment")
            return

        logging.info(f"\n📦 Found {len(shippo_shipment.rates)} shipping rates for US → Canada:")
        logging.info("=" * 80)

        # Group rates by provider for better display
        rates_by_provider = {}
        for rate in shippo_shipment.rates:
            provider = rate.provider
            if provider not in rates_by_provider:
                rates_by_provider[provider] = []
            rates_by_provider[provider].append(rate)

        # Display all available rates grouped by provider
        for provider, rates in rates_by_provider.items():
            logging.info(f"\n🚚 {provider.upper()} Options:")
            for i, rate in enumerate(rates, 1):
                # Get additional rate details
                amount = rate.amount
                currency = rate.currency
                service = rate.servicelevel.name if hasattr(rate.servicelevel, 'name') else 'Unknown Service'
                estimated_days = getattr(rate, 'estimated_days', 'N/A')

                # Format the rate display
                logging.info(f"  {i}. {service}")
                logging.info(f"     💰 Price: ${amount} {currency}")
                logging.info(f"     ⏱️  Delivery: {estimated_days} days")

                # Show additional details if available
                if hasattr(rate, 'duration_terms'):
                    logging.info(f"     📋 Terms: {rate.duration_terms}")

                logging.info("")  # Empty line for readability

        # Find the cheapest shipping rate
        cheapest_rate = min(
            shippo_shipment.rates,
            key=lambda r: float(r.amount)
        )

        # Find the fastest shipping rate
        fastest_rate = None
        try:
            rates_with_days = [r for r in shippo_shipment.rates if hasattr(r, 'estimated_days') and r.estimated_days is not None]
            if rates_with_days:
                fastest_rate = min(rates_with_days, key=lambda r: int(r.estimated_days))
        except:
            pass

        logging.info("=" * 80)
        logging.info("🎯 RECOMMENDED OPTIONS:")
        logging.info(f"💰 CHEAPEST: {cheapest_rate.provider} {cheapest_rate.servicelevel.name} - ${cheapest_rate.amount} {cheapest_rate.currency}")

        if fastest_rate and fastest_rate != cheapest_rate:
            logging.info(f"⚡ FASTEST: {fastest_rate.provider} {fastest_rate.servicelevel.name} - ${fastest_rate.amount} {fastest_rate.currency} ({fastest_rate.estimated_days} days)")

        logging.info("=" * 80)
        
        # Test creating a transaction (purchasing the label)
        # Note: This will actually purchase a shipping label and charge your account!
        # Uncomment the following section only if you want to test the full flow
        
        """
        logging.info("Creating Shippo transaction (purchasing label)...")
        shippo_transaction = shippo_sdk.transactions.create(
            components.TransactionCreateRequest(
                rate=cheapest_rate.object_id,
                label_file_type="PDF",
                async_=False,
                insurance_amount=100  # $100 insurance
            )
        )
        
        logging.info(f"✅ Transaction created successfully: {shippo_transaction.object_id}")
        logging.info(f"📦 Tracking number: {shippo_transaction.tracking_number}")
        logging.info(f"🔗 Tracking URL: {shippo_transaction.tracking_url_provider}")
        logging.info(f"📄 Label URL: {shippo_transaction.label_url}")
        
        # Prepare the data that would be stored in Firestore
        shipment_data = {
            'shippo_address_id': shippo_address.object_id,
            'shippo_parcel_id': shippo_parcel.object_id,
            'shippo_shipment_id': shippo_shipment.object_id,
            'shippo_transaction_id': shippo_transaction.object_id,
            'shippo_label_url': shippo_transaction.label_url,
            'tracking_number': shippo_transaction.tracking_number,
            'tracking_url': shippo_transaction.tracking_url_provider,
            'shipping_status': 'label_created',
            'status': 'label_created',
            'processed_at': datetime.utcnow().isoformat()
        }
        
        logging.info("📋 Shipment data that would be stored:")
        logging.info(json.dumps(shipment_data, indent=2))
        """
        
        # Instead of actually purchasing, just show what would happen
        logging.info("🧪 Test mode: Not purchasing actual label")
        logging.info("To purchase a real label, uncomment the transaction creation section")
        
        # Prepare the data that would be stored in Firestore (without transaction)
        shipment_data = {
            'shippo_address_id': shippo_address.object_id,
            'shippo_parcel_id': shippo_parcel.object_id,
            'shippo_shipment_id': shippo_shipment.object_id,
            'cheapest_rate_id': cheapest_rate.object_id,
            'cheapest_rate_amount': cheapest_rate.amount,
            'cheapest_rate_currency': cheapest_rate.currency,
            'cheapest_rate_provider': cheapest_rate.provider,
            'cheapest_rate_service': cheapest_rate.servicelevel.name,
            'estimated_days': cheapest_rate.estimated_days,
            'shipping_status': 'rates_retrieved',
            'status': 'rates_retrieved',
            'processed_at': datetime.utcnow().isoformat()
        }
        
        logging.info("📋 Shipment data that would be stored in Firestore:")
        logging.info(json.dumps(shipment_data, indent=2))
        
        logging.info("✅ Canadian shipment test completed successfully!")
        
        # Test summary
        logging.info("\n" + "="*70)
        logging.info("🇨🇦 CANADIAN SHIPMENT TEST SUMMARY")
        logging.info("="*70)
        logging.info(f"📍 Origin: Plain City, OH, US")
        logging.info(f"📍 Destination: {canadian_address['city']}, {canadian_address['state']}, {canadian_address['country']}")
        logging.info(f"📦 Package: 6\"×4\"×1\", 4oz (Trading Cards)")
        logging.info(f"🚚 Total carriers available: {len(rates_by_provider)}")

        # List all available carriers
        carrier_names = list(rates_by_provider.keys())
        logging.info(f"📋 Available carriers: {', '.join(carrier_names)}")

        logging.info(f"💰 Cheapest option: {cheapest_rate.provider} {cheapest_rate.servicelevel.name} - ${cheapest_rate.amount} {cheapest_rate.currency}")

        if fastest_rate:
            logging.info(f"⚡ Fastest option: {fastest_rate.provider} {fastest_rate.servicelevel.name} - {fastest_rate.estimated_days} days")

        # Address validation status
        validation_status = "Unknown"
        if hasattr(shippo_address, 'validation_results') and shippo_address.validation_results:
            is_valid = getattr(shippo_address.validation_results, 'is_valid', None)
            validation_status = 'Passed' if is_valid else 'Failed' if is_valid is not None else 'Unknown'

        logging.info(f"✅ Address validation: {validation_status}")
        logging.info("="*70)
        
    except Exception as e:
        logging.error(f"❌ Error in Canadian shipment test: {e}")
        import traceback
        traceback.print_exc()

def test_carrier_coverage():
    """Test and display carrier coverage information for Canadian shipping."""

    logging.info("\n🚚 CARRIER COVERAGE ANALYSIS")
    logging.info("="*60)

    # Expected carriers for US → Canada shipping
    expected_carriers = {
        'USPS': {
            'services': ['Priority Mail International', 'Priority Mail Express International', 'First-Class Mail International'],
            'notes': 'Most economical for small packages'
        },
        'UPS': {
            'services': ['UPS Standard', 'UPS Expedited', 'UPS Express Saver', 'UPS Express'],
            'notes': 'Reliable tracking and delivery confirmation'
        },
        'FedEx': {
            'services': ['FedEx International Economy', 'FedEx International Priority', 'FedEx International First'],
            'notes': 'Fast international delivery'
        },
        'DHL': {
            'services': ['DHL Express Worldwide', 'DHL Express 12:00'],
            'notes': 'Premium international express service'
        }
    }

    for carrier, info in expected_carriers.items():
        logging.info(f"\n📦 {carrier}:")
        logging.info(f"   Services: {', '.join(info['services'])}")
        logging.info(f"   Notes: {info['notes']}")

    logging.info("\n💡 The actual available carriers and rates will depend on:")
    logging.info("   • Package dimensions and weight")
    logging.info("   • Destination address")
    logging.info("   • Shippo account configuration")
    logging.info("   • Carrier agreements and availability")

def test_multiple_canadian_addresses():
    """Test multiple Canadian addresses to verify different provinces work."""

    canadian_addresses = [
        {
            'name': 'Alice Smith',
            'street': '456 Queen Street West',
            'city': 'Toronto',
            'state': 'ON',
            'zip': 'M5V 2B4',
            'country': 'CA',
            'description': 'Ontario - Major city'
        },
        {
            'name': 'Bob Johnson',
            'street': '789 Granville Street',
            'city': 'Vancouver',
            'state': 'BC',
            'zip': 'V6Z 1K3',
            'country': 'CA',
            'description': 'British Columbia - West coast'
        },
        {
            'name': 'Carol Brown',
            'street': '321 Rue Saint-Jacques',
            'city': 'Montreal',
            'state': 'QC',
            'zip': 'H2Y 1L6',
            'country': 'CA',
            'description': 'Quebec - French-speaking province'
        },
        {
            'name': 'David Wilson',
            'street': '555 8th Avenue SW',
            'city': 'Calgary',
            'state': 'AB',
            'zip': 'T2P 1G2',
            'country': 'CA',
            'description': 'Alberta - Prairie province'
        }
    ]

    logging.info("\n🇨🇦 CANADIAN PROVINCES COVERAGE TEST")
    logging.info("="*60)

    for i, address in enumerate(canadian_addresses, 1):
        logging.info(f"\n📍 Address {i}: {address['description']}")
        logging.info(f"   {address['street']}")
        logging.info(f"   {address['city']}, {address['state']} {address['zip']}")
        logging.info(f"   {address['country']}")

    logging.info(f"\n✅ Testing coverage for {len(canadian_addresses)} major Canadian regions")
    logging.info("   This ensures carriers serve all major Canadian provinces")

if __name__ == "__main__":
    print("🇨🇦 Canadian Shipment Test - Carrier Coverage Analysis")
    print("=" * 70)
    print("This script tests Shippo API integration with Canadian addresses")
    print("and displays all available carriers and shipping options.")
    print("Make sure to set SHIPPO_API_KEY environment variable before running.")
    print("=" * 70)

    # Show expected carrier coverage
    test_carrier_coverage()

    # Run the main test with detailed carrier analysis
    test_canada_shipment()

    # Test multiple Canadian addresses
    test_multiple_canadian_addresses()
