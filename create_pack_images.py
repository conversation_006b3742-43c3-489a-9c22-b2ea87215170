#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to create folders with pack images showing the three most expensive cards.
Creates a folder structure: pack_images/[collection_name]/[pack_name]/[card_name].jpg
"""

import os
import asyncio
import aiohttp
from typing import List, Dict, Any, Optional
from pathlib import Path
import re
import argparse
from google.cloud import firestore
from google.oauth2 import service_account
import json

# Configuration
OUTPUT_BASE_DIR = "pack_images"
TOP_N_CARDS = 3  # Number of most expensive cards to fetch

class PackImageDownloader:
    def __init__(self, credentials_path: str = None):
        """Initialize the downloader with Firestore credentials."""
        if credentials_path and os.path.exists(credentials_path):
            credentials = service_account.Credentials.from_service_account_file(
                credentials_path
            )
            self.db = firestore.AsyncClient(credentials=credentials)
        else:
            # Use default credentials if no path provided
            self.db = firestore.AsyncClient()
        
        self.session = None
        
    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()
    
    def sanitize_filename(self, name: str) -> str:
        """Sanitize a string to be used as a filename."""
        # Remove invalid characters for filenames
        sanitized = re.sub(r'[<>:"/\\|?*]', '_', name)
        # Remove extra whitespace and replace with underscore
        sanitized = re.sub(r'\s+', '_', sanitized)
        # Limit length to avoid filesystem issues
        return sanitized[:100]
    
    async def download_image(self, url: str, filepath: Path) -> bool:
        """Download an image from URL to filepath."""
        if not url:
            print(f"  ⚠️  No URL provided for {filepath.name}")
            return False
            
        try:
            async with self.session.get(url) as response:
                if response.status == 200:
                    content = await response.read()
                    filepath.write_bytes(content)
                    print(f"  ✅ Downloaded: {filepath.name}")
                    return True
                else:
                    print(f"  ❌ Failed to download {filepath.name}: HTTP {response.status}")
                    return False
        except Exception as e:
            print(f"  ❌ Error downloading {filepath.name}: {str(e)}")
            return False
    
    async def get_all_collections(self) -> List[str]:
        """Get all pack collections from Firestore."""
        collections = []
        try:
            # Get all documents in the 'packs' collection
            packs_collection = self.db.collection('packs')
            docs = packs_collection.list_documents()
            
            async for doc in docs:
                collections.append(doc.id)
                
            print(f"Found {len(collections)} collections: {', '.join(collections)}")
            return collections
        except Exception as e:
            print(f"Error fetching collections: {str(e)}")
            return []
    
    async def get_packs_in_collection(self, collection_id: str) -> List[Dict[str, Any]]:
        """Get all packs in a specific collection."""
        packs = []
        try:
            # Get all packs in the subcollection
            packs_ref = (
                self.db.collection('packs')
                .document(collection_id)
                .collection(collection_id)
            )
            
            docs = await packs_ref.get()
            
            for doc in docs:
                pack_data = doc.to_dict()
                pack_data['id'] = doc.id
                packs.append(pack_data)
                
            print(f"  Found {len(packs)} packs in collection '{collection_id}'")
            return packs
        except Exception as e:
            print(f"  Error fetching packs from collection '{collection_id}': {str(e)}")
            return []
    
    async def get_top_cards_in_pack(self, collection_id: str, pack_id: str, pack_name: str) -> List[Dict[str, Any]]:
        """Get the top N most expensive cards in a pack."""
        try:
            # Get reference to the pack's cards subcollection
            cards_ref = (
                self.db.collection('packs')
                .document(collection_id)
                .collection(collection_id)
                .document(pack_id)
                .collection('cards')
            )
            
            # Get all cards
            cards_docs = await cards_ref.get()
            
            # Convert to list and sort by point_worth
            cards = []
            for doc in cards_docs:
                card_data = doc.to_dict()
                card_data['id'] = doc.id
                cards.append(card_data)
            
            # Sort by point_worth in descending order
            cards.sort(key=lambda x: x.get('point_worth', 0), reverse=True)
            
            # Get top N cards
            top_cards = cards[:TOP_N_CARDS]
            
            if top_cards:
                print(f"    Found top {len(top_cards)} cards in pack '{pack_name}'")
                for i, card in enumerate(top_cards, 1):
                    print(f"      {i}. {card.get('card_name', 'Unknown')} - {card.get('point_worth', 0)} points")
            else:
                print(f"    No cards found in pack '{pack_name}'")
                
            return top_cards
        except Exception as e:
            print(f"    Error fetching cards from pack '{pack_name}': {str(e)}")
            return []
    
    async def process_pack(self, collection_id: str, pack_data: Dict[str, Any]):
        """Process a single pack - create folder and download top card images."""
        pack_id = pack_data['id']
        pack_name = pack_data.get('name', pack_id)
        
        print(f"  Processing pack: {pack_name}")
        
        # Create folder structure
        collection_folder = Path(OUTPUT_BASE_DIR) / self.sanitize_filename(collection_id)
        pack_folder = collection_folder / self.sanitize_filename(pack_name)
        pack_folder.mkdir(parents=True, exist_ok=True)
        
        # Get top cards
        top_cards = await self.get_top_cards_in_pack(collection_id, pack_id, pack_name)
        
        # Download card images
        download_tasks = []
        for card in top_cards:
            card_name = card.get('card_name', f"card_{card.get('id', 'unknown')}")
            image_url = card.get('image_url', '')
            
            if image_url:
                # Determine file extension from URL or default to .jpg
                ext = '.jpg'
                if '.' in image_url:
                    url_ext = image_url.split('.')[-1].lower()
                    if url_ext in ['jpg', 'jpeg', 'png', 'gif', 'webp']:
                        ext = f'.{url_ext}'
                
                filename = f"{self.sanitize_filename(card_name)}{ext}"
                filepath = pack_folder / filename
                
                # Skip if file already exists
                if filepath.exists():
                    print(f"    ⏭️  Skipping {filename} (already exists)")
                else:
                    download_tasks.append(self.download_image(image_url, filepath))
        
        # Execute downloads in parallel
        if download_tasks:
            await asyncio.gather(*download_tasks)
        
        # Create a summary file for the pack
        summary_file = pack_folder / "pack_info.txt"
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write(f"Pack: {pack_name}\n")
            f.write(f"Collection: {collection_id}\n")
            f.write(f"Pack ID: {pack_id}\n")
            f.write(f"\nTop {TOP_N_CARDS} Most Expensive Cards:\n")
            f.write("=" * 50 + "\n")
            for i, card in enumerate(top_cards, 1):
                f.write(f"\n{i}. {card.get('card_name', 'Unknown')}\n")
                f.write(f"   Points: {card.get('point_worth', 0)}\n")
                f.write(f"   Rarity: {card.get('rarity', 'N/A')}\n")
                if card.get('probability'):
                    f.write(f"   Probability: {card.get('probability')}%\n")
    
    async def run(self, collection_filter: Optional[str] = None):
        """Main execution function.
        
        Args:
            collection_filter: Optional collection ID to process. If None, processes all collections.
        """
        print(f"🚀 Starting Pack Image Downloader")
        print(f"📁 Output directory: {OUTPUT_BASE_DIR}")
        print(f"🎯 Fetching top {TOP_N_CARDS} cards per pack")
        
        if collection_filter:
            print(f"🎨 Filter: Only processing '{collection_filter}' collection\n")
        else:
            print(f"🎨 Processing all collections\n")
        
        # Create base output directory
        Path(OUTPUT_BASE_DIR).mkdir(exist_ok=True)
        
        # Get collections to process
        if collection_filter:
            # Only process the specified collection
            collections = [collection_filter]
            print(f"Processing single collection: {collection_filter}")
        else:
            # Get all collections
            collections = await self.get_all_collections()
            
            if not collections:
                print("No collections found. Exiting.")
                return
        
        # Process each collection
        for collection_id in collections:
            print(f"\n📦 Processing collection: {collection_id}")
            
            # Get all packs in collection
            packs = await self.get_packs_in_collection(collection_id)
            
            if not packs:
                print(f"  ⚠️  No packs found in collection '{collection_id}'")
                continue
            
            # Process each pack
            for pack_data in packs:
                await self.process_pack(collection_id, pack_data)
        
        print(f"\n✨ Done! Images saved to '{OUTPUT_BASE_DIR}' directory")
        print(f"📊 Structure: {OUTPUT_BASE_DIR}/[collection]/[pack_name]/[card_name].jpg")


async def main():
    """Main entry point."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(
        description="Download top card images from packs and organize them by collection and pack name."
    )
    parser.add_argument(
        '--collection',
        type=str,
        default=None,
        help='Collection ID to process (e.g., "pokemon"). If not specified, processes all collections.'
    )
    parser.add_argument(
        '--pokemon',
        action='store_true',
        help='Shortcut for --collection pokemon'
    )
    
    args = parser.parse_args()
    
    # Handle the --pokemon shortcut
    collection_filter = args.collection
    if args.pokemon:
        collection_filter = 'pokemon'
    
    # You can specify a path to service account credentials if needed
    # Otherwise it will use default credentials (from environment or compute engine)
    credentials_path = None  # or "path/to/service-account-key.json"
    
    # Check for credentials in common locations
    possible_paths = [
        "backend/service-account-key.json",
        "user_backend/service-account-key.json",
        "service-account-key.json",
        os.environ.get('GOOGLE_APPLICATION_CREDENTIALS', '')
    ]
    
    for path in possible_paths:
        if path and os.path.exists(path):
            credentials_path = path
            print(f"📋 Using credentials from: {path}")
            break
    
    if not credentials_path:
        print("⚠️  No credentials file found. Attempting to use default credentials...")
        print("   If this fails, set GOOGLE_APPLICATION_CREDENTIALS environment variable")
        print("   or place service-account-key.json in the project directory\n")
    
    async with PackImageDownloader(credentials_path) as downloader:
        await downloader.run(collection_filter)


if __name__ == "__main__":
    # Run the async main function
    asyncio.run(main())