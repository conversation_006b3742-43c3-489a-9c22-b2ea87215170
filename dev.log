   ▲ Next.js 15.4.6 (Turbopack)
   - Local:        http://localhost:3000
   - Network:      http://*************:3000
   - Environments: .env.local

 ✓ Starting...
 ✓ Ready in 881ms
 ○ Compiling / ...
 ✓ Compiled / in 2.4s
 GET / 200 in 2785ms
 GET / 200 in 246ms
 ⚠ The "images.domains" configuration is deprecated. Please use "images.remotePatterns" configuration instead.
 GET / 200 in 86ms
 GET / 200 in 141ms
 GET / 200 in 104ms
 ✓ Compiled in 375ms
 ✓ Compiled in 300ms
 ✓ Compiled / in 26ms
 GET / 200 in 443ms
 ✓ Compiled /favicon.ico in 235ms
 GET /favicon.ico?favicon.45db1c09.ico 200 in 514ms
 ✓ Compiled in 120ms
 ✓ Compiled / in 21ms
 GET /?collection=pokemon 200 in 177ms
 GET /?collection=pokemon 200 in 380ms
 GET /favicon.ico?favicon.45db1c09.ico 200 in 271ms
 GET /favicon.ico?favicon.45db1c09.ico 200 in 288ms
 ○ Compiling /packs/[collectionId]/[packId] ...
 ✓ Compiled /packs/[collectionId]/[packId] in 653ms
 GET /packs/pokemon/%E6%B5%8B%E8%AF%95%E5%8D%A1%E5%8C%85 200 in 1023ms
 ✓ Compiled in 167ms
 ✓ Compiled in 174ms
 ✓ Compiled in 172ms
 ✓ Compiled in 132ms
 ✓ Compiled in 135ms
 ✓ Compiled in 138ms
 ✓ Compiled in 126ms
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ✓ Compiled in 147ms
 GET /?collection=pokemon 200 in 639ms
 GET /favicon.ico?favicon.45db1c09.ico 200 in 259ms
 GET /favicon.ico?favicon.45db1c09.ico 200 in 264ms
 ✓ Compiled in 126ms
 ✓ Compiled in 130ms
 ✓ Compiled in 132ms
 ✓ Compiled in 121ms
 GET /?collection=pokemon 200 in 602ms
 GET /favicon.ico?favicon.45db1c09.ico 200 in 304ms
 GET /favicon.ico?favicon.45db1c09.ico 200 in 260ms
 ✓ Compiled in 150ms
 ✓ Compiled in 123ms
 GET /packs/pokemon/new_pack_test 200 in 96ms
 GET /?collection=pokemon 200 in 400ms
 GET /favicon.ico?favicon.45db1c09.ico 200 in 499ms
 GET /favicon.ico?favicon.45db1c09.ico 200 in 310ms
 ✓ Compiled in 228ms
 ✓ Compiled in 157ms
 ✓ Compiled in 158ms
 ✓ Compiled in 130ms
 GET /?collection=pokemon 200 in 472ms
 GET /favicon.ico?favicon.45db1c09.ico 200 in 463ms
 GET /favicon.ico?favicon.45db1c09.ico 200 in 297ms
 ✓ Compiled in 172ms
 ✓ Compiled in 220ms
 GET /favicon.ico 200 in 376ms
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ✓ Compiled in 178ms
 GET /?collection=pokemon 200 in 540ms
 GET /favicon.ico?favicon.45db1c09.ico 200 in 266ms
 ✓ Compiled in 148ms
 ○ Compiling /marketplace ...
 ✓ Compiled /marketplace in 760ms
 GET /marketplace 200 in 843ms
 GET /marketplace 200 in 58ms
 GET / 200 in 143ms
 ✓ Compiled in 220ms
 ✓ Compiled in 135ms
 ✓ Compiled in 100ms
 ✓ Compiled in 138ms
 ✓ Compiled in 178ms
 ✓ Compiled in 155ms
 ✓ Compiled in 179ms
 ✓ Compiled in 235ms
 ✓ Compiled in 146ms
 ✓ Compiled in 178ms
 ✓ Compiled in 169ms
 ✓ Compiled / in 50ms
 GET /?collection=pokemon 200 in 385ms
 ✓ Compiled in 149ms
 GET /?collection=pokemon 200 in 543ms
 GET /favicon.ico?favicon.45db1c09.ico 200 in 270ms
 GET /favicon.ico?favicon.45db1c09.ico 200 in 292ms
 ✓ Compiled in 161ms
