    <!-- Edit Card Modal -->
    <div id="edit-card-modal" class="modal">
        <div class="modal-content">
            <span class="close-button" onclick="closeModal()">&times;</span>
            <h2>Edit Card</h2>
            <form id="edit-card-form" onsubmit="event.preventDefault(); updateCard(new FormData(this));">
                <input type="hidden" name="card-id">
                <input type="hidden" name="edit-collection-name">
                
                <div class="form-group">
                    <label for="card-name">Card Name:</label>
                    <input type="text" id="card-name" name="card-name" required>
                </div>
                
                <div class="form-group">
                    <label for="card-rarity">Rarity:</label>
                    <input type="text" id="card-rarity" name="card-rarity" required>
                </div>
                
                <div class="form-group">
                    <label for="card-points">Point Worth:</label>
                    <input type="number" id="card-points" name="card-points" required>
                </div>
                
                <div class="form-group">
                    <label for="card-quantity">Quantity:</label>
                    <input type="number" id="card-quantity" name="card-quantity" required>
                </div>
                
                <div class="form-group">
                    <label for="card-date">Date Added:</label>
                    <input type="text" id="card-date" name="card-date" required>
                </div>
                
                <div class="form-actions">
                    <button type="button" onclick="closeModal()">Cancel</button>
                    <button type="submit">Save Changes</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Add CSS for modal -->
    <style>
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }
        
        .modal-content {
            background-color: #fff;
            margin: 10% auto;
            padding: 20px;
            border-radius: 5px;
            width: 80%;
            max-width: 500px;
        }
        
        .close-button {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .close-button:hover {
            color: #000;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .form-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
        }
        
        .form-actions button {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .form-actions button[type="button"] {
            background-color: #f2f2f2;
        }
        
        .form-actions button[type="submit"] {
            background-color: #0066cc;
            color: white;
        }
    </style>
</body>
</html>
