body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f4f4f4;
    color: #333;
    line-height: 1.6;
}

header {
    background: #333;
    color: #fff;
    padding: 1rem 0;
    text-align: center;
}

header h1 {
    margin: 0;
}

nav {
    background: #444;
    padding: 0.5rem;
    text-align: center;
}

nav a {
    color: #fff;
    margin: 0 1rem;
    text-decoration: none;
    padding: 0.5rem 1rem;
}

nav a:hover {
    background: #555;
    border-radius: 5px;
}

main {
    padding: 1rem;
    max-width: 1200px;
    margin: auto;
    overflow: hidden;
}

#hero {
    background: #fff;
    padding: 2rem;
    text-align: center;
    border-radius: 8px;
    margin-bottom: 1rem;
}

#hero button {
    display: inline-block;
    background: #5cb85c;
    color: white;
    padding: 10px 20px;
    text-decoration: none;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1.1rem;
}

#hero button:hover {
    background: #4cae4c;
}

#featured-packs h3 {
    text-align: center;
    margin-bottom: 1rem;
}

.pack-container {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
}

.pack {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 1rem;
    margin: 0.5rem;
    width: calc(45% - 1rem);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    text-align: center;
}

footer {
    text-align: center;
    padding: 1rem;
    background: #333;
    color: #fff;
    margin-top: 2rem;
}

/* Basic responsive for smaller screens */
@media (max-width: 768px) {
    .pack-container {
        flex-direction: column;
        align-items: center;
    }
    .pack {
        width: calc(90% - 1rem);
    }
    nav a {
        display: block;
        margin-bottom: 0.5rem;
    }
}

/* Styles for displaying cards */
.cards-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    padding: 20px;
    justify-content: center;
}

.card-item {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    margin: 10px;
    width: 200px;
    text-align: center;
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.2s, box-shadow 0.2s;
    cursor: pointer;
}

.card-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.card-item img {
    width: 150px;
    height: 150px;
    object-fit: cover;
    border-radius: 4px;
    margin-bottom: 10px;
}

.card-item h4 {
    margin: 10px 0;
    color: #333;
}

.card-item p {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 0;
}

/* Rarity-based styling */
.rarity-common { border-color: #b0b0b0; }
.rarity-rare { border-color: #4a90e2; }
.rarity-epic { border-color: #9b59b6; }
.rarity-legendary { border-color: #f1c40f; }

/* Modal styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    position: relative;
    background-color: white;
    margin: 15% auto;
    padding: 30px;
    width: 90%;
    max-width: 500px;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    z-index: 1001;
}

.close {
    position: absolute;
    right: 20px;
    top: 10px;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    color: #666;
}

.close:hover {
    color: #333;
}

/* Form styles */
.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    color: #333;
    font-weight: 500;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.form-group input:focus,
.form-group select:focus {
    border-color: #4a90e2;
    outline: none;
    box-shadow: 0 0 0 2px rgba(74,144,226,0.2);
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

.btn-primary,
.btn-secondary {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s;
}

.btn-primary {
    background-color: #4a90e2;
    color: white;
}

.btn-primary:hover {
    background-color: #357abd;
}

.btn-secondary {
    background-color: #e0e0e0;
    color: #333;
}

.btn-secondary:hover {
    background-color: #d0d0d0;
} 