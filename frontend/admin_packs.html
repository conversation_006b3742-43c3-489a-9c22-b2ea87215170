<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Pack Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .pack-name-link {
            color: #007bff;
            cursor: pointer;
            text-decoration: none;
        }
        .pack-name-link:hover {
            text-decoration: underline;
        }
        .cards-list {
            max-height: 200px;
            overflow-y: auto;
            padding-left: 20px;
        }
        #alerts-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1050;
            width: 350px;
        }
    </style>
</head>
<body>
    <div class="container py-4">
        <div id="alerts-container"></div>
        
        <header class="mb-4">
            <h1>Pack Management</h1>
            <p class="text-muted">Manage your card packs, rarities, and card assignments</p>
            <hr>
        </header>
        
        <main>
            <section id="packs-list">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h2>All Packs</h2>
                    <button class="btn btn-success" onclick="location.href='admin_create_pack.html'">Create New Pack</button>
                </div>
                
                <div class="table-responsive">
                    <table class="table table-striped table-bordered">
                        <thead class="table-dark">
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Image</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="packs-table-body">
                            <!-- Pack rows will be inserted here by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </section>
        </main>
        
        <!-- Pack Details Modal -->
        <div class="modal fade" id="pack-details-modal" tabindex="-1" aria-labelledby="packDetailsModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="packDetailsModalLabel">Edit Pack</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form id="update-pack-form">
                            <div class="mb-3">
                                <label class="form-label">Pack ID:</label>
                                <div><strong id="pack-id-display"></strong></div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="pack-name-input" class="form-label">Pack Name:</label>
                                <input type="text" class="form-control" id="pack-name-input" name="name" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="pack-description-input" class="form-label">Description:</label>
                                <textarea class="form-control" id="pack-description-input" name="description" rows="2"></textarea>
                            </div>
                            
                            <h4 class="mt-4">Rarities</h4>
                            <div id="rarities-container">
                                <!-- Rarity sections will be inserted here -->
                            </div>
                            
                            <!-- Hidden field to store cards to delete -->
                            <input type="hidden" id="cards-to-delete" name="cards-to-delete" value="{}">
                            
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                <button type="submit" class="btn btn-primary">Save Changes</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/admin.js"></script>
</body>
</html>
