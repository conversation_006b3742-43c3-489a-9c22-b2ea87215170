from fastapi import <PERSON><PERSON>P<PERSON>x<PERSON>, UploadFile
import os
from typing import Dict # Keep for potential future use if DB_STORED_CARDS or similar is ever reinstated for other purposes
from uuid import uuid4
# from datetime import timedelta # No longer needed here
# import google.auth.transport.requests # No longer needed here
# from google.auth import compute_engine # No longer needed here
# from google.oauth2 import service_account # No longer needed here

from models.schemas import StoredCardInfo, PaginationInfo, AppliedFilters, CardListResponse, CollectionMetadata, Base64UploadCardRequest
from config import get_logger, settings, get_storage_client, get_firestore_client, get_typesense_client, get_typesense_collection_name

# Hardcoded file size limit: 2.5MB
MAX_FILE_SIZE_BYTES = int(2.5 * 1024 * 1024)  # 2.5MB in bytes
from utils.storage_utils import upload_image, get_public_url, parse_base64_image # Import the R2 utility functions
from datetime import datetime

# New imports
import math
from google.cloud import firestore # For firestore.Query constants
from pydantic import BaseModel
from typing import List, Optional, Any
import base64
import io
import re

logger = get_logger(__name__)

# --- Pydantic models for API response ---
# class PaginationInfo(BaseModel):
#     total_items: int
#     total_pages: int
#     current_page: int
#     per_page: int

# class AppliedFilters(BaseModel):
#     sort_by: str
#     sort_order: str
#     # In the future, could add:
#     # available_sort_options: List[str] = ["point_worth", "card_name", "date_got_in_stock", "quantity"]
#     # search_query: Optional[str] = None

# class CardListResponse(BaseModel):
#     cards: List[StoredCardInfo]
#     pagination: PaginationInfo
#     filters: AppliedFilters

# --- Real Google Cloud Storage and Firestore interactions ---

async def upload_image_to_gcs(
    file: UploadFile, 
    destination_blob_name: str
) -> str:
    """
    Uploads an image to R2 Storage.
    Returns the public R2 URL of the uploaded file.
    """
    logger.info(f"Attempting to upload {file.filename} to R2 as {destination_blob_name}")

    try:
        contents = await file.read() # Read the contents of the UploadFile
        
        # Upload to R2 and get public URL
        r2_url = await upload_image(
            image_data=contents,
            object_key=destination_blob_name,
            bucket_type='card',  # Default to card bucket for this function
            content_type=file.content_type
        )
        
        logger.info(f"File {file.filename} uploaded to R2: {r2_url}")
        return r2_url 
    except Exception as e:
        logger.error(f"Failed to upload file {file.filename} to R2: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Could not upload image: {str(e)}")
    finally:
        await file.close() # Ensure the file is closed


async def save_card_information(card_info: StoredCardInfo, collection_name: str | None = None) -> StoredCardInfo:
    """
    Saves the card information to Firestore, using card_info.card_name as the document ID.
    Checks if a document with this ID (card_name) already exists before saving.
    Returns the saved card information.
    If collection_name is provided, it saves to that collection, otherwise defaults
    to settings.firestore_collection_cards.

    If collection_name is provided, it will look up the metadata for that collection
    in the metadata collection and use the firestoreCollection for the Firestore collection.
    """
    firestore_client = get_firestore_client()
    # The collection_name will be determined by the settings or passed if this function is called from elsewhere with a specific collection.
    # For process_new_card_submission, it uses settings.firestore_collection_cards.
    # This function itself should not default the collection_name, but expect it to be resolved before or passed in.
    if not collection_name:
        effective_collection_name = settings.firestore_collection_cards
    else:
        # Try to get the metadata for the collection
        try:
            metadata = await get_collection_metadata(collection_name)
            effective_collection_name = metadata.firestoreCollection
            logger.info(f"Using metadata for collection '{collection_name}': firestoreCollection='{effective_collection_name}'")
        except HTTPException as e:
            if e.status_code == 404:
                # If metadata not found, use the provided collection_name as is
                effective_collection_name = collection_name
                logger.warning(f"Metadata for collection '{collection_name}' not found. Using provided collection_name as is.")
            else:
                # For other HTTP exceptions, re-raise
                raise e

    # Document ID is now the card_name from the StoredCardInfo instance
    # The StoredCardInfo instance's 'id' field should already be populated with card_name by the caller (e.g., process_new_card_submission)
    # Generate a unique document ID instead of using card name
    # This avoids issues with special characters like "/" in card names
    doc_id = str(uuid4())
    
    # Keep the card_name in the id field for backward compatibility with searches
    # But use the generated doc_id for the actual document
    doc_ref = firestore_client.collection(effective_collection_name).document(doc_id)

    try:
        # No longer checking for duplicate card names since we use UUID document IDs
        # This allows multiple cards with the same name but different conditions
        
        # Prepare data for Firestore: StoredCardInfo model fields.
        # Update the 'id' field to use the generated doc_id instead of card_name
        card_data_to_save = card_info.model_dump()
        card_data_to_save['id'] = doc_id  # Use the generated ID

        await doc_ref.set(card_data_to_save)

        logger.info(f"Card information for '{card_info.card_name}' (ID: {doc_id}) saved to Firestore collection '{effective_collection_name}'.")
        # Update the card_info object with the generated ID before returning
        card_info.id = doc_id
        return card_info # Return the card_info object with the generated ID
    except HTTPException as http_exc: # Re-raise HTTPException
        raise http_exc
    except Exception as e:
        logger.error(f"Failed to save card information for '{card_info.card_name}' (ID: {doc_id}) to Firestore: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Could not save card information: {str(e)}")


async def process_new_card_submission(
    image_file: UploadFile,
    card_name: str,
    rarity: str,
    point_worth: int,
    collection_metadata_id: str,
    quantity: int = 0,
    condition: str = "new"
) -> StoredCardInfo:
    """
    Orchestrates uploading the image to GCS and then saving the card data to Firestore.
    The card's name will be used as its document ID. Checks for pre-existing card name.
    It gets the collection metadata using collection_metadata_id and uses
    the firestoreCollection and storagePrefix from it.

    It will look up the metadata for that collection in the metadata collection
    and use the storagePrefix for the image path and the firestoreCollection
    for the Firestore collection.
    """
    if not image_file.filename:
        raise HTTPException(status_code=400, detail="Image file name is missing.")

    if not card_name or not card_name.strip():
        raise HTTPException(status_code=400, detail="Card name cannot be empty.")

    if not collection_metadata_id or not collection_metadata_id.strip():
        raise HTTPException(status_code=400, detail="Collection metadata ID cannot be empty.")
    
    # Validate rarity is within range 0-7
    try:
        rarity_int = int(rarity)
        if rarity_int < 0 or rarity_int > 7:
            raise HTTPException(status_code=400, detail="Rarity must be between 0 and 7")
    except ValueError:
        raise HTTPException(status_code=400, detail="Rarity must be a valid integer")

    # Sanitize card_name slightly for use as document ID (e.g., replace slashes if necessary)
    # For now, we'll assume card_name is valid or Firestore handles it.
    # A more robust solution might involve slugifying or more aggressive sanitization.
    # Simple example: doc_id_card_name = card_name.replace("/", "_")
    # However, StoredCardInfo.id should still reflect the original card_name for data integrity.
    # Let's proceed with using card_name directly as ID, assuming it's valid.

    try:
        # Look up the metadata for the collection
        effective_collection_name = settings.firestore_collection_cards  # Default collection name
        storage_prefix = "cards"  # Default storage prefix
        logger.info(f"Default storage_prefix set to: '{storage_prefix}'")

        logger.info(f"Collection metadata ID received: '{collection_metadata_id}'")

        try:
            # Try to get the metadata for the collection
            logger.info(f"Looking up metadata for collection: '{collection_metadata_id}'")
            metadata = await get_collection_metadata(collection_metadata_id)

            effective_collection_name = metadata.firestoreCollection
            storage_prefix = metadata.storagePrefix
            logger.info(f"Using metadata for collection '{collection_metadata_id}': firestoreCollection='{effective_collection_name}', storagePrefix='{storage_prefix}'")
        except HTTPException as e:
            if e.status_code == 404:
                # If metadata not found, use the provided collection_metadata_id as is
                logger.warning(f"Metadata for collection '{collection_metadata_id}' not found. Using provided collection_metadata_id as is.")
                effective_collection_name = collection_metadata_id
                # Keep the default storage_prefix as "cards"
                logger.warning(f"Keeping default storage_prefix as '{storage_prefix}'")
            else:
                # For other HTTP exceptions, re-raise
                logger.error(f"Error retrieving collection metadata: {e.detail}")
                raise e

        file_extension = os.path.splitext(image_file.filename)[1]
        # Use the storage_prefix from the collection metadata for the path
        unique_filename = f"{storage_prefix}/{str(uuid4())}{file_extension}" # Image filename remains unique with UUID
        logger.info(f"Generated unique filename: {unique_filename} with storage_prefix: {storage_prefix}")

        image_url = await upload_image_to_gcs(
            file=image_file,
            destination_blob_name=unique_filename
        )

        # No longer checking for duplicate card names since we use UUID document IDs
        # This allows multiple cards with the same name but different conditions
        
        # R2 URLs are public, no need to sign
        signed_image_url = image_url

        # Format the date as "Month Day, Year"
        current_date = datetime.now().strftime("%b %d, %Y")
        # Calculate Unix timestamp
        current_unix_timestamp = int(datetime.now().timestamp())

        card_data = StoredCardInfo(
            id="",  # Will be set by save_card_information with a generated UUID
            card_name=card_name,
            rarity=rarity,
            point_worth=point_worth,
            date_got_in_stock=current_date,  # Use today's date automatically
            date_got_in_stock_unix=current_unix_timestamp,  # Unix timestamp
            image_url=signed_image_url,  # Use the signed URL if available
            quantity=quantity,
            condition=condition
        )

        # save_card_information will now use card_data.card_name for doc ID and check existence
        saved_card_info = await save_card_information(card_data, effective_collection_name)
        return saved_card_info

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error processing new card submission: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"An unexpected error occurred: {str(e)}")

# For backward compatibility
async def process_new_card_submission_with_collection_name(
    image_file: UploadFile,
    card_name: str,
    rarity: str,
    point_worth: int,
    collection_name: str,
    date_got_in_stock: str = None,  # Kept for backward compatibility but not used
    quantity: int = 0,
    condition: str = "new"
) -> StoredCardInfo:
    """
    Backward compatibility wrapper for process_new_card_submission.
    Uses collection_name as collection_metadata_id.
    The date_got_in_stock parameter is kept for backward compatibility but not used.
    The collection_name parameter is required.
    """
    # Validate rarity is within range 0-7
    try:
        rarity_int = int(rarity)
        if rarity_int < 0 or rarity_int > 7:
            raise HTTPException(status_code=400, detail="Rarity must be between 0 and 7")
    except ValueError:
        raise HTTPException(status_code=400, detail="Rarity must be a valid integer")
    
    return await process_new_card_submission(
        image_file=image_file,
        card_name=card_name,
        rarity=rarity,
        point_worth=point_worth,
        quantity=quantity,
        condition=condition,
        collection_metadata_id=collection_name
    )

def decode_base64_image(base64_string: str) -> tuple[bytes, str]:
    """
    Decodes a base64 encoded image string and returns the binary data and content type.
    
    Args:
        base64_string: Base64 encoded image string, with or without data URL prefix
        
    Returns:
        Tuple of (binary_data, content_type)
        
    Raises:
        ValueError: If the base64 string is invalid
    """
    try:
        # Check if it has data URL prefix
        if base64_string.startswith('data:'):
            # Extract content type and base64 data
            match = re.match(r'data:([^;]+);base64,(.+)', base64_string)
            if not match:
                raise ValueError("Invalid data URL format")
            content_type = match.group(1)
            base64_data = match.group(2)
        else:
            # No data URL prefix, assume it's raw base64
            content_type = 'image/jpeg'  # Default content type
            base64_data = base64_string
        
        # Decode base64 to binary
        binary_data = base64.b64decode(base64_data)
        
        # Try to determine content type from binary data if not provided
        if content_type == 'image/jpeg':
            # Check magic bytes for common image formats
            if binary_data.startswith(b'\x89PNG'):
                content_type = 'image/png'
            elif binary_data.startswith(b'GIF87a') or binary_data.startswith(b'GIF89a'):
                content_type = 'image/gif'
            elif binary_data.startswith(b'RIFF') and binary_data[8:12] == b'WEBP':
                content_type = 'image/webp'
                
        return binary_data, content_type
        
    except Exception as e:
        logger.error(f"Failed to decode base64 image: {e}")
        raise ValueError(f"Invalid base64 image data: {str(e)}")

async def upload_binary_to_gcs(
    binary_data: bytes,
    destination_blob_name: str,
    content_type: str = 'application/octet-stream'
) -> str:
    """
    Uploads binary data to R2 storage.
    Returns the public R2 URL of the uploaded file.
    """
    logger.info(f"Attempting to upload binary data to R2 as {destination_blob_name}")

    try:
        # Determine bucket type based on the destination blob name
        if destination_blob_name.startswith('cards/'):
            bucket_type = 'card'
        elif destination_blob_name.startswith('packs/'):
            bucket_type = 'pack'
        elif destination_blob_name.startswith('avatars/'):
            bucket_type = 'avatar'
        elif destination_blob_name.startswith('achievements/') or destination_blob_name.startswith('emblems/'):
            bucket_type = 'achievement'
        else:
            # Default to card bucket
            bucket_type = 'card'
            
        # Upload to R2
        r2_url = await upload_image(
            image_data=binary_data,
            object_key=destination_blob_name,
            bucket_type=bucket_type,
            content_type=content_type
        )
        
        logger.info(f"Binary data uploaded to R2: {r2_url}")
        return r2_url
    except Exception as e:
        logger.error(f"Failed to upload binary data to R2: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Could not upload image: {str(e)}")

async def process_base64_card_submission(
    request: Base64UploadCardRequest
) -> StoredCardInfo:
    """
    Process a new card submission with base64 encoded image.
    """
    # Validate rarity is within range 0-7
    try:
        rarity_int = int(request.rarity)
        if rarity_int < 0 or rarity_int > 7:
            raise HTTPException(status_code=400, detail="Rarity must be between 0 and 7")
    except ValueError:
        raise HTTPException(status_code=400, detail="Rarity must be a valid integer")
    
    # Validate base64 image size before processing
    base64_data = request.image_base64
    if base64_data.startswith('data:'):
        # Extract just the base64 part after the comma
        comma_index = base64_data.find(',')
        if comma_index != -1:
            base64_data = base64_data[comma_index + 1:]
    
    # Calculate approximate decoded size
    base64_length = len(base64_data)
    padding = base64_data.count('=')
    estimated_file_size = (base64_length * 3) // 4 - padding
    
    MAX_FILE_SIZE = MAX_FILE_SIZE_BYTES
    if estimated_file_size > MAX_FILE_SIZE:
        raise HTTPException(
            status_code=413, 
            detail=f"File too large. Maximum size allowed is {MAX_FILE_SIZE / (1024*1024):.1f}MB, but received approximately {estimated_file_size / (1024*1024):.1f}MB."
        )
    
    logger.info(f"Base64 file size validation passed in service: estimated {estimated_file_size} bytes ({estimated_file_size / (1024*1024):.2f}MB)")
    
    try:
        # Decode base64 image
        binary_data, content_type = decode_base64_image(request.image_base64)
        
        # Get file extension from content type
        extension_map = {
            'image/jpeg': '.jpg',
            'image/jpg': '.jpg',
            'image/png': '.png',
            'image/gif': '.gif',
            'image/webp': '.webp'
        }
        file_extension = extension_map.get(content_type, '.jpg')
        
        # Get collection metadata
        effective_collection_name = settings.firestore_collection_cards
        storage_prefix = "cards"
        
        try:
            metadata = await get_collection_metadata(request.collection_metadata_id)
            effective_collection_name = metadata.firestoreCollection
            storage_prefix = metadata.storagePrefix
            logger.info(f"Using metadata for collection '{request.collection_metadata_id}'")
        except HTTPException as e:
            if e.status_code == 404:
                logger.warning(f"Metadata not found, using collection_metadata_id as collection name")
                effective_collection_name = request.collection_metadata_id
            else:
                raise e
        
        # Generate unique filename
        unique_filename = f"{storage_prefix}/{str(uuid4())}{file_extension}"
        logger.info(f"Generated unique filename: {unique_filename}")
        
        # Upload to GCS
        image_url = await upload_binary_to_gcs(
            binary_data=binary_data,
            destination_blob_name=unique_filename,
            content_type=content_type
        )
        
        # No longer checking for duplicate card names since we use UUID document IDs
        # This allows multiple cards with the same name but different conditions
        
        # R2 URLs are public, no need to sign
        signed_image_url = image_url
        
        # Create card data
        current_date = datetime.now().strftime("%b %d, %Y")
        # Calculate Unix timestamp
        current_unix_timestamp = int(datetime.now().timestamp())
        
        card_data = StoredCardInfo(
            id="",  # Will be set by save_card_information with a generated UUID
            card_name=request.card_name,
            rarity=request.rarity,
            point_worth=request.point_worth,
            date_got_in_stock=current_date,
            date_got_in_stock_unix=current_unix_timestamp,  # Unix timestamp
            image_url=signed_image_url,
            quantity=request.quantity,
            condition=request.condition
        )
        
        # Save to Firestore
        saved_card_info = await save_card_information(card_data, effective_collection_name)
        return saved_card_info
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error processing base64 card submission: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"An unexpected error occurred: {str(e)}")

async def update_image_url_in_database(card_id: str, signed_url: str, collection_name: str) -> None:
    """
    Updates the image_url field in Firestore with a new signed URL.
    This helps avoid regenerating URLs unnecessarily.
    """
    try:
        firestore_client = get_firestore_client()
        doc_ref = firestore_client.collection(collection_name).document(card_id)
        await doc_ref.update({'image_url': signed_url})
        logger.debug(f"Successfully updated image_url in Firestore for card {card_id} in collection {collection_name}")
    except Exception as e:
        logger.warning(f"Failed to update image_url for card {card_id}: {e}")
        # Don't raise the exception as this is not critical for the main operation

async def get_all_stored_cards(
    collection_name: str | None = None,
    page: int = 1,
    per_page: int = 10,
    sort_by: str = "point_worth",
    sort_order: str = "desc",
    search_query: str | None = None,
    card_id: str | None = None,
    rarity_min: int | None = None,
    rarity_max: int | None = None,
    point_worth_min: int | None = None,
    point_worth_max: int | None = None
) -> CardListResponse:
    """
    Retrieves a paginated and sorted list of card information using Typesense.
    Allows searching by card name or card_id.
    Allows filtering by rarity and point_worth ranges.
    Generates signed URLs for images.
    If collection_name is provided, it fetches from that collection, otherwise defaults
    to settings.firestore_collection_cards.

    If collection_name is provided, it will look up the metadata for that collection
    in the metadata collection and use the firestoreCollection for the Firestore collection.

    Supports sorting by point_worth, rarity, quantity, and date_got_in_stock.
    When searching, results are sorted by relevance first, then by the specified sort field.
    
    Args:
        rarity_min: Filter for minimum rarity value (inclusive, 0-7)
        rarity_max: Filter for maximum rarity value (inclusive, 0-7)
        point_worth_min: Filter for minimum point_worth value (inclusive)
        point_worth_max: Filter for maximum point_worth value (inclusive)
    
    Note: For exact matching, set min and max to the same value.
    """
    if not collection_name:
        effective_collection_name = settings.firestore_collection_cards
        logger.info(f"collection_name not provided, defaulting to '{effective_collection_name}'.")
    else:
        # Try to get the metadata for the collection
        try:
            metadata = await get_collection_metadata(collection_name)
            effective_collection_name = metadata.firestoreCollection
            logger.info(f"Using metadata for collection '{collection_name}': firestoreCollection='{effective_collection_name}'")
        except HTTPException as e:
            if e.status_code == 404:
                # If metadata not found, use the provided collection_name as is
                effective_collection_name = collection_name
                logger.warning(f"Metadata for collection '{collection_name}' not found. Using provided collection_name as is.")
            else:
                # For other HTTP exceptions, re-raise
                raise e

    log_params = {
        "page": page, "per_page": per_page, "sort_by": sort_by, 
        "sort_order": sort_order, "collection": effective_collection_name,
        "search_query": search_query, "card_id": card_id,
        "rarity_min": rarity_min, "rarity_max": rarity_max,
        "point_worth_min": point_worth_min, "point_worth_max": point_worth_max
    }
    logger.info(f"Fetching cards with parameters: {log_params}")

    try:
        # Get Typesense client and collection name
        client = get_typesense_client()
        typesense_collection = get_typesense_collection_name(collection_name)
        
        # Build search parameters for Typesense
        search_params = {
            'q': search_query or '*',  # Use '*' for empty search
            'query_by': 'card_name',  # Search by card name
            'page': page,
            'per_page': per_page,
            'num_typos': 2,  # Allow up to 2 typos for better user experience
            'infix': 'always',  # Enable infix (substring) search
            'prefix': 'true',  # Enable prefix matching for card_name
            'highlight_full_fields': 'card_name',  # Highlight matching parts
        }
        
        # Add filters
        filter_by = []
        
        # Add filter for card_id if provided
        if card_id:
            filter_by.append(f'id:={card_id}')
        
        # Add rarity filters
        if rarity_min is not None or rarity_max is not None:
            if rarity_min is not None and rarity_max is not None:
                filter_by.append(f'rarity:[{rarity_min}..{rarity_max}]')
            elif rarity_min is not None:
                filter_by.append(f'rarity:>={rarity_min}')
            else:  # rarity_max is not None
                filter_by.append(f'rarity:<={rarity_max}')
        
        # Add point_worth filters
        if point_worth_min is not None or point_worth_max is not None:
            if point_worth_min is not None and point_worth_max is not None:
                filter_by.append(f'point_worth:[{point_worth_min}..{point_worth_max}]')
            elif point_worth_min is not None:
                filter_by.append(f'point_worth:>={point_worth_min}')
            else:  # point_worth_max is not None
                filter_by.append(f'point_worth:<={point_worth_max}')
        
        if filter_by:
            search_params['filter_by'] = ' && '.join(filter_by)
        
        # Handle sorting
        # If there's a search query, prioritize relevance sorting
        if search_query and search_query.strip():
            # First sort by relevance, then by the requested field
            sort_field = sort_by
            if sort_by == 'date_got_in_stock':
                # Use Unix timestamp field for date sorting
                sort_field = 'date_got_in_stock_unix'
            
            sort_direction = 'desc' if sort_order.lower() == 'desc' else 'asc'
            # Combine relevance sorting with requested sorting
            search_params['sort_by'] = f'_text_match:desc,{sort_field}:{sort_direction}'
        else:
            # No search query, just use the requested sorting
            sort_field = sort_by
            if sort_by == 'date_got_in_stock':
                # Use Unix timestamp field for date sorting
                sort_field = 'date_got_in_stock_unix'
            
            # Typesense sort format
            sort_direction = 'desc' if sort_order.lower() == 'desc' else 'asc'
            search_params['sort_by'] = f'{sort_field}:{sort_direction}'
        
        # Log the search params for debugging
        logger.info(f"Typesense search params: {search_params}")
        logger.info(f"Searching in Typesense collection: {typesense_collection}")
        
        # Execute search
        try:
            res = client.collections[typesense_collection].documents.search(search_params)
        except Exception as e:
            logger.error(f"Typesense search error: {e}")
            raise

        logger.info(f"Typesense search returned {res['found']} results, showing {len(res['hits'])} hits")
        
        # Log first few results for debugging if search query was provided
        if search_query and res['hits']:
            logger.debug(f"First result: {res['hits'][0]['document'].get('card_name', 'N/A')}")

        # Process results
        cards_list = []
        for hit in res['hits']:
            try:
                hit_data = hit['document']
                
                # Typesense documents should have 'id' field
                if 'id' not in hit_data:
                    logger.warning(f"Skipping hit without id: {hit_data}")
                    continue

                # R2 URLs are public, no need to sign them

                # Check for required fields
                required_fields = ["card_name", "rarity", "point_worth"]
                missing_fields = [f for f in required_fields if f not in hit_data]
                if missing_fields:
                    logger.warning(f"Skipping due to missing fields {missing_fields}: {hit_data}")
                    continue

                cards_list.append(StoredCardInfo(**hit_data))

            except Exception as e:
                # Try to get objectID safely from hit object
                try:
                    object_id = getattr(hit, 'objectID', 'unknown')
                except:
                    object_id = 'unknown'
                logger.warning(f"Failed to parse hit {object_id}: {e}")
                continue

        # Create pagination info
        total_items = res['found']
        total_pages = math.ceil(total_items / per_page) if per_page > 0 else 1
        
        pagination_info = PaginationInfo(
            total_items=total_items,
            total_pages=total_pages,
            current_page=page,
            per_page=per_page
        )

        # Create filters info
        applied_filters_info = AppliedFilters(
            sort_by=sort_by,
            sort_order=sort_order,
            search_query=search_query
        )

        logger.info(f"Successfully fetched {len(cards_list)} cards using Typesense. Total items: {total_items}.")
        return CardListResponse(cards=cards_list, pagination=pagination_info, filters=applied_filters_info)

    except Exception as e:
        logger.error(f"Failed to fetch cards using Typesense: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Could not retrieve cards: {str(e)}")


async def update_card_quantity(document_id: str, quantity_change: int, collection_name: str | None = None) -> StoredCardInfo:
    """
    Updates the quantity of a specific card in Firestore.
    If collection_name is provided, it updates in that collection, otherwise defaults
    to settings.firestore_collection_cards.

    If collection_name is provided, it will look up the metadata for that collection
    in the metadata collection and use the firestoreCollection for the Firestore collection.
    """
    firestore_client = get_firestore_client()

    if not collection_name:
        effective_collection_name = settings.firestore_collection_cards
    else:
        # Try to get the metadata for the collection
        try:
            metadata = await get_collection_metadata(collection_name)
            effective_collection_name = metadata.firestoreCollection
            logger.info(f"Using metadata for collection '{collection_name}': firestoreCollection='{effective_collection_name}'")
        except HTTPException as e:
            if e.status_code == 404:
                # If metadata not found, use the provided collection_name as is
                effective_collection_name = collection_name
                logger.warning(f"Metadata for collection '{collection_name}' not found. Using provided collection_name as is.")
            else:
                # For other HTTP exceptions, re-raise
                raise e

    doc_ref = firestore_client.collection(effective_collection_name).document(document_id)

    try:
        # Get the document reference
        doc = await doc_ref.get()

        if not doc.exists:
            raise HTTPException(status_code=404, detail=f"Card with ID {document_id} not found")

        # Get current card data
        card_data = doc.to_dict()
        current_quantity = card_data.get('quantity', 0)

        # Calculate new quantity, ensure it doesn't go below 0
        new_quantity = max(0, current_quantity + quantity_change)

        # Update only the quantity field
        await doc_ref.update({'quantity': new_quantity})

        # Update the quantity in the card data and add the document ID
        card_data['quantity'] = new_quantity
        card_data['id'] = document_id

        # R2 URLs are public, no need to sign them

        return StoredCardInfo(**card_data)

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error updating quantity for card {document_id} in '{effective_collection_name}': {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Could not update card quantity in collection '{effective_collection_name}'.")

async def update_card_information(document_id: str, update_data: dict, collection_name: str | None = None) -> StoredCardInfo:
    """
    Updates specific fields of a card in Firestore.
    If collection_name is provided, it updates in that collection, otherwise defaults
    to settings.firestore_collection_cards.

    If collection_name is provided, it will look up the metadata for that collection
    in the metadata collection and use the firestoreCollection for the Firestore collection.
    """
    firestore_client = get_firestore_client()

    if not collection_name:
        effective_collection_name = settings.firestore_collection_cards
    else:
        # Try to get the metadata for the collection
        try:
            metadata = await get_collection_metadata(collection_name)
            effective_collection_name = metadata.firestoreCollection
            logger.info(f"Using metadata for collection '{collection_name}': firestoreCollection='{effective_collection_name}'")
        except HTTPException as e:
            if e.status_code == 404:
                # If metadata not found, use the provided collection_name as is
                effective_collection_name = collection_name
                logger.warning(f"Metadata for collection '{collection_name}' not found. Using provided collection_name as is.")
            else:
                # For other HTTP exceptions, re-raise
                raise e

    doc_ref = firestore_client.collection(effective_collection_name).document(document_id)
    logger.info(f"Attempting to update card {document_id} in collection '{effective_collection_name}' with data: {update_data}")

    try:
        # Get the document reference
        doc = await doc_ref.get()

        if not doc.exists:
            raise HTTPException(status_code=404, detail=f"Card with ID {document_id} not found")

        # Get current card data
        card_data = doc.to_dict()

        # Update only the fields that are provided
        for field, value in update_data.items():
            if value is not None:  # Only update if value is provided
                card_data[field] = value
        
        # If date_got_in_stock is being updated, also update the Unix timestamp
        if 'date_got_in_stock' in update_data and update_data['date_got_in_stock'] is not None:
            try:
                # Parse the date string and convert to Unix timestamp
                date_obj = datetime.strptime(update_data['date_got_in_stock'], "%b %d, %Y")
                update_data['date_got_in_stock_unix'] = int(date_obj.timestamp())
                card_data['date_got_in_stock_unix'] = update_data['date_got_in_stock_unix']
            except ValueError:
                # If date parsing fails, use current timestamp
                update_data['date_got_in_stock_unix'] = int(datetime.now().timestamp())
                card_data['date_got_in_stock_unix'] = update_data['date_got_in_stock_unix']

        # Update the document in Firestore
        await doc_ref.update(update_data)

        # Also update the card in all packs that contain it
        await _update_card_in_all_packs(document_id, effective_collection_name, update_data)

        # If we used ArrayUnion or other transforms, we need to re-fetch the document
        # to get the actual updated values
        needs_refetch = False
        for value in update_data.values():
            if hasattr(value, '__class__') and value.__class__.__name__ in ['ArrayUnion', 'ArrayRemove', 'Increment']:
                needs_refetch = True
                break
        
        if needs_refetch:
            # Re-fetch the document to get the updated data
            doc = await doc_ref.get()
            card_data = doc.to_dict()
        
        # Add the document ID back to the data
        card_data['id'] = document_id

        # R2 URLs are public, no need to sign them

        return StoredCardInfo(**card_data)

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error updating card {document_id} in '{effective_collection_name}': {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Could not update card information in '{effective_collection_name}'.")

async def update_card_image(document_id: str, image_base64: str, collection_name: str | None = None) -> str:
    """
    Updates the image for an existing card by uploading a new image to GCS.
    
    Args:
        document_id: The ID of the card to update
        image_base64: Base64 encoded image data
        collection_name: The collection name where the card is stored
        
    Returns:
        str: The signed URL of the newly uploaded image
        
    Raises:
        HTTPException: If card not found or upload fails
    """
    firestore_client = get_firestore_client()
    
    # Determine the effective collection name
    if not collection_name:
        effective_collection_name = settings.firestore_collection_cards
        storage_prefix = "cards"
    else:
        # Try to get the metadata for the collection
        try:
            metadata = await get_collection_metadata(collection_name)
            effective_collection_name = metadata.firestoreCollection
            storage_prefix = metadata.storagePrefix
            logger.info(f"Using metadata for collection '{collection_name}': firestoreCollection='{effective_collection_name}', storagePrefix='{storage_prefix}'")
        except HTTPException as e:
            if e.status_code == 404:
                # If metadata not found, use the provided collection_name as is
                effective_collection_name = collection_name
                storage_prefix = "cards"  # Default storage prefix
                logger.warning(f"Metadata for collection '{collection_name}' not found. Using provided collection_name as is.")
            else:
                # For other HTTP exceptions, re-raise
                raise e
    
    # Get the card document to verify it exists
    doc_ref = firestore_client.collection(effective_collection_name).document(document_id)
    doc = await doc_ref.get()
    
    if not doc.exists:
        raise HTTPException(status_code=404, detail=f"Card with ID {document_id} not found")
    
    card_data = doc.to_dict()
    
    try:
        # Decode base64 image
        binary_data, content_type = decode_base64_image(image_base64)
        
        # Get file extension from content type
        extension_map = {
            'image/jpeg': '.jpg',
            'image/jpg': '.jpg',
            'image/png': '.png',
            'image/gif': '.gif',
            'image/webp': '.webp'
        }
        file_extension = extension_map.get(content_type, '.jpg')
        
        # Generate unique filename with storage prefix
        unique_filename = f"{storage_prefix}/{str(uuid4())}{file_extension}"
        logger.info(f"Updating image for card {document_id} with new filename: {unique_filename}")
        
        # Upload to GCS
        image_url = await upload_binary_to_gcs(
            binary_data=binary_data,
            destination_blob_name=unique_filename,
            content_type=content_type
        )
        
        # R2 URLs are public, no need to sign
        signed_image_url = image_url
        
        # TODO: Consider deleting the old image from GCS if needed
        # This would require parsing the old image_url to get the blob name
        
        return signed_image_url
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error updating image for card {document_id}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to update card image: {str(e)}")

async def clean_fusion_references(document_id: str, collection_name: str | None = None, fusion_id_to_remove: str | None = None) -> None:
    """
    Cleans up fusion references when a card is deleted or when a specific fusion recipe is deleted.
    If the card has a used_in_fusion field, it deletes all fusion recipes that use this card.
    If fusion_id_to_remove is provided, it only removes that specific fusion from the card's used_in_fusion array.

    Args:
        document_id: The ID of the card being deleted or cleaned
        collection_name: The collection name where the card is stored. If provided, it will look up
                        the metadata for that collection to get the actual Firestore collection name.
        fusion_id_to_remove: If provided, only this specific fusion ID will be removed from the card's used_in_fusion array.
    """
    firestore_client = get_firestore_client()

    # Determine the effective collection name
    if not collection_name:
        effective_collection_name = settings.firestore_collection_cards
    else:
        # Try to get the metadata for the collection
        try:
            metadata = await get_collection_metadata(collection_name)
            effective_collection_name = metadata.firestoreCollection
            logger.info(f"Using metadata for collection '{collection_name}': firestoreCollection='{effective_collection_name}'")
        except HTTPException as e:
            if e.status_code == 404:
                # If metadata not found, use the provided collection_name as is
                effective_collection_name = collection_name
                logger.warning(f"Metadata for collection '{collection_name}' not found. Using provided collection_name as is.")
            else:
                # For other HTTP exceptions, re-raise
                raise e

    try:
        # Get the card document to check if it has used_in_fusion field
        doc_ref = firestore_client.collection(effective_collection_name).document(document_id)
        doc = await doc_ref.get()

        if not doc.exists:
            logger.warning(f"Card {document_id} not found in collection '{effective_collection_name}' when cleaning fusion references.")
            return

        card_data = doc.to_dict()


        # Check if the card has used_in_fusion field
        if 'used_in_fusion' in card_data and card_data['used_in_fusion']:
            logger.info(f"Card {document_id} has fusion references. Cleaning up...")

            # If fusion_id_to_remove is provided, only remove that specific fusion
            if fusion_id_to_remove:
                # Filter out the fusion with the specified fusion_id
                updated_fusions = [
                    fusion for fusion in card_data['used_in_fusion'] 
                    if fusion.get('fusion_id') != fusion_id_to_remove and fusion.get('result_card_id') != fusion_id_to_remove
                ]

                # Update the card with the filtered array
                await doc_ref.update({
                    'used_in_fusion': updated_fusions
                })

                logger.info(f"Removed fusion information from card '{document_id}' (with collection_id '{effective_collection_name}') for fusion ID '{fusion_id_to_remove}'")
            else:
                # Iterate through all fusion recipes referenced in used_in_fusion
                for fusion_info in card_data['used_in_fusion']:
                    result_card_id = fusion_info.get('result_card_id')

                    if result_card_id:
                        try:
                            # Find and delete the fusion recipe
                            # First, we need to find which pack collection this recipe belongs to
                            fusion_recipes_ref = firestore_client.collection('fusion_recipes')
                            collections_stream = fusion_recipes_ref.stream()

                            fusion_doc = None
                            recipe_data = None
                            fusion_doc_ref = None

                            # Iterate through all pack collections
                            async for collection_doc in collections_stream:
                                pack_collection_id = collection_doc.id
                                # Get all packs in this collection
                                packs_ref = fusion_recipes_ref.document(pack_collection_id).collection(pack_collection_id)
                                packs_stream = packs_ref.stream()

                                # For each pack, check if it contains the recipe
                                async for pack_doc in packs_stream:
                                    pack_id = pack_doc.id

                                    # Check if the cards collection has the result_card_id
                                    card_ref = packs_ref.document(pack_id).collection('cards').document(result_card_id)
                                    card_doc = await card_ref.get()

                                    if card_doc.exists:
                                        fusion_doc = card_doc
                                        recipe_data = card_doc.to_dict()
                                        fusion_doc_ref = card_ref
                                        break

                                if fusion_doc:
                                    break

                            if fusion_doc and recipe_data:
                                # Use the recipe data to find all ingredients

                                # Remove fusion information from all ingredient cards
                                if 'ingredients' in recipe_data and recipe_data['ingredients']:
                                    for ingredient_data in recipe_data['ingredients']:
                                        try:
                                            # Get card_id and card_collection_id from ingredient data
                                            card_id = ingredient_data.get('card_id')
                                            card_collection_id = ingredient_data.get('card_collection_id')

                                            if card_id and card_collection_id:
                                                # Skip the card being deleted
                                                if card_id == document_id and card_collection_id == effective_collection_name:
                                                    continue

                                                # Get the card document
                                                ingredient_doc_ref = firestore_client.collection(card_collection_id).document(card_id)
                                                ingredient_doc = await ingredient_doc_ref.get()

                                                if ingredient_doc.exists:
                                                    ingredient_data = ingredient_doc.to_dict()

                                                    # Remove this fusion from the used_in_fusion array
                                                    if 'used_in_fusion' in ingredient_data:
                                                        if ingredient_data['used_in_fusion']:
                                                            # Filter out the fusion with this result_card_id
                                                            updated_fusions = [
                                                                fusion for fusion in ingredient_data['used_in_fusion'] 
                                                                if fusion.get('result_card_id') != result_card_id
                                                            ]

                                                            # Update the card with the filtered array
                                                            await ingredient_doc_ref.update({
                                                                'used_in_fusion': updated_fusions
                                                            })

                                                            logger.info(f"Removed fusion information from card '{card_id}' in collection '{card_collection_id}'")
                                        except Exception as e:
                                            # Log the error but continue with other ingredients
                                            logger.error(f"Error removing fusion information from ingredient card: {e}", exc_info=True)

                                # Delete the fusion recipe
                                # We already have the fusion_doc_ref from the search above
                                await fusion_doc_ref.delete()
                                logger.info(f"Deleted fusion recipe '{result_card_id}' from pack collection '{pack_collection_id}' with pack ID '{pack_id}'")
                        except Exception as e:
                            # Log the error but continue with other fusion recipes
                            logger.error(f"Error deleting fusion recipe '{result_card_id}': {e}", exc_info=True)
    except Exception as e:
        logger.error(f"Error cleaning fusion references for card {document_id}: {e}", exc_info=True)

async def delete_card_from_firestore(document_id: str, collection_name: str | None = None) -> None:
    """
    Deletes a card document from Firestore.
    If collection_name is provided, it deletes from that collection, otherwise defaults
    to settings.firestore_collection_cards.

    If collection_name is provided, it will look up the metadata for that collection
    in the metadata collection and use the firestoreCollection for the Firestore collection.
    """
    firestore_client = get_firestore_client()
    if not collection_name:
        effective_collection_name = settings.firestore_collection_cards
    else:
        # Try to get the metadata for the collection
        try:
            metadata = await get_collection_metadata(collection_name)
            effective_collection_name = metadata.firestoreCollection
            logger.info(f"Using metadata for collection '{collection_name}': firestoreCollection='{effective_collection_name}'")
        except HTTPException as e:
            if e.status_code == 404:
                # If metadata not found, use the provided collection_name as is
                effective_collection_name = collection_name
                logger.warning(f"Metadata for collection '{collection_name}' not found. Using provided collection_name as is.")
            else:
                # For other HTTP exceptions, re-raise
                raise e

    # Clean up fusion references before deleting the card
    await clean_fusion_references(document_id, effective_collection_name)

    doc_ref = firestore_client.collection(effective_collection_name).document(document_id)
    try:
        await doc_ref.delete()
        logger.info(f"Successfully deleted card {document_id} from collection '{effective_collection_name}'.")
    except Exception as e:
        logger.error(f"Error deleting card {document_id} from '{effective_collection_name}': {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Could not delete card from collection '{effective_collection_name}'.")

async def add_collection_metadata(metadata: CollectionMetadata) -> CollectionMetadata:
    """
    Adds metadata for a collection to the metadata collection.
    Uses the name field as the document ID.
    Returns the saved metadata.
    """
    firestore_client = get_firestore_client()
    meta_collection_name = settings.meta_data_collection

    # Use the name field as the document ID
    doc_id = metadata.name
    if not doc_id or not doc_id.strip():
        raise HTTPException(status_code=400, detail="Collection name (for use as ID) cannot be empty.")

    doc_ref = firestore_client.collection(meta_collection_name).document(doc_id)

    try:
        # Check if document already exists
        existing_doc = await doc_ref.get()
        if existing_doc.exists:
            logger.warning(f"Attempted to create metadata with existing name (ID): '{doc_id}' in collection '{meta_collection_name}'.")
            raise HTTPException(status_code=409, detail=f"Metadata for collection '{doc_id}' already exists.")

        # Prepare data for Firestore
        metadata_data = metadata.model_dump()

        await doc_ref.set(metadata_data)

        logger.info(f"Metadata for collection '{doc_id}' saved to Firestore collection '{meta_collection_name}'.")
        return metadata
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        logger.error(f"Failed to save metadata for collection '{doc_id}' to Firestore: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Could not save collection metadata: {str(e)}")

async def get_collection_metadata(collection_name: str) -> CollectionMetadata:
    """
    Retrieves metadata for a specific collection from the metadata collection.
    Returns the metadata if found, otherwise raises a 404 error.
    """
    firestore_client = get_firestore_client()
    meta_collection_name = settings.meta_data_collection

    doc_ref = firestore_client.collection(meta_collection_name).document(collection_name)

    try:
        doc_snapshot = await doc_ref.get()

        if not doc_snapshot.exists:
            logger.warning(f"Metadata for collection '{collection_name}' not found in Firestore.")
            raise HTTPException(status_code=404, detail=f"Metadata for collection '{collection_name}' not found")

        metadata_data = doc_snapshot.to_dict()
        return CollectionMetadata(**metadata_data)
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        logger.error(f"Failed to retrieve metadata for collection '{collection_name}' from Firestore: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Could not retrieve collection metadata: {str(e)}")

async def get_all_collection_metadata() -> List[CollectionMetadata]:
    """
    Retrieves metadata for all collections from the metadata collection.
    Returns a list of metadata objects.
    """
    firestore_client = get_firestore_client()
    meta_collection_name = settings.meta_data_collection

    try:
        collection_ref = firestore_client.collection(meta_collection_name)
        docs_stream = collection_ref.stream()

        metadata_list = []
        async for doc in docs_stream:
            metadata_data = doc.to_dict()
            metadata_list.append(CollectionMetadata(**metadata_data))

        return metadata_list
    except Exception as e:
        logger.error(f"Failed to retrieve all collection metadata from Firestore: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Could not retrieve collection metadata: {str(e)}")

async def delete_collection_metadata(collection_name: str) -> None:
    """
    Deletes metadata for a specific collection from the metadata collection.
    
    Args:
        collection_name: The name of the collection metadata to delete.
        
    Raises:
        HTTPException: 404 if collection metadata not found, 500 for other errors
    """
    firestore_client = get_firestore_client()
    meta_collection_name = settings.meta_data_collection
    
    try:
        # Get a reference to the document
        doc_ref = firestore_client.collection(meta_collection_name).document(collection_name)
        
        # Check if the document exists
        doc = await doc_ref.get()
        if not doc.exists:
            raise HTTPException(status_code=404, detail=f"Metadata for collection '{collection_name}' not found")
        
        # Delete the document
        await doc_ref.delete()
        
        logger.info(f"Metadata for collection '{collection_name}' deleted from Firestore collection '{meta_collection_name}'.")
        
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Failed to delete metadata for collection '{collection_name}' from Firestore: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Could not delete collection metadata: {str(e)}")

# Function to get all collections - for API naming consistency
async def get_all_collections() -> List[CollectionMetadata]:
    """
    Alias for get_all_collection_metadata.
    Returns metadata for all collections from the metadata collection.
    """
    return await get_all_collection_metadata()

async def add_to_official_listing(collection_id: str, card_id: str, quantity: int = 1, pricePoints: int = 0) -> dict:
    """
    Adds a card to the official_listing collection.
    Creates a new collection called "official_listing" if it doesn't exist.
    Adds a subcollection with the provided collection_id.
    Adds the card under that subcollection using the actual card data.

    Includes a card_reference field in the format "{effective_collection_name}/{card_id}"
    that points to the original card in Firestore.

    Also updates the original card in Firestore:
    - Creates/increments a new field called quantity_in_offical_marketplace
    - Decreases the quantity field by the specified quantity

    Uses collection_id directly with get_card_by_id to get the card information.

    Args:
        collection_id: The ID of the collection the card belongs to
        card_id: The ID of the card to add to the official listing
        quantity: The quantity of cards to add to the official listing (default: 1)
        pricePoints: The price in points for the card in the official listing (default: 0)

    Returns:
        dict: The data of the added card

    Raises:
        HTTPException: 404 if card not found, 500 for other errors
    """
    firestore_client = get_firestore_client()

    try:
        # Check if the card is already listed in the official marketplace
        official_listing_ref = firestore_client.collection("official_listing").document(collection_id).collection("cards").document(card_id)
        existing_listing = await official_listing_ref.get()
        
        if existing_listing.exists:
            raise HTTPException(status_code=409, detail=f"Card {card_id} from collection {collection_id} is already listed in the official marketplace")
        
        # Get the card data from the original collection using collection_id directly
        card = await get_card_by_id(card_id, collection_id)

        # Get the effective collection name for updating the original card
        if not collection_id:
            effective_collection_name = settings.firestore_collection_cards
        else:
            try:
                metadata = await get_collection_metadata(collection_id)
                effective_collection_name = metadata.firestoreCollection
            except HTTPException as e:
                if e.status_code == 404:
                    effective_collection_name = collection_id
                else:
                    raise e

        # Get a reference to the original card document
        original_card_ref = firestore_client.collection(effective_collection_name).document(card_id)

        # Get the current card data
        original_card_doc = await original_card_ref.get()
        if not original_card_doc.exists:
            raise HTTPException(status_code=404, detail=f"Card with ID {card_id} not found")

        original_card_data = original_card_doc.to_dict()

        # Update the original card:
        # 1. Add/increment quantity_in_offical_marketplace by the specified quantity
        # 2. Decrease quantity by the specified quantity (allowing negative values)
        current_quantity = original_card_data.get('quantity', 0)
        current_marketplace_quantity = original_card_data.get('quantity_in_offical_marketplace', 0)

        # No longer checking if current_quantity < quantity
        # This allows negative quantities in the main card collection as desired

        # Update the original card (allowing negative quantities)
        await original_card_ref.update({
            'quantity': current_quantity - quantity,  # Removed max(0, ...) to allow negative values
            'quantity_in_offical_marketplace': current_marketplace_quantity + quantity
        })

        # Convert the card model to a dictionary for Firestore
        card_dict = card.model_dump()

        # Add the pricePoints and collection_id fields to the card_dict
        card_dict['pricePoints'] = pricePoints
        card_dict['quantity'] = quantity  # Set the quantity to the specified quantity
        card_dict['card_reference'] = f"{effective_collection_name}/{card_id}"  # Add card reference
        card_dict['collection_id'] = collection_id  # Add collection_id field

        # Add the card to the official_listing collection
        # Path: official_listing/{collection_id}/{card_id}
        doc_ref = firestore_client.collection("official_listing").document(collection_id).collection("cards").document(card_id)

        await doc_ref.set(card_dict)

        logger.info(f"Added card {card_id} from collection {collection_id} to official listing with quantity {quantity} and pricePoints {pricePoints}")
        logger.info(f"Updated original card: decreased quantity by {quantity}, increased quantity_in_offical_marketplace by {quantity}")

        return card_dict

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error adding card {card_id} from collection {collection_id} to official_listing: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Could not add card to official listing: {str(e)}")

async def get_all_official_listings(collection_id: str) -> List[dict]:
    """
    Retrieves all cards from the official_listing collection for a specific collection.

    Args:
        collection_id: The ID of the collection to get official listings for

    Returns:
        List[dict]: A list of card data from the official listing

    Raises:
        HTTPException: 404 if collection not found, 500 for other errors
    """
    firestore_client = get_firestore_client()

    try:
        # Get a reference to the cards subcollection
        cards_ref = firestore_client.collection("official_listing").document(collection_id).collection("cards")

        # Get all documents from the cards subcollection
        cards_stream = cards_ref.stream()

        # Create a list to store the card data
        cards_list = []

        # Iterate through the documents and add them to the list
        async for card_doc in cards_stream:
            card_data = card_doc.to_dict()
            card_data['id'] = card_doc.id  # Add the document ID to the card data

            # Ensure collection_id is in the card data
            if 'collection_id' not in card_data:
                card_data['collection_id'] = collection_id

            # R2 URLs are public, no need to sign them

            cards_list.append(card_data)

        logger.info(f"Retrieved {len(cards_list)} cards from official listing for collection {collection_id}")
        return cards_list

    except Exception as e:
        logger.error(f"Error retrieving cards from official listing for collection {collection_id}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Could not retrieve cards from official listing: {str(e)}")

async def update_official_listing(collection_id: str, card_id: str, pricePoints: Optional[int] = None, quantity: Optional[int] = None) -> dict:
    """
    Updates a card in the official_listing collection.
    This function updates the pricePoints and/or quantity of a card in the official marketplace.
    If quantity is updated, it also updates the original card's quantity and quantity_in_offical_marketplace fields.

    Args:
        collection_id: The ID of the collection the card belongs to
        card_id: The ID of the card to update in the official listing
        pricePoints: The new price in points for the card in the official listing (optional)
        quantity: The new quantity for the card in the official listing (optional)

    Returns:
        dict: The updated card data

    Raises:
        HTTPException: 404 if card not found, 400 for invalid quantity, 500 for other errors
    """
    firestore_client = get_firestore_client()

    try:
        # Get a reference to the card in the official_listing collection
        official_listing_ref = firestore_client.collection("official_listing").document(collection_id).collection("cards").document(card_id)

        # Get the current card data from the official_listing collection
        official_listing_doc = await official_listing_ref.get()
        if not official_listing_doc.exists:
            raise HTTPException(status_code=404, detail=f"Card with ID {card_id} not found in official listing for collection {collection_id}")

        official_listing_data = official_listing_doc.to_dict()
        current_listing_quantity = official_listing_data.get('quantity', 0)

        # Prepare update data for official listing
        update_data = {}
        log_parts = []
        
        if pricePoints is not None:
            update_data['pricePoints'] = pricePoints
            official_listing_data['pricePoints'] = pricePoints
            log_parts.append(f"pricePoints to {pricePoints}")
            
        if quantity is not None:
            if quantity < 0:
                raise HTTPException(status_code=400, detail="Quantity cannot be negative")
                
            # Calculate the difference in quantity
            quantity_diff = quantity - current_listing_quantity
            
            # If quantity is changing, we need to update the original card as well
            if quantity_diff != 0:
                # Get the effective collection name for updating the original card
                if not collection_id:
                    effective_collection_name = settings.firestore_collection_cards
                else:
                    try:
                        metadata = await get_collection_metadata(collection_id)
                        effective_collection_name = metadata.firestoreCollection
                    except HTTPException as e:
                        if e.status_code == 404:
                            effective_collection_name = collection_id
                        else:
                            raise e

                # Get a reference to the original card document
                original_card_ref = firestore_client.collection(effective_collection_name).document(card_id)
                original_card_doc = await original_card_ref.get()
                
                if not original_card_doc.exists:
                    raise HTTPException(status_code=404, detail=f"Original card with ID {card_id} not found in collection {effective_collection_name}")

                original_card_data = original_card_doc.to_dict()
                current_original_quantity = original_card_data.get('quantity', 0)
                current_marketplace_quantity = original_card_data.get('quantity_in_offical_marketplace', 0)

                # Check if we have enough quantity in the original card if we're increasing marketplace quantity
                if quantity_diff > 0 and current_original_quantity < quantity_diff:
                    raise HTTPException(status_code=400, detail=f"Insufficient quantity in original card. Available: {current_original_quantity}, requested increase: {quantity_diff}")

                # Update the original card quantities
                new_original_quantity = current_original_quantity - quantity_diff
                new_marketplace_quantity = current_marketplace_quantity + quantity_diff
                
                await original_card_ref.update({
                    'quantity': max(0, new_original_quantity),
                    'quantity_in_offical_marketplace': max(0, new_marketplace_quantity)
                })
                
                logger.info(f"Updated original card {card_id}: quantity changed by {-quantity_diff}, marketplace quantity changed by {quantity_diff}")
            
            update_data['quantity'] = quantity
            official_listing_data['quantity'] = quantity
            log_parts.append(f"quantity to {quantity}")

        # Update the card in the official_listing collection
        if update_data:
            await official_listing_ref.update(update_data)
            log_message = "set " + ", ".join(log_parts)
            logger.info(f"Updated card {card_id} from collection {collection_id} in official listing: {log_message}")

        # Return the updated official_listing_data

        # Ensure collection_id is in the returned data
        if 'collection_id' not in official_listing_data:
            official_listing_data['collection_id'] = collection_id

        return official_listing_data

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error updating card {card_id} from collection {collection_id} in official listing: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Could not update card in official listing: {str(e)}")

async def withdraw_from_official_listing(collection_id: str, card_id: str) -> dict:
    """
    Withdraws a card completely from the official_listing collection.
    This function removes the card from the official marketplace entirely:
    - Gets the card from the official_listing collection
    - Deletes the card document from the official_listing collection
    - Returns all quantity_in_offical_marketplace back to the original card's quantity field
    - Sets quantity_in_offical_marketplace to 0

    Args:
        collection_id: The ID of the collection the card belongs to
        card_id: The ID of the card to withdraw from the official listing

    Returns:
        dict: The data of the withdrawn card

    Raises:
        HTTPException: 404 if card not found, 500 for other errors
    """
    firestore_client = get_firestore_client()

    try:
        # Get a reference to the card in the official_listing collection
        official_listing_ref = firestore_client.collection("official_listing").document(collection_id).collection("cards").document(card_id)

        # Get the current card data from the official_listing collection
        official_listing_doc = await official_listing_ref.get()
        if not official_listing_doc.exists:
            raise HTTPException(status_code=404, detail=f"Card with ID {card_id} not found in official listing for collection {collection_id}")

        official_listing_data = official_listing_doc.to_dict()
        listing_quantity = official_listing_data.get('quantity', 0)

        # Get the effective collection name for updating the original card
        if not collection_id:
            effective_collection_name = settings.firestore_collection_cards
        else:
            try:
                metadata = await get_collection_metadata(collection_id)
                effective_collection_name = metadata.firestoreCollection
            except HTTPException as e:
                if e.status_code == 404:
                    effective_collection_name = collection_id
                else:
                    raise e

        # Get a reference to the original card document
        original_card_ref = firestore_client.collection(effective_collection_name).document(card_id)

        # Get the current card data
        original_card_doc = await original_card_ref.get()
        if not original_card_doc.exists:
            raise HTTPException(status_code=404, detail=f"Original card with ID {card_id} not found in collection {effective_collection_name}")

        original_card_data = original_card_doc.to_dict()

        # Update the original card:
        # 1. Return all marketplace quantity back to the original card
        # 2. Set quantity_in_offical_marketplace to 0
        current_quantity = original_card_data.get('quantity', 0)
        current_marketplace_quantity = original_card_data.get('quantity_in_offical_marketplace', 0)

        # Update the original card to return all quantity
        await original_card_ref.update({
            'quantity': current_quantity + current_marketplace_quantity,
            'quantity_in_offical_marketplace': 0
        })

        # Delete the card from the official_listing collection
        await official_listing_ref.delete()
        
        logger.info(f"Removed card {card_id} from collection {collection_id} from official listing")
        logger.info(f"Returned {current_marketplace_quantity} quantity to original card, set quantity_in_offical_marketplace to 0")

        # Return the withdrawn card data
        official_listing_data['quantity'] = 0  # Set to 0 since it's been removed

        # Ensure collection_id is in the returned data
        if 'collection_id' not in official_listing_data:
            official_listing_data['collection_id'] = collection_id

        return official_listing_data

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error withdrawing card {card_id} from collection {collection_id} from official_listing: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Could not withdraw card from official listing: {str(e)}")

async def get_card_by_id(document_id: str, collection_name: str | None = None) -> StoredCardInfo:
    """
    Retrieves all data for a specific card from Firestore by its ID.
    If collection_name is provided, it fetches from that collection, otherwise defaults
    to settings.firestore_collection_cards.

    If collection_name is provided, it will look up the metadata for that collection
    in the metadata collection and use the firestoreCollection for the Firestore collection.

    Returns:
        StoredCardInfo: Complete data for the requested card

    Raises:
        HTTPException: 404 if card not found, 500 for other errors
    """
    firestore_client = get_firestore_client()

    if not collection_name:
        effective_collection_name = settings.firestore_collection_cards
    else:
        # Try to get the metadata for the collection
        try:
            metadata = await get_collection_metadata(collection_name)
            effective_collection_name = metadata.firestoreCollection
            logger.info(f"Using metadata for collection '{collection_name}': firestoreCollection='{effective_collection_name}'")
        except HTTPException as e:
            if e.status_code == 404:
                # If metadata not found, use the provided collection_name as is
                effective_collection_name = collection_name
                logger.warning(f"Metadata for collection '{collection_name}' not found. Using provided collection_name as is.")
            else:
                # For other HTTP exceptions, re-raise
                raise e

    doc_ref = firestore_client.collection(effective_collection_name).document(document_id)

    try:
        doc = await doc_ref.get()

        if not doc.exists:
            logger.warning(f"Card with ID {document_id} not found in collection '{effective_collection_name}'.")
            raise HTTPException(status_code=404, detail=f"Card with ID {document_id} not found")

        # Get the card data and add the document ID
        card_data = doc.to_dict()
        card_data['id'] = document_id

        # R2 URLs are public, no need to sign them

        return StoredCardInfo(**card_data)

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error retrieving card {document_id} from '{effective_collection_name}': {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Could not retrieve card data from collection '{effective_collection_name}': {str(e)}")

async def _update_card_in_all_packs(card_id: str, collection_id: str, updates: dict) -> None:
    """
    Helper function to update a card in all packs that contain it.
    This ensures consistency between master cards and pack cards.
    
    Args:
        card_id: The ID of the card to update
        collection_id: The collection the card belongs to  
        updates: Dictionary of fields to update
    """
    try:
        firestore_client = get_firestore_client()
        
        # Get all packs for this collection
        packs_ref = firestore_client.collection('packs').document(collection_id).collection(collection_id)
        packs_docs = await packs_ref.get()
        
        if not packs_docs:
            logger.info(f"No packs found for collection {collection_id}")
            return
        
        packs_updated = 0
        
        # Update cards in each pack that contains this card
        for pack_doc in packs_docs:
            pack_id = pack_doc.id
            pack_data = pack_doc.to_dict()
            
            # Check if this pack contains the card in its cards map
            cards_map = pack_data.get('cards', {})
            if card_id in cards_map:
                try:
                    # Update the card in this pack's cards subcollection
                    pack_card_ref = (
                        firestore_client
                        .collection('packs')
                        .document(collection_id)
                        .collection(collection_id)
                        .document(pack_id)
                        .collection('cards')
                        .document(card_id)
                    )
                    
                    # Check if card document exists in pack's cards subcollection
                    card_doc = await pack_card_ref.get()
                    if card_doc.exists:
                        await pack_card_ref.update(updates)
                        packs_updated += 1
                        logger.debug(f"Updated card {card_id} in pack {pack_id}")
                        
                except Exception as e:
                    # Log error but continue with other packs
                    logger.error(f"Failed to update card {card_id} in pack {pack_id}: {str(e)}")
        
        logger.info(f"Updated card {card_id} in {packs_updated} packs for collection {collection_id}")
        
    except Exception as e:
        # Log error but don't fail the main update operation
        logger.error(f"Error updating card {card_id} in packs for collection {collection_id}: {str(e)}")
        # Don't raise exception to avoid breaking the main card update
