# Application settings
APP_NAME="Card Gacha API"

# Google Cloud Storage settings
GCS_PROJECT_ID="seventh-program-433718-h8"
GCS_BUCKET_NAME="pokemon_cards_pull"
PACKS_BUCKET="pack_covers"

# Firestore settings
FIRESTORE_PROJECT_ID="seventh-program-433718-h8"
FIRESTORE_COLLECTION_CARDS="pokemon"
META_DATA_COLLECTION="collection_meta_data"
QUOTA_PROJECT_ID="seventh-program-433718-h8"

# User backend service configuration
USER_BACKEND_URL="http://localhost:8082/users/api/v1"

# Logging settings
LOG_LEVEL="INFO"