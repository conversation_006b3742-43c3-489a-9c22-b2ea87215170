apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: backend
spec:
  template:
    metadata:
      annotations:
        # Increase timeout for large file uploads (up to 1800 seconds = 30 minutes)
        run.googleapis.com/timeout: "1800"
        # Allow concurrent requests
        run.googleapis.com/execution-environment: gen2
    spec:
      # Increase request timeout
      timeoutSeconds: 1800
      containers:
      - image: gcr.io/seventh-program-433718-h8/backend
        ports:
        - containerPort: 8080
        resources:
          limits:
            cpu: "2"  # Increased CPU for processing large files
            memory: 1Gi  # Increased memory for handling large uploads
        env:
        - name: APP_NAME
          value: "User Service API"
        - name: GCS_PROJECT_ID
          value: "seventh-program-433718-h8"
        - name: GCS_BUCKET_NAME
          value: "user_profiles"
        - name: USER_AVATOR_BUCKET
          value: "user_avator"
        - name: QUOTA_PROJECT_ID
          value: "seventh-program-433718-h8"
        - name: FIRESTORE_PROJECT_ID
          value: "seventh-program-433718-h8"
        - name: FIRESTORE_COLLECTION_USERS
          value: "users"
        - name: CARD_EXPIRE_DAYS
          value: "10"
        - name: CARD_BUYBACK_EXPIRE_DAYS
          value: "7"
        - name: STORAGE_SERVICE_URL
          value: "http://0.0.0.0:8080"
        - name: STRIPE_API_KEY
          valueFrom:
            secretKeyRef:
              name: backend-secrets
              key: stripe-api-key
        - name: STRIPE_WEBHOOK_SECRET
          valueFrom:
            secretKeyRef:
              name: backend-secrets
              key: stripe-webhook-secret
        - name: DB_INSTANCE_CONNECTION_NAME
          value: "seventh-program-433718-h8:us-central1:test"
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: backend-secrets
              key: db-user
        - name: DB_PASS
          valueFrom:
            secretKeyRef:
              name: backend-secrets
              key: db-pass
        - name: DB_NAME
          value: "test"
        - name: DB_PORT
          value: "5432"
        - name: LOG_LEVEL
          value: "INFO"
