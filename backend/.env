# Application settings
APP_NAME="Card Gacha API"

# Google Cloud Storage settings
GCS_PROJECT_ID="seventh-program-433718-h8"
GCS_BUCKET_NAME="pokemon_cards_pull"
QUOTA_PROJECT_ID="seventh-program-433718-h8"

# Firestore settings
FIRESTORE_PROJECT_ID="seventh-program-433718-h8"
FIRESTORE_COLLECTION_CARDS="pokemon"
META_DATA_COLLECTION="collection_meta_data"

# Bucket settings
PACKS_BUCKET="pack_covers"
USER_AVATOR_BUCKET="user_avatars"
EMBLEM_BUCKET="achievement_emblems"


#Shippo API settings
SHIPPO_API_KEY="shippo_test_f722395e8ea267c139451e2d063d6cc76eea8e58"

# User backend service configuration
USER_BACKEND_URL="http://localhost:8082/users/api/v1"

# Logging settings
LOG_LEVEL="INFO"

# Typesense settings
TYPESENSE_API_KEY=68vTuaLCLRTBDmpEpnCy0cMSHP0oC2Kc

TYPESENSE_HOST=norguvx0y71jmcsdp-1.a1.typesense.net
TYPESENSE_PORT=443
TYPESENSE_PROTOCOL=https
TYPESENSE_COLLECTION_POKEMON=pokemon_test
TYPESENSE_COLLECTION_ONE_PIECE=one_piece_test
TYPESENSE_COLLECTION_MAGIC=magic_test
TYPESENSE_COLLECTION_LISTINGS=listings_test

# Cloudflare R2 Storage settings
R2_ACCESS_KEY_ID="4efb72bd0c675eaef34bcac66113062f"
R2_SECRET_ACCESS_KEY="9c4df4f314dd939cf0277c4d962db9b6c7aaceb1284ccfb5687654ad69909a1c"
R2_ENDPOINT="https://10c5a6603c2479b5ee449179b5877db5.r2.cloudflarestorage.com"

# R2 Bucket names
R2_BUCKET_ACHIEVEMENT="achievement-dev"
R2_BUCKET_PACK="pack-dev"
R2_BUCKET_CARD="card-dev"
R2_BUCKET_AVATAR="avator-dev"
