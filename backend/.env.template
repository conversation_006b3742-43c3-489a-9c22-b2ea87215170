# Application settings
APP_NAME="Card Gacha API"

# Google Cloud Storage settings (deprecated - migrating to R2)
GCS_PROJECT_ID="your-project-id"
GCS_BUCKET_NAME="pokemon_cards_pull"
PACKS_BUCKET="pack_covers"
USER_AVATOR_BUCKET="user_avatars"
EMBLEM_BUCKET="achievement_emblems"

# Cloudflare R2 Storage settings
R2_ACCESS_KEY_ID="your-r2-access-key-id"
R2_SECRET_ACCESS_KEY="your-r2-secret-access-key"
R2_ENDPOINT="https://your-account-id.r2.cloudflarestorage.com"

# R2 Bucket names
R2_BUCKET_ACHIEVEMENT="achievement-dev"
R2_BUCKET_PACK="pack-dev"
R2_BUCKET_CARD="card-dev"
R2_BUCKET_AVATAR="avator-dev"

# Firestore settings
FIRESTORE_PROJECT_ID="your-project-id"
FIRESTORE_COLLECTION_CARDS="pokemon"
META_DATA_COLLECTION="collection_meta_data"
QUOTA_PROJECT_ID="your-project-id"

# User backend service configuration
USER_BACKEND_URL="http://localhost:8082/users/api/v1"
INTERNAL_API_SECRET="your-strong-internal-secret-here"

# Typesense settings
TYPESENSE_API_KEY="your-typesense-api-key"
TYPESENSE_HOST="your-typesense-host.a1.typesense.net"
TYPESENSE_PORT="443"
TYPESENSE_PROTOCOL="https"
TYPESENSE_COLLECTION_POKEMON="pokemon_test"
TYPESENSE_COLLECTION_ONE_PIECE="one_piece_test"
TYPESENSE_COLLECTION_MAGIC="magic_test"
TYPESENSE_COLLECTION_LISTINGS="listings_test"

# Logging settings
LOG_LEVEL="INFO"

# Shippo API settings
SHIPPO_API_KEY="your-shippo-api-key"

# CORS settings
CORS_ALLOWED_ORIGINS="http://localhost:3000,https://yourdomain.com"  # Comma-separated list
DEVELOPMENT_MODE="False"  # Set to "True" to skip CORS checks in development