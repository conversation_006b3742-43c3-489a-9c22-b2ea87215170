#!/usr/bin/env python3
"""Debug script to test Firestore packs query"""

import asyncio
import os
from google.cloud import firestore
from dotenv import load_dotenv

# Load environment variables
load_dotenv("user_backend/.env")

async def debug_packs_query():
    # Initialize Firestore client
    db = firestore.AsyncClient()
    
    collection_id = "one_piece"
    
    print(f"Debugging packs query for collection: {collection_id}")
    print("-" * 50)
    
    try:
        # Step 1: Check if collection document exists
        collection_ref = db.collection("packs").document(collection_id)
        collection_doc = await collection_ref.get()
        
        if collection_doc.exists:
            print(f"✓ Collection document '{collection_id}' exists")
            print(f"  Data: {collection_doc.to_dict()}")
        else:
            print(f"✗ Collection document '{collection_id}' NOT FOUND")
            return
        
        print("\n" + "-" * 50)
        
        # Step 2: List all documents in the subcollection (without filter)
        print(f"All documents in /packs/{collection_id}/{collection_id}/:")
        packs_ref = db.collection("packs").document(collection_id).collection(collection_id)
        all_docs = await packs_ref.get()
        
        if not all_docs:
            print("  No documents found!")
        else:
            for doc in all_docs:
                data = doc.to_dict()
                print(f"  - ID: {doc.id}")
                print(f"    is_active: {data.get('is_active', 'NOT SET')}")
                print(f"    name: {data.get('name', 'NOT SET')}")
                print(f"    Data keys: {list(data.keys())}")
        
        print("\n" + "-" * 50)
        
        # Step 3: Query with is_active filter
        print("Documents with is_active=True filter:")
        active_packs_ref = (
            db.collection("packs")
            .document(collection_id)
            .collection(collection_id)
            .where("is_active", "==", True)
        )
        active_docs = await active_packs_ref.get()
        
        if not active_docs:
            print("  No active documents found!")
        else:
            for doc in active_docs:
                data = doc.to_dict()
                print(f"  - ID: {doc.id}")
                print(f"    Data: {data}")
        
        print("\n" + "-" * 50)
        
        # Step 4: Try to get specific document
        print(f"Trying to get specific document 'one_piece_test':")
        test_doc_ref = (
            db.collection("packs")
            .document(collection_id)
            .collection(collection_id)
            .document("one_piece_test")
        )
        test_doc = await test_doc_ref.get()
        
        if test_doc.exists:
            print(f"  ✓ Document exists!")
            data = test_doc.to_dict()
            print(f"    is_active: {data.get('is_active', 'NOT SET')}")
            print(f"    Type of is_active: {type(data.get('is_active'))}")
            print(f"    Full data: {data}")
        else:
            print(f"  ✗ Document NOT FOUND")
            
    except Exception as e:
        print(f"ERROR: {type(e).__name__}: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        await db.close()

if __name__ == "__main__":
    asyncio.run(debug_packs_query())