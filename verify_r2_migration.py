#!/usr/bin/env python3
"""
Script to verify the R2 migration and show statistics about URLs in Firestore.
"""

import asyncio
import logging
from typing import Dict, List, Any
from google.cloud import firestore
from google.oauth2 import service_account
import os
from collections import Counter

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# R2 domains
R2_DOMAINS = [
    'achievement-dev.zapull.fun',
    'pack-dev.zapull.fun',
    'card-dev.zapull.fun',
    'avator-dev.zapull.fun'
]

def analyze_url(url: str) -> str:
    """Categorize a URL."""
    if not url:
        return 'empty'
    elif any(domain in url for domain in R2_DOMAINS):
        return 'r2'
    elif url.startswith('gs://'):
        return 'gcs_uri'
    elif 'storage.googleapis.com' in url:
        return 'gcs_signed'
    else:
        return 'other'

def analyze_nested_data(data: Any, stats: Counter):
    """Recursively analyze nested data structures to find URLs."""
    if isinstance(data, dict):
        for key, value in data.items():
            if key in ['url', 'image_url', 'icon_url', 'avatar'] and isinstance(value, str):
                url_type = analyze_url(value)
                stats[url_type] += 1
                if url_type in ['gcs_uri', 'gcs_signed']:
                    logger.debug(f"Found GCS URL in {key}: {value}")
            else:
                analyze_nested_data(value, stats)
    elif isinstance(data, list):
        for item in data:
            analyze_nested_data(item, stats)

async def analyze_collection(db: firestore.Client, collection_name: str) -> Dict[str, int]:
    """Analyze URLs in a collection."""
    stats = Counter()
    doc_count = 0
    
    logger.info(f"Analyzing collection: {collection_name}")
    docs = db.collection(collection_name).stream()
    
    for doc in docs:
        doc_count += 1
        doc_data = doc.to_dict()
        if not doc_data:
            continue
            
        # Special handling for different collections
        if collection_name == 'achievements':
            # Check reward array
            if 'reward' in doc_data:
                analyze_nested_data(doc_data['reward'], stats)
            # Check top-level fields
            for field in ['image_url', 'icon_url', 'emblem_url']:
                if field in doc_data:
                    url_type = analyze_url(doc_data[field])
                    stats[url_type] += 1
                    
        elif collection_name in ['pokemon_card_info', 'one_piece']:
            if 'image_url' in doc_data:
                url_type = analyze_url(doc_data['image_url'])
                stats[url_type] += 1
                if url_type in ['gcs_uri', 'gcs_signed']:
                    logger.debug(f"Document {doc.id} has GCS URL: {doc_data['image_url']}")
                    
        elif collection_name == 'users':
            if 'avatar' in doc_data:
                url_type = analyze_url(doc_data['avatar'])
                stats[url_type] += 1
                if url_type in ['gcs_uri', 'gcs_signed']:
                    logger.debug(f"User {doc.id} has GCS avatar: {doc_data['avatar']}")
                    
    stats['total_docs'] = doc_count
    return dict(stats)

async def main():
    """Main function to verify the migration."""
    # Initialize Firestore client
    try:
        if os.getenv('GOOGLE_APPLICATION_CREDENTIALS'):
            credentials = service_account.Credentials.from_service_account_file(
                os.getenv('GOOGLE_APPLICATION_CREDENTIALS')
            )
            db = firestore.Client(credentials=credentials)
        else:
            db = firestore.Client()
            
        logger.info("Successfully initialized Firestore client")
    except Exception as e:
        logger.error(f"Failed to initialize Firestore client: {e}")
        return
        
    # Collections to analyze
    collections = [
        'achievements',
        'pokemon_card_info', 
        'one_piece',
        'users'
    ]
    
    # Analyze each collection
    print("\n" + "="*60)
    print("R2 Migration Verification Report")
    print("="*60 + "\n")
    
    total_stats = Counter()
    
    for collection_name in collections:
        try:
            stats = await analyze_collection(db, collection_name)
            
            print(f"\nCollection: {collection_name}")
            print("-" * 40)
            print(f"Total documents: {stats.get('total_docs', 0)}")
            print(f"R2 URLs: {stats.get('r2', 0)}")
            print(f"GCS URIs (gs://): {stats.get('gcs_uri', 0)}")
            print(f"GCS Signed URLs: {stats.get('gcs_signed', 0)}")
            print(f"Other URLs: {stats.get('other', 0)}")
            print(f"Empty/None: {stats.get('empty', 0)}")
            
            # Add to totals
            for key, value in stats.items():
                if key != 'total_docs':
                    total_stats[key] += value
                    
        except Exception as e:
            logger.error(f"Error analyzing collection {collection_name}: {e}")
            
    # Print summary
    print("\n" + "="*60)
    print("SUMMARY")
    print("="*60)
    print(f"Total R2 URLs: {total_stats.get('r2', 0)}")
    print(f"Total GCS URIs (need migration): {total_stats.get('gcs_uri', 0)}")
    print(f"Total GCS Signed URLs (need migration): {total_stats.get('gcs_signed', 0)}")
    print(f"Total Other URLs: {total_stats.get('other', 0)}")
    print(f"Total Empty/None: {total_stats.get('empty', 0)}")
    
    remaining = total_stats.get('gcs_uri', 0) + total_stats.get('gcs_signed', 0)
    if remaining > 0:
        print(f"\n⚠️  {remaining} URLs still need migration to R2!")
    else:
        print("\n✅ All URLs have been migrated to R2!")

if __name__ == "__main__":
    asyncio.run(main())