<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>About Us - Zapull | Meet Our Team</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" sizes="32x32" href="/static/zapull_black.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/static/zapull_black.png">
    <link rel="shortcut icon" type="image/png" href="/static/zapull_black.png">
    <link rel="apple-touch-icon" sizes="180x180" href="/static/zapull_black.png">
    <link rel="manifest" href="/manifest.json">
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="Meet the Zapull team. Learn about our founders, company history, and vision for revolutionizing digital TCG collecting.">
    <meta name="keywords" content="Zapull team, about <PERSON><PERSON><PERSON>, TCG founders, trading card platform team">
    <meta name="author" content="Zapull">
    <meta name="robots" content="index, follow">
    
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
        
        /* Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
            background-color: #0a0a0a;
            color: #ffffff;
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* Container */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Section Spacing */
        .section {
            padding: 80px 0;
        }

        /* Typography */
        h1 {
            font-size: 48px;
            font-weight: 700;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #b794f6 0%, #9f7aea 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        h2 {
            font-size: 36px;
            font-weight: 600;
            margin-bottom: 40px;
            text-align: center;
        }

        h3 {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 15px;
        }

        p {
            font-size: 16px;
            color: #a0a0a0;
            line-height: 1.8;
        }

        /* Navigation Header */
        .header {
            position: fixed;
            top: 0;
            width: 100%;
            background: rgba(10, 10, 10, 0.95);
            backdrop-filter: blur(10px);
            z-index: 1000;
            border-bottom: 1px solid rgba(159, 122, 234, 0.2);
        }

        .nav-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 0;
        }

        .logo {
            height: 50px;
        }

        .logo img {
            height: 100%;
            width: auto;
        }

        .nav-links {
            display: flex;
            gap: 30px;
            align-items: center;
        }

        .nav-links a {
            color: #a0a0a0;
            text-decoration: none;
            transition: color 0.3s;
        }

        .nav-links a:hover {
            color: #9f7aea;
        }

        .nav-signup-btn {
            background: linear-gradient(135deg, #9f7aea 0%, #805ad5 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .nav-signup-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(159, 122, 234, 0.3);
        }

        /* Hero Section */
        .hero {
            padding: 140px 0 80px;
            text-align: center;
            background: radial-gradient(ellipse at center, rgba(159, 122, 234, 0.1) 0%, transparent 70%);
        }

        /* Team Section */
        .team-section {
            background: rgba(159, 122, 234, 0.02);
        }

        .team-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
            margin-top: 60px;
        }

        .team-member {
            text-align: center;
            padding: 40px;
            background: rgba(255, 255, 255, 0.02);
            border: 1px solid rgba(159, 122, 234, 0.2);
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .team-member:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(159, 122, 234, 0.1);
        }

        .member-photo {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            background: linear-gradient(135deg, #9f7aea 0%, #805ad5 100%);
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 60px;
            color: white;
        }

        .member-name {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #ffffff;
        }

        .member-title {
            font-size: 18px;
            color: #9f7aea;
            margin-bottom: 20px;
        }

        .member-bio {
            font-size: 14px;
            color: #b0b0b0;
            line-height: 1.6;
        }

        /* Product Stage Section */
        .stage-section {
            text-align: center;
        }

        .stage-badge {
            display: inline-block;
            padding: 8px 20px;
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
            border-radius: 20px;
            font-weight: 600;
            margin-bottom: 20px;
        }

        .timeline {
            max-width: 600px;
            margin: 40px auto;
            position: relative;
        }

        .timeline-item {
            padding: 20px;
            background: rgba(255, 255, 255, 0.02);
            border: 1px solid rgba(159, 122, 234, 0.2);
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .timeline-item.active {
            border-color: #f59e0b;
            background: rgba(245, 158, 11, 0.1);
        }

        /* Company Details Section */
        .company-details {
            background: rgba(159, 122, 234, 0.02);
        }

        .details-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 40px;
            margin-top: 40px;
        }

        .detail-card {
            text-align: center;
            padding: 30px;
            background: rgba(255, 255, 255, 0.02);
            border: 1px solid rgba(159, 122, 234, 0.2);
            border-radius: 12px;
        }

        .detail-icon {
            width: 60px;
            height: 60px;
            background: rgba(159, 122, 234, 0.2);
            border-radius: 12px;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }

        /* Mobile Navigation */
        .mobile-nav {
            display: none;
        }

        /* Responsive */
        @media (max-width: 768px) {
            h1 {
                font-size: 36px;
            }

            h2 {
                font-size: 28px;
            }

            .hero {
                padding: 100px 0 40px;
            }

            .section {
                padding: 40px 0;
            }

            .nav-links {
                display: none;
            }

            .mobile-nav {
                display: block;
            }

            .team-grid {
                grid-template-columns: 1fr;
                gap: 30px;
            }

            .details-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation Header -->
    <header class="header">
        <div class="container">
            <nav class="nav-container">
                <div class="logo">
                    <a href="/">
                        <img src="/static/zapull_black.png" alt="Zapull Logo">
                    </a>
                </div>
                <div class="nav-links">
                    <a href="/">Home</a>
                    <a href="/#how">How It Works</a>
                    <a href="/#features">Features</a>
                    <a href="/about.html">About</a>
                    <a href="#" class="nav-signup-btn">Sign Up</a>
                </div>
                <div class="mobile-nav">
                    <a href="#" class="nav-signup-btn">Sign Up</a>
                </div>
            </nav>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <h1>About Zapull</h1>
            <p style="font-size: 20px; color: #b0b0b0; max-width: 600px; margin: 0 auto;">
                Building the future of digital TCG collecting with a passionate team of collectors and technologists.
            </p>
        </div>
    </section>

    <!-- Team Information Section -->
    <section class="section team-section">
        <div class="container">
            <h2>Meet Our Team</h2>
            <div class="team-grid">
                <div class="team-member">
                    <div class="member-photo" style="background: none; padding: 0; overflow: hidden;">
                        <img src="/static/team.jpg" alt="Andy" style="width: 100%; height: 100%; object-fit: cover;">
                    </div>
                    <h3 class="member-name">Andy Qi</h3>
                    <p class="member-title">Founder & CTO</p>
                    <p class="member-bio">
                        Full-stack engineer with expertise in secure payment systems and marketplaces. 
                        Background in building scalable platforms for digital commerce. 
                        Focused on creating seamless user experiences.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Product Development Stage -->
    <section class="section stage-section">
        <div class="container">
            <h2>Product Development Stage</h2>
            <span class="stage-badge">ALPHA DEVELOPMENT</span>
            <p style="font-size: 18px; color: #b0b0b0; max-width: 600px; margin: 20px auto;">
                Zapull is currently in Alpha development, building core features and preparing for beta launch.
            </p>
            
            <div class="timeline">
                <div class="timeline-item active">
                    <h3>May - July 2024 - Alpha Development (Current)</h3>
                    <p>Building core platform features and infrastructure</p>
                </div>
                <div class="timeline-item">
                    <h3>Late July / Early August 2024 - Beta Launch</h3>
                    <p>Closed beta with select collectors for testing</p>
                </div>
                <div class="timeline-item">
                    <h3>Q4 2024 - Public Launch</h3>
                    <p>Full platform launch open to all collectors</p>
                </div>
            </div>
            
            <p style="color: #f59e0b; font-weight: 600;">
                ⚡ Core features in development<br>
                ⚡ Beta launch coming late July 2024<br>
                ⚡ Join our waitlist to be first in line
            </p>
        </div>
    </section>

    <!-- Company Details -->
    <section class="section company-details">
        <div class="container">
            <h2>Company Information</h2>
            <div class="details-grid">
                <div class="detail-card">
                    <div class="detail-icon">📅</div>
                    <h3>Founded</h3>
                    <p>May 2024</p>
                    <p style="font-size: 14px; margin-top: 10px;">
                        Incorporated as Zapull Inc.
                    </p>
                </div>
                <div class="detail-card">
                    <div class="detail-icon">📍</div>
                    <h3>Headquarters</h3>
                    <p>Columbus, OH</p>
                    <p style="font-size: 14px; margin-top: 10px;">
                        Columbus, Ohio<br>
                        United States
                    </p>
                </div>
                <div class="detail-card">
                    <div class="detail-icon">✉️</div>
                    <h3>Contact</h3>
                    <p><EMAIL></p>
                    <p style="font-size: 14px; margin-top: 10px;">
                        For all inquiries and support
                    </p>
                </div>
            </div>
            
            <div style="text-align: center; margin-top: 60px; padding: 40px; background: rgba(255, 255, 255, 0.02); border-radius: 12px;">
                <h3 style="margin-bottom: 20px;">Our Mission</h3>
                <p style="font-size: 18px; color: #e0e0e0; max-width: 800px; margin: 0 auto;">
                    We're building Zapull to solve the biggest pain points in TCG collecting: 
                    storage limitations, dead inventory, and inefficient trading. Our platform 
                    combines the thrill of opening physical packs with the convenience of 
                    digital management, creating a seamless experience for modern collectors.
                </p>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer style="background: rgba(10, 10, 10, 0.95); padding: 40px 0; border-top: 1px solid rgba(159, 122, 234, 0.2);">
        <div class="container">
            <div style="text-align: center;">
                <p style="color: #666; font-size: 14px; margin: 0;">© 2024 Zapull Inc. All rights reserved.</p>
            </div>
        </div>
    </footer>
</body>
</html>