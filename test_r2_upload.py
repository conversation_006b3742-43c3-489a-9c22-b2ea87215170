#!/usr/bin/env python3
"""
Test script for R2 storage upload functionality.
"""

import asyncio
import os
import sys

# Add backend utils to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.utils.storage_utils import upload_image, upload_avatar, parse_base64_image, get_public_url

async def test_r2_functionality():
    """Test various R2 storage functions."""
    
    print("Testing R2 Storage Functions")
    print("=" * 50)
    
    # Test 1: Get public URL
    print("\n1. Testing get_public_url:")
    card_url = get_public_url("cards/pokemon/pikachu.png", "card")
    print(f"   Card URL: {card_url}")
    
    avatar_url = get_public_url("avatars/user123/profile.jpg", "avatar")
    print(f"   Avatar URL: {avatar_url}")
    
    # Test 2: Parse base64 image
    print("\n2. Testing parse_base64_image:")
    # Sample base64 image (1x1 transparent PNG)
    sample_base64 = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
    try:
        content_type, binary_data = parse_base64_image(sample_base64)
        print(f"   Content type: {content_type}")
        print(f"   Binary data length: {len(binary_data)} bytes")
    except Exception as e:
        print(f"   Error: {e}")
    
    # Test 3: Upload avatar (simulation - requires actual R2 bucket)
    print("\n3. Testing upload_avatar (simulation):")
    print("   Note: Actual upload requires R2 bucket to be configured")
    # Uncomment to test actual upload:
    # avatar_url = await upload_avatar(binary_data, "test_user_123", content_type)
    # print(f"   Uploaded avatar URL: {avatar_url}")
    
    # Test 4: Upload image (simulation)
    print("\n4. Testing upload_image (simulation):")
    print("   Note: Actual upload requires R2 bucket to be configured")
    # Uncomment to test actual upload:
    # image_url = await upload_image(binary_data, "test/sample.png", "card", "image/png")
    # print(f"   Uploaded image URL: {image_url}")
    
    print("\n" + "=" * 50)
    print("Test completed!")

if __name__ == "__main__":
    asyncio.run(test_r2_functionality())