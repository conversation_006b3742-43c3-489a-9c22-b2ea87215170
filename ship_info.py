async def withdraw_ship_multiple_cards(user_id: str, cards_to_withdraw: List[Dict[str, Any]], address_id: str, phone_number: str, db_client: AsyncClient) -> List[UserCard]:
    """
    Withdraw or ship multiple cards from a user's collection by creating a single withdraw request.
    The withdraw request contains fields for request date and status, and a subcollection "cards"
    that contains all withdrawn cards.
    For each card, if quantity is less than the card's quantity, only move the specified quantity.
    Only remove a card from the original subcollection if the remaining quantity is 0.

    This function also creates a shipment using the Shippo API and stores the shipping information
    in the withdraw request document.

    Args:
        user_id: The ID of the user who owns the cards
        cards_to_withdraw: List of dictionaries containing card_id, quantity, and subcollection_name for each card to withdraw
        address_id: The ID of the address to ship the cards to
        phone_number: The phone number of the recipient for shipping purposes
        db_client: Firestore client

    Returns:
        List of the updated withdrawn cards as UserCard objects

    Raises:
        HTTPException: If there's an error withdrawing the cards
    """
    try:
        # Check if user exists
        user_ref = db_client.collection(settings.firestore_collection_users).document(user_id)
        user_doc = await user_ref.get()

        if not user_doc.exists:
            raise HTTPException(status_code=404, detail=f"User with ID {user_id} not found")

        # Get user data to find the address
        user_data = user_doc.to_dict()
        user_addresses = user_data.get('addresses', [])

        # Find the address with the given ID
        shipping_address = None
        for address in user_addresses:
            if address.get('id') == address_id:
                shipping_address = address
                break

        if not shipping_address:
            raise HTTPException(status_code=404, detail=f"Address with ID {address_id} not found for user {user_id}")

        # Validate cards to withdraw
        if not cards_to_withdraw:
            raise HTTPException(status_code=400, detail="No cards specified for withdrawal")

        # Create a new withdraw request
        withdraw_requests_ref = user_ref.collection('withdraw_requests')
        new_request_ref = withdraw_requests_ref.document()  # Auto-generate ID
        request_cards_ref = new_request_ref.collection('cards')

        # Prepare data for transaction
        cards_data = []
        for card_info in cards_to_withdraw:
            card_id = card_info.get('card_id')
            quantity = card_info.get('quantity', 1)
            subcollection_name = card_info.get('subcollection_name')

            if not card_id:
                raise HTTPException(status_code=400, detail="Card ID is required for each card")

            if not subcollection_name:
                raise HTTPException(status_code=400, detail=f"Subcollection name is required for card {card_id}")

            if quantity <= 0:
                raise HTTPException(status_code=400, detail=f"Quantity must be greater than 0 for card {card_id}")

            # Get the card from the source subcollection
            source_card_ref = user_ref.collection('cards').document('cards').collection(subcollection_name).document(card_id)
            source_card_doc = await source_card_ref.get()

            if not source_card_doc.exists:
                raise HTTPException(status_code=404, detail=f"Card with ID {card_id} not found in subcollection {subcollection_name}")

            source_card_data = source_card_doc.to_dict()
            card = UserCard(**source_card_data)
            card_quantity = card.quantity

            if quantity > card_quantity:
                raise HTTPException(status_code=400, detail=f"Cannot withdraw/ship {quantity} of card {card_id}, only {card_quantity} available")

            # Get the main card reference and pre-fetch it
            main_card_ref = user_ref.collection('cards').document(card_id)
            main_card_doc = await main_card_ref.get()

            # Add card data to the list
            cards_data.append({
                'card_id': card_id,
                'quantity': quantity,
                'subcollection_name': subcollection_name,
                'source_card_ref': source_card_ref,
                'source_card_data': source_card_data,
                'remaining_quantity': card_quantity - quantity,
                'main_card_ref': main_card_ref,
                'main_card_doc': main_card_doc,
                'request_card_ref': request_cards_ref.document(card_id)
            })

        # Create transaction to process all cards
        @firestore.async_transactional
        async def create_withdraw_request(transaction):
            # Create the withdraw request document
            now = datetime.now()
            request_data = {
                'request_date': now,
                'status': 'pending',  # Initial status is pending
                'user_id': user_id,
                'created_at': now,
                'card_count': len(cards_data),  # Add count of cards in this request
                'shipping_address': shipping_address,  # Add shipping address
                'shipping_status': 'pending'  # Initial shipping status
            }
            transaction.set(new_request_ref, request_data)

            # Process each card
            for card_data in cards_data:
                card_id = card_data['card_id']
                quantity_to_withdraw = card_data['quantity']
                source_card_ref = card_data['source_card_ref']
                source_card_data = card_data['source_card_data']
                remaining_quantity = card_data['remaining_quantity']
                main_card_ref = card_data['main_card_ref']
                main_card_doc = card_data['main_card_doc']
                request_card_ref = card_data['request_card_ref']

                # Prepare the card data for the request cards subcollection
                request_card_data = source_card_data.copy()
                request_card_data['quantity'] = quantity_to_withdraw

                # Add the card to the request cards subcollection
                transaction.set(request_card_ref, request_card_data)

                if remaining_quantity <= 0:
                    # Delete the card from the source subcollection if quantity is 0
                    transaction.delete(source_card_ref)

                    # Also delete from the main cards collection if it exists
                    if main_card_doc.exists:
                        transaction.delete(main_card_ref)

                    subcollection_name = card_data['subcollection_name']
                    logger.info(f"Created withdraw request for all {quantity_to_withdraw} of card {card_id} from user {user_id}'s subcollection {subcollection_name}")
                else:
                    # Update the quantity in the source subcollection
                    transaction.update(source_card_ref, {"quantity": remaining_quantity})

                    # Also update in the main cards collection if it exists
                    if main_card_doc.exists:
                        transaction.update(main_card_ref, {"quantity": remaining_quantity})

                    subcollection_name = card_data['subcollection_name']
                    logger.info(f"Created withdraw request for {quantity_to_withdraw} of card {card_id} from user {user_id}'s subcollection {subcollection_name}, {remaining_quantity} remaining")

        # Execute the transaction
        transaction = db_client.transaction()
        await create_withdraw_request(transaction)

        # Get the updated cards from the withdraw request
        withdrawn_cards = []
        for card_data in cards_data:
            card_id = card_data['card_id']
            request_card_ref = card_data['request_card_ref']

            updated_request_card_doc = await request_card_ref.get()
            updated_request_card_data = updated_request_card_doc.to_dict()

            # Ensure ID is part of the data
            if 'id' not in updated_request_card_data:
                updated_request_card_data['id'] = card_id

            # Generate signed URL for the card image
            if 'image_url' in updated_request_card_data and updated_request_card_data['image_url']:
                try:
                    updated_request_card_data['image_url'] = await generate_signed_url(updated_request_card_data['image_url'])
                except Exception as sign_error:
                    logger.error(f"Failed to generate signed URL for {updated_request_card_data['image_url']}: {sign_error}")
                    # Keep the original URL if signing fails

            # Create a UserCard object from the updated request card data
            withdrawn_card = UserCard(
                card_reference=updated_request_card_data.get("card_reference", ""),
                card_name=updated_request_card_data.get("card_name", ""),
                date_got=updated_request_card_data.get("date_got"),
                id=updated_request_card_data.get("id", card_id),
                image_url=updated_request_card_data.get("image_url", ""),
                point_worth=updated_request_card_data.get("point_worth", 0),
                quantity=updated_request_card_data.get("quantity", 0),
                rarity=updated_request_card_data.get("rarity", 1)
            )

            # Add optional fields if they exist in the data
            if "expireAt" in updated_request_card_data:
                withdrawn_card.expireAt = updated_request_card_data["expireAt"]
            if "buybackexpiresAt" in updated_request_card_data:
                withdrawn_card.buybackexpiresAt = updated_request_card_data["buybackexpiresAt"]
            if "request_date" in updated_request_card_data:
                withdrawn_card.request_date = updated_request_card_data["request_date"]

            withdrawn_cards.append(withdrawn_card)

        # Now that the transaction is complete, create an entry in the card_shipping collection
        try:
            # Create a document in the card_shipping collection
            card_shipping_ref = db_client.collection('card_shipping').document(new_request_ref.id)

            # Get the withdraw request data
            request_doc = await new_request_ref.get()
            request_data = request_doc.to_dict()

            # Prepare the card shipping data
            card_shipping_data = {
                'request_id': new_request_ref.id,
                'user_id': user_id,
                'request_date': request_data.get('request_date'),
                'status': request_data.get('status', 'pending'),
                'shipping_address': shipping_address,
                'shipping_status': request_data.get('shipping_status', 'pending'),
                'card_count': len(cards_data),
                'created_at': datetime.now(),
                'phone_number': phone_number
            }

            # Add the cards data to the card shipping document
            cards_list = []
            for card_data in cards_data:
                card_id = card_data['card_id']
                request_card_ref = card_data['request_card_ref']
                request_card_doc = await request_card_ref.get()
                request_card_data = request_card_doc.to_dict()

                card_info = {
                    'card_id': card_id,
                    'card_reference': request_card_data.get('card_reference', ''),
                    'card_name': request_card_data.get('card_name', ''),
                    'quantity': request_card_data.get('quantity', 0),
                    'image_url': request_card_data.get('image_url', ''),
                    'rarity': request_card_data.get('rarity', 1),
                    'point_worth': request_card_data.get('point_worth', 0)
                }
                cards_list.append(card_info)

            card_shipping_data['cards'] = cards_list

            # Set the card shipping document
            await card_shipping_ref.set(card_shipping_data)

            logger.info(f"Successfully created card shipping entry for withdraw request {new_request_ref.id}")
        except Exception as e:
            logger.error(f"Error creating card shipping entry for withdraw request {new_request_ref.id}: {e}", exc_info=True)
            # Don't raise an exception here, just log the error and continue
            # The withdraw request was created successfully, but the card shipping entry creation failed

        # Now that the transaction is complete, create a shipment using Shippo API
        try:
            # Initialize the Shippo SDK with API key
            if not hasattr(settings, 'shippo_api_key') or not settings.shippo_api_key:
                logger.error("Shippo API key not configured")
                raise HTTPException(status_code=500, detail="Shipping service not configured")

            shippo_sdk = Shippo(
                api_key_header=settings.shippo_api_key
            )

            # Create a Shippo address object for the shipping address
            shippo_address = shippo_sdk.addresses.create(
                components.AddressCreateRequest(
                    name=shipping_address.get('name', ''),
                    street1=shipping_address.get('street', ''),
                    city=shipping_address.get('city', ''),
                    state=shipping_address.get('state', ''),
                    zip=shipping_address.get('zip', ''),
                    country=shipping_address.get('country', ''),
                    phone=phone_number,
                    validate=True
                )
            )

            # Create a Shippo parcel object for the package
            shippo_parcel = shippo_sdk.parcels.create(
                components.ParcelCreateRequest(
                    length="8",
                    width="6",
                    height="2",
                    distance_unit="in",
                    weight="16",
                    mass_unit="oz"
                )
            )

            # Create a Shippo shipment object
            shippo_shipment = shippo_sdk.shipments.create(
                components.ShipmentCreateRequest(
                    address_from=components.AddressCreateRequest(
                        name="Chouka Cards",
                        street1="123 Main St",
                        city="San Francisco",
                        state="CA",
                        zip="94105",
                        country="US",
                        phone="+14155559999",
                        email="<EMAIL>"
                    ),
                    address_to=components.AddressCreateRequest(
                        name=shipping_address.get('name', ''),
                        street1=shipping_address.get('street', ''),
                        city=shipping_address.get('city', ''),
                        state=shipping_address.get('state', ''),
                        zip=shipping_address.get('zip', ''),
                        country=shipping_address.get('country', ''),
                        phone=phone_number
                    ),
                    parcels=[components.ParcelCreateRequest(
                        length="6",
                        width="4",
                        height="1",
                        distance_unit="in",
                        weight="4",
                        mass_unit="oz"
                    )],
                    async_=False
                )
            )

            cheapest_rate = min(
                shippo_shipment.rates,
                key=lambda r: float(r.amount)
            )

            # Create a Shippo transaction (purchase a label)
            shippo_transaction = shippo_sdk.transactions.create(
                components.TransactionCreateRequest(
                    rate=cheapest_rate.object_id,
                    label_file_type="PDF",
                    async_=False,
                    insurance_amount=100
                )
            )

            # Update the withdraw request document with the Shippo-related information
            await new_request_ref.update({
                'shippo_address_id': shippo_address.object_id,
                'shippo_parcel_id': shippo_parcel.object_id,
                'shippo_shipment_id': shippo_shipment.object_id,
                'shippo_transaction_id': shippo_transaction.object_id,
                'shippo_label_url': shippo_transaction.label_url,
                'tracking_number': shippo_transaction.tracking_number,
                'tracking_url': shippo_transaction.tracking_url_provider,
                'shipping_status': 'label_created'
            })

            await card_shipping_ref.update({
                'shippo_address_id': shippo_address.object_id,
                'shippo_parcel_id': shippo_parcel.object_id,
                'shippo_shipment_id': shippo_shipment.object_id,
                'shippo_transaction_id': shippo_transaction.object_id,
                'shippo_label_url': shippo_transaction.label_url,
                'tracking_number': shippo_transaction.tracking_number,
                'tracking_url': shippo_transaction.tracking_url_provider,
                'shipping_status': 'label_created'
            })



            logger.info(f"Successfully created shipment for withdraw request {new_request_ref.id}")
        except Exception as e:
            logger.error(f"Error creating shipment for withdraw request {new_request_ref.id}: {e}", exc_info=True)
            # Don't raise an exception here, just log the error and continue
            # The withdraw request was created successfully, but the shipment creation failed
            # The shipment can be created manually later

        logger.info(f"Successfully created withdraw request for {len(withdrawn_cards)} cards from user {user_id}'s subcollection {subcollection_name}")
        return withdrawn_cards

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error creating withdraw request for multiple cards for user {user_id}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to create withdraw request: {str(e)}")
