# Application settings
APP_NAME="User Service API"

# Google Cloud Storage settings
GCS_PROJECT_ID="seventh-program-433718-h8"
GCS_BUCKET_NAME="user_profiles"
USER_AVATOR_BUCKET="user_avator"
QUOTA_PROJECT_ID="seventh-program-433718-h8"

# Firestore settings
FIRESTORE_PROJECT_ID="seventh-program-433718-h8"
FIRESTORE_COLLECTION_USERS="users"

# Card expiration settings (in days)
CARD_EXPIRE_DAYS=10
CARD_BUYBACK_EXPIRE_DAYS=7

# Backend service URLs
STORAGE_SERVICE_URL="http://0.0.0.0:8080"

# Stripe API settings
STRIPE_API_KEY="sk_test_51RRaUk4STmMQIYMZkwfGsWvGFIEC6gW4zu6KXq56iUSeTmNmSDsa0zrdNO8KNTGL5YwWRM6sOurnc8tNww2o2aOM00ezEKdaKK"
STRIPE_WEBHOOK_SECRET="whsec_LnLqqTmn7K1Pth2AZqyNPfOnKlQWe2Zx"
STRIPE_CONNECT_WEBHOOK_SECRET="whsec_LlS2wSOyBSGqgYrBQqlyGGtacOM0Y9Ly"

#Shippo API settings
SHIPPO_API_KEY="shippo_test_f722395e8ea267c139451e2d063d6cc76eea8e58"

#mailgun api
MAILGUN_API="**************************************************"

# Database connection settings
DB_INSTANCE_CONNECTION_NAME="seventh-program-433718-h8:us-central1:test"
DB_USER="test"
DB_PASS="Qjh19981201!"
DB_NAME="test"
DB_PORT=5432

# Logging settings
LOG_LEVEL="INFO"

#development
DEVELOPMENT_MODE="True"

# Typesense settings
TYPESENSE_API_KEY="68vTuaLCLRTBDmpEpnCy0cMSHP0oC2Kc"
TYPESENSE_HOST="norguvx0y71jmcsdp-1.a1.typesense.net"
TYPESENSE_PORT="443"
TYPESENSE_PROTOCOL="https"
TYPESENSE_COLLECTION_LISTINGS="listings_test"


#firebase config:
FIREBASE_API_KEY=AIzaSyBfbEQUIGs-0rGMMw2GLFkcq6EvlG4ID40
FIREBASE_AUTH_DOMAIN=seventh-program-433718-h8.firebaseapp.com
FIREBASE_PROJECT_ID=seventh-program-433718-h8
FIREBASE_STORAGE_BUCKET=seventh-program-433718-h8.firebasestorage.app
FIREBASE_MESSAGING_SENDER_ID=351785787544
FIREBASE_APP_ID=1:351785787544:web:eeb0ca41aa9ffa0354f0ed
FIREBASE_MEASUREMENT_ID=G-X53524FJ7B

# Cloudflare R2 Storage settings
R2_ACCESS_KEY_ID="4efb72bd0c675eaef34bcac66113062f"
R2_SECRET_ACCESS_KEY="9c4df4f314dd939cf0277c4d962db9b6c7aaceb1284ccfb5687654ad69909a1c"
R2_ENDPOINT="https://10c5a6603c2479b5ee449179b5877db5.r2.cloudflarestorage.com"

# R2 Bucket names
R2_BUCKET_ACHIEVEMENT="achievement-dev"
R2_BUCKET_PACK="pack-dev"
R2_BUCKET_CARD="card-dev"
R2_BUCKET_AVATAR="avator-dev"