from fastapi import APIRouter, HTTPException, Depends, Path, Query
from typing import List, Optional, Dict, Any
from google.cloud import firestore
from datetime import datetime, timedelta

from models.schemas import RankEntry, LevelRankEntry
from service.rank_service import get_weekly_spending_rank, get_top_level_users
from config import get_firestore_client, get_logger

logger = get_logger(__name__)

router = APIRouter(
    prefix="/rank",
    tags=["rank"],
)

@router.get("/weekly_spent/weekly_spent", response_model=Dict[str, Any])
async def get_weekly_spending_rank_route(
    page: int = Query(1, description="The page number (default: 1)"),
    per_page: int = Query(10, description="The number of items per page (default: 10)"),
    user_id: Optional[str] = Query(None, description="Optional user ID to get their rank and spent amount"),
    db: firestore.AsyncClient = Depends(get_firestore_client)
):
    """
    Get the top 100 users by weekly spending for the current week with pagination.

    This endpoint:
    1. Automatically calculates the current week ID (no input required)
    2. Returns a dictionary containing:
       - 'rankings': List of RankEntry objects with user_id, spent, display_name, and avatar (paginated from top 100)
       - 'pagination': Dict with 'page', 'per_page', 'total_items', 'total_pages'
       - 'user_rank': Optional dict with 'rank' and 'spent' if user_id is provided

    The week ID is calculated as the start of the week (Monday) using:
    ```python
    today = datetime.now()
    start_of_week = today - timedelta(days=today.weekday())
    week_id = start_of_week.strftime("%Y-%m-%d")
    ```
    """
    try:
        # Get the top users by weekly spending for the current week
        rank_data = await get_weekly_spending_rank(db, None, page, per_page, user_id)

        # Return the data directly without converting to strings
        # This will include user_id, spent, display_name, and avatar for each entry
        return rank_data
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting weekly spending rank: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="An error occurred while getting the weekly spending rank")

@router.get("/top_level", response_model=Dict[str, Any])
async def get_top_level_users_route(
    page: int = Query(1, description="The page number (default: 1)"),
    per_page: int = Query(10, description="The number of items per page (default: 10)"),
    user_id: Optional[str] = Query(None, description="Optional user ID to get their rank and level"),
    db: firestore.AsyncClient = Depends(get_firestore_client)
):
    """
    Get the top 100 users by level, sorted by total_drawn with pagination.

    This endpoint:
    1. Queries the users collection
    2. Orders the users by total_drawn in descending order
    3. Returns a dictionary containing:
       - 'rankings': List of LevelRankEntry objects with user_id, total_drawn, level, display_name, and avatar (paginated from top 100)
       - 'pagination': Dict with 'page', 'per_page', 'total_items', 'total_pages'
       - 'user_rank': Optional dict with 'rank' and 'level' if user_id is provided
    """
    try:
        # Get the top users by level
        rank_data = await get_top_level_users(db, page, per_page, user_id)

        return rank_data
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting top level users: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="An error occurred while getting the top level users")
