# Application settings
APP_NAME=chouka-user-backend

# Google Cloud Storage settings (deprecated - migrating to R2)
GCS_PROJECT_ID=your-project-id
GCS_BUCKET_NAME=your-bucket-name
USER_AVATOR_BUCKET=your-avatar-bucket

# Cloudflare R2 Storage settings
R2_ACCESS_KEY_ID=your-r2-access-key-id
R2_SECRET_ACCESS_KEY=your-r2-secret-access-key
R2_ENDPOINT=https://your-account-id.r2.cloudflarestorage.com

# R2 Bucket names
R2_BUCKET_ACHIEVEMENT=achievement-dev
R2_BUCKET_PACK=pack-dev
R2_BUCKET_CARD=card-dev
R2_BUCKET_AVATAR=avator-dev

# R2 Public domains (for generating public URLs)
# Dev defaults below; set to production domains in Cloud Run (e.g., achievement.zapull.fun, pack.zapull.fun, card.zapull.fun, avatar.zapull.fun)
R2_PUBLIC_DOMAIN_ACHIEVEMENT=achievement-dev.zapull.fun
R2_PUBLIC_DOMAIN_PACK=pack-dev.zapull.fun
R2_PUBLIC_DOMAIN_CARD=card-dev.zapull.fun
R2_PUBLIC_DOMAIN_AVATAR=avator-dev.zapull.fun

# Firestore settings
FIRESTORE_PROJECT_ID=your-firestore-project-id
FIRESTORE_COLLECTION_USERS=users
QUOTA_PROJECT_ID=your-quota-project-id

# Card expiration settings (in days)
CARD_EXPIRE_DAYS=30
CARD_BUYBACK_EXPIRE_DAYS=7

# Backend service URLs
STORAGE_SERVICE_URL=http://localhost:8080

# Stripe API settings
STRIPE_API_KEY=your-stripe-api-key
STRIPE_WEBHOOK_SECRET=your-stripe-webhook-secret
STRIPE_CONNECT_WEBHOOK_SECRET=your-stripe-connect-webhook-secret

# Development settings
DEVELOPMENT_MODE=true
# Enable API documentation (Swagger/OpenAPI) - set to false in production for security
ENABLE_DOCS=true

# Typesense settings
TYPESENSE_API_KEY=your-typesense-api-key
TYPESENSE_HOST=your-typesense-host
TYPESENSE_PORT=443
TYPESENSE_PROTOCOL=https
TYPESENSE_COLLECTION_LISTINGS=listings_test

# Shippo API KEY
SHIPPO_API_KEY=your-shippo-api-key

# Mailgun API
MAILGUN_API=your-mailgun-api

# Database connection settings
DB_INSTANCE_CONNECTION_NAME=your-db-instance-connection
DB_USER=your-db-user
DB_PASS=your-db-password
DB_NAME=your-db-name
DB_PORT=5432

# Logging settings
LOG_LEVEL=INFO

# Firebase settings (Development)
FIREBASE_API_KEY=AIzaSyBfbEQUIGs-0rGMMw2GLFkcq6EvlG4ID40
FIREBASE_AUTH_DOMAIN=seventh-program-433718-h8.firebaseapp.com
FIREBASE_PROJECT_ID=seventh-program-433718-h8
FIREBASE_STORAGE_BUCKET=seventh-program-433718-h8.firebasestorage.app
FIREBASE_MESSAGING_SENDER_ID=351785787544
FIREBASE_APP_ID=1:351785787544:web:eeb0ca41aa9ffa0354f0ed
FIREBASE_MEASUREMENT_ID=G-X53524FJ7B