<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Firebase Token Generator for Swagger</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .token-container {
            margin-top: 20px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 4px;
            border: 1px solid #ddd;
            display: none;
        }
        .token-container.show {
            display: block;
        }
        .token-text {
            word-break: break-all;
            background-color: #fff;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .copy-button {
            background-color: #008CBA;
            margin-top: 10px;
        }
        .copy-button:hover {
            background-color: #007399;
        }
        .error {
            color: #d32f2f;
            margin-top: 10px;
            padding: 10px;
            background-color: #ffebee;
            border-radius: 4px;
            display: none;
        }
        .error.show {
            display: block;
        }
        .success {
            color: #388e3c;
            margin-top: 10px;
            padding: 10px;
            background-color: #e8f5e9;
            border-radius: 4px;
        }
        .instructions {
            margin-top: 20px;
            padding: 15px;
            background-color: #e3f2fd;
            border-radius: 4px;
            border-left: 4px solid #2196f3;
        }
        .instructions h3 {
            margin-top: 0;
            color: #1976d2;
        }
        .instructions ol {
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Firebase Token Generator</h1>
        <p style="text-align: center; color: #666;">Generate a Firebase ID token for testing authenticated endpoints in Swagger UI</p>
        
        <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" id="email" placeholder="<EMAIL>" required>
        </div>
        
        <div class="form-group">
            <label for="password">Password:</label>
            <input type="password" id="password" placeholder="Enter your password" required>
        </div>
        
        <button id="signInButton" onclick="signIn()">Sign In and Get Token</button>
        
        <div id="error" class="error"></div>
        
        <div id="tokenContainer" class="token-container">
            <div class="success">
                ✓ Token generated successfully! (Valid for 1 hour)
            </div>
            <h3>Your Firebase ID Token:</h3>
            <div id="tokenText" class="token-text"></div>
            <button class="copy-button" onclick="copyToken()">Copy to Clipboard</button>
        </div>
        
        <div class="instructions">
            <h3>How to use in Swagger UI:</h3>
            <ol>
                <li>Copy the token above</li>
                <li>Go to <a href="/users/api/v1/docs" target="_blank">Swagger Documentation</a></li>
                <li>Click the "Authorize" button (🔒)</li>
                <li>Paste the token in the "Value" field</li>
                <li>Click "Authorize" then "Close"</li>
                <li>Now you can test authenticated endpoints!</li>
            </ol>
        </div>
    </div>

    <!-- Firebase SDKs -->
    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getAuth, signInWithEmailAndPassword } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';
        
        // Your Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBfbEQUIGs-0rGMMw2GLFkcq6EvlG4ID40",
            authDomain: "seventh-program-433718-h8.firebaseapp.com",
            projectId: "seventh-program-433718-h8",
            storageBucket: "seventh-program-433718-h8.firebasestorage.app",
            messagingSenderId: "351785787544",
            appId: "1:351785787544:web:eeb0ca41aa9ffa0354f0ed"
        };
        
        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        
        // Make functions available globally
        window.signIn = async function() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const button = document.getElementById('signInButton');
            const errorDiv = document.getElementById('error');
            const tokenContainer = document.getElementById('tokenContainer');
            const tokenText = document.getElementById('tokenText');
            
            // Reset states
            errorDiv.classList.remove('show');
            tokenContainer.classList.remove('show');
            button.disabled = true;
            button.textContent = 'Signing in...';
            
            try {
                // Sign in the user
                const userCredential = await signInWithEmailAndPassword(auth, email, password);
                const user = userCredential.user;
                
                // Get the ID token
                const idToken = await user.getIdToken();
                
                // Display the token
                tokenText.textContent = idToken;
                tokenContainer.classList.add('show');
                
                // Store in sessionStorage for convenience
                sessionStorage.setItem('firebaseIdToken', idToken);
                
                button.textContent = 'Sign In and Get Token';
                button.disabled = false;
            } catch (error) {
                errorDiv.textContent = `Error: ${error.message}`;
                errorDiv.classList.add('show');
                button.textContent = 'Sign In and Get Token';
                button.disabled = false;
            }
        };
        
        window.copyToken = function() {
            const tokenText = document.getElementById('tokenText').textContent;
            navigator.clipboard.writeText(tokenText).then(() => {
                const button = event.target;
                const originalText = button.textContent;
                button.textContent = 'Copied!';
                setTimeout(() => {
                    button.textContent = originalText;
                }, 2000);
            }).catch(err => {
                alert('Failed to copy token: ' + err);
            });
        };
        
        // Allow Enter key to submit
        document.getElementById('password').addEventListener('keypress', function(event) {
            if (event.key === 'Enter') {
                window.signIn();
            }
        });
    </script>
</body>
</html>