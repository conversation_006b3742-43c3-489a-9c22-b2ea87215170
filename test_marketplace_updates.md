# Marketplace Admin Updates Test Checklist

## Changes Made:

### 1. **编辑 (Edit) Button** - Enhanced Functionality
   - Now allows editing both **数量 (Quantity)** and **价格 (Price)**
   - Opens a drawer with two input fields:
     - 数量 (Quantity) - minimum value of 1
     - 积分价格 (Points Price) - minimum value of 0

### 2. **修改数量 (Modify Quantity) Button** - REMOVED
   - This separate button has been completely removed
   - Quantity editing is now integrated into the main Edit function

### 3. **撤下商品 (Delist/Withdraw Item) Button** - NEW
   - Added a new red "撤下商品" button next to Edit
   - Shows a confirmation dialog before delisting
   - Calls the DELETE `/marketplace/official_listing` endpoint
   - Removes the item completely from the official marketplace

## Files Modified:

1. `/zapull-admin/src/views/marketplace/index.vue`
   - Updated the operations column from 280px to 200px width
   - Removed "修改数量" button
   - Added "撤下商品" button with danger styling
   - Enhanced edit dialog to include quantity field
   - Removed separate quantity dialog
   - Added withdraw confirmation logic

2. `/zapull-admin/src/api/marketplace.ts`
   - Added `withdrawMarketplaceItem` function
   - Added `WithdrawMarketplaceItemParams` interface
   - Function calls DELETE endpoint with query parameters

## Backend Endpoints Used:

1. **Update Price**: `PUT /marketplace/official_listing`
   - Parameters: collection_id, card_id, pricePoints

2. **Update Quantity**: `POST /marketplace/official_listing`
   - Parameters: collection_id, card_id, quantity

3. **Withdraw Item**: `DELETE /marketplace/official_listing`
   - Parameters: collection_id, card_id
   - Returns quantity back to original card inventory

## Testing Steps:

1. Navigate to the marketplace admin page
2. Select a card collection
3. Test Edit button:
   - Click 编辑 on any item
   - Verify both 数量 and 积分价格 fields appear
   - Change values and save
   - Verify the list updates

4. Test Withdraw button:
   - Click 撤下商品 on any item
   - Verify confirmation dialog appears
   - Confirm the action
   - Verify item is removed from the list

5. Verify the "修改数量" button no longer appears

## Notes:
- The edit operation performs two API calls when both quantity and price are changed
- The withdraw operation includes a safety confirmation to prevent accidental deletes
- All operations refresh the marketplace list after completion