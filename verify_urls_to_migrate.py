#!/usr/bin/env python3
"""
Verify and list all URLs that need to be migrated from GCS to R2.
This script checks all collections for GCS URLs and provides a summary.
"""

import asyncio
import re
from typing import Dict, List, Any, Optional
from google.cloud import firestore
from google.oauth2 import service_account
import json
import os

# Initialize Firestore client
def get_firestore_client():
    # Check if running in production (Cloud Run)
    if os.getenv('K_SERVICE'):
        # Use default credentials in Cloud Run
        return firestore.Client()
    else:
        # Use service account for local development
        creds_path = os.getenv('GOOGLE_APPLICATION_CREDENTIALS')
        if creds_path and os.path.exists(creds_path):
            credentials = service_account.Credentials.from_service_account_file(creds_path)
            return firestore.Client(credentials=credentials)
        else:
            return firestore.Client()

db = get_firestore_client()

# Print which project we're connected to
print(f"Connected to Firestore project: {db.project}")
print(f"Using credentials: {os.getenv('GOOGLE_APPLICATION_CREDENTIALS', 'default credentials')}")
print()

def is_gcs_url(url: str) -> bool:
    """Check if a URL is a GCS URL that needs migration."""
    if not url:
        return False
    
    # Check for GCS patterns
    if url.startswith('gs://'):
        return True
    if 'storage.googleapis.com' in url:
        return True
    
    return False

def extract_bucket_from_url(url: str) -> Optional[str]:
    """Extract bucket name from GCS URL."""
    if not url:
        return None
    
    # Pattern 1: gs://bucket/path
    gs_match = re.match(r'gs://([^/]+)/', url)
    if gs_match:
        return gs_match.group(1)
    
    # Pattern 2: https://storage.googleapis.com/bucket/path
    https_match = re.match(r'https://storage\.googleapis\.com/([^/]+)/', url)
    if https_match:
        return https_match.group(1)
    
    return None

async def check_collection_for_urls(collection_name: str, field_names: List[str]) -> Dict[str, Any]:
    """Check a collection for GCS URLs in specified fields."""
    results = {
        'collection': collection_name,
        'total_docs': 0,
        'docs_with_gcs_urls': 0,
        'gcs_urls_by_bucket': {},
        'sample_urls': []
    }
    
    collection_ref = db.collection(collection_name)
    docs = collection_ref.stream()
    
    doc_count = 0
    for doc in docs:
        doc_count += 1
        results['total_docs'] += 1
        doc_data = doc.to_dict()
        has_gcs_url = False
        
        if doc_data:
            for field in field_names:
                if field in doc_data:
                    url = doc_data[field]
                    if is_gcs_url(url):
                        has_gcs_url = True
                        bucket = extract_bucket_from_url(url) or 'unknown'
                        
                        if bucket not in results['gcs_urls_by_bucket']:
                            results['gcs_urls_by_bucket'][bucket] = 0
                        results['gcs_urls_by_bucket'][bucket] += 1
                        
                        # Store sample URLs (limit to 5 per collection)
                        if len(results['sample_urls']) < 5:
                            results['sample_urls'].append({
                                'doc_id': doc.id,
                                'field': field,
                                'url': url,
                                'bucket': bucket
                            })
        
        if has_gcs_url:
            results['docs_with_gcs_urls'] += 1
    
    print(f"  Found {doc_count} documents in {collection_name}")
    return results

async def check_card_collections() -> List[Dict[str, Any]]:
    """Check pokemon, one_piece, and magic collections with their nested card structure."""
    collections_to_check = ['pokemon', 'one_piece', 'magic']
    all_results = []
    
    for collection_name in collections_to_check:
        results = {
            'collection': collection_name,
            'total_docs': 0,
            'docs_with_gcs_urls': 0,
            'gcs_urls_by_bucket': {},
            'sample_urls': []
        }
        
        print(f"Checking {collection_name}...")
        
        # Get all documents (types like pokemon types, one piece types, etc)
        collection_ref = db.collection(collection_name)
        type_docs = collection_ref.stream()
        
        type_count = 0
        for type_doc in type_docs:
            type_count += 1
            type_id = type_doc.id
            type_data = type_doc.to_dict()
            
            # The type document itself is a card with image_url
            if type_data and 'image_url' in type_data:
                results['total_docs'] += 1
                url = type_data['image_url']
                if is_gcs_url(url):
                    results['docs_with_gcs_urls'] += 1
                    bucket = extract_bucket_from_url(url) or 'unknown'
                    
                    if bucket not in results['gcs_urls_by_bucket']:
                        results['gcs_urls_by_bucket'][bucket] = 0
                    results['gcs_urls_by_bucket'][bucket] += 1
                    
                    # Store sample URLs
                    if len(results['sample_urls']) < 5:
                        results['sample_urls'].append({
                            'path': f"{collection_name}/{type_id}",
                            'field': 'image_url',
                            'url': url,
                            'bucket': bucket
                        })
            
            # Also check if there are cards in a subcollection
            cards_ref = type_doc.reference.collection('cards')
            cards = cards_ref.stream()
            
            card_count = 0
            for card_doc in cards:
                card_count += 1
                results['total_docs'] += 1
                card_data = card_doc.to_dict()
                
                if card_data and 'image_url' in card_data:
                    url = card_data['image_url']
                    if is_gcs_url(url):
                        results['docs_with_gcs_urls'] += 1
                        bucket = extract_bucket_from_url(url) or 'unknown'
                        
                        if bucket not in results['gcs_urls_by_bucket']:
                            results['gcs_urls_by_bucket'][bucket] = 0
                        results['gcs_urls_by_bucket'][bucket] += 1
                        
                        # Store sample URLs
                        if len(results['sample_urls']) < 5:
                            results['sample_urls'].append({
                                'path': f"{collection_name}/{type_id}/cards/{card_doc.id}",
                                'field': 'image_url',
                                'url': url,
                                'bucket': bucket
                            })
            
            if card_count > 0:
                print(f"  Type {type_id}: has {card_count} cards in subcollection")
        
        print(f"  Total types: {type_count}, Total cards: {results['total_docs']}")
        all_results.append(results)
    
    return all_results

async def check_achievements() -> Dict[str, Any]:
    """Check achievements collection and user achievements."""
    results = {
        'collection': 'achievements',
        'total_docs': 0,
        'docs_with_gcs_urls': 0,
        'gcs_urls_by_bucket': {},
        'sample_urls': []
    }
    
    # Check main achievements collection
    collection_ref = db.collection('achievements')
    docs = collection_ref.stream()
    
    for doc in docs:
        results['total_docs'] += 1
        doc_data = doc.to_dict()
        
        if doc_data:
            # Check image_url field
            if 'image_url' in doc_data and is_gcs_url(doc_data['image_url']):
                results['docs_with_gcs_urls'] += 1
                bucket = extract_bucket_from_url(doc_data['image_url']) or 'unknown'
                
                if bucket not in results['gcs_urls_by_bucket']:
                    results['gcs_urls_by_bucket'][bucket] = 0
                results['gcs_urls_by_bucket'][bucket] += 1
                
                if len(results['sample_urls']) < 3:
                    results['sample_urls'].append({
                        'doc_id': doc.id,
                        'field': 'image_url',
                        'url': doc_data['image_url'],
                        'bucket': bucket
                    })
            
            # Check reward array for emblem URLs
            if 'reward' in doc_data and isinstance(doc_data['reward'], list):
                for reward in doc_data['reward']:
                    if isinstance(reward, dict) and reward.get('type') == 'emblem':
                        emblem_url = reward.get('url')
                        if emblem_url and is_gcs_url(emblem_url):
                            bucket = extract_bucket_from_url(emblem_url) or 'unknown'
                            
                            if bucket not in results['gcs_urls_by_bucket']:
                                results['gcs_urls_by_bucket'][bucket] = 0
                            results['gcs_urls_by_bucket'][bucket] += 1
                            
                            if len(results['sample_urls']) < 5:
                                results['sample_urls'].append({
                                    'doc_id': doc.id,
                                    'field': 'reward.emblem.url',
                                    'url': emblem_url,
                                    'bucket': bucket
                                })
    
    return results

async def main():
    """Main verification function."""
    print("URL Migration Verification Script")
    print("=================================")
    print("\nChecking all collections for GCS URLs that need migration...\n")
    
    all_results = []
    
    # Collections to check with their image URL fields
    collections_to_check = [
        ('cards', ['image_url']),
        ('listings', ['image_url']),
        ('users', ['avatarUrl']),
    ]
    
    # Check standard collections
    for collection_name, fields in collections_to_check:
        print(f"Checking {collection_name}...")
        result = await check_collection_for_urls(collection_name, fields)
        all_results.append(result)
    
    # Check pokemon, one_piece, and magic collections (nested structure)
    card_results = await check_card_collections()
    all_results.extend(card_results)
    
    # Check achievements
    print("Checking achievements...")
    achievements_result = await check_achievements()
    all_results.append(achievements_result)
    
    # Print summary
    print("\n" + "="*80)
    print("VERIFICATION SUMMARY")
    print("="*80)
    
    total_gcs_urls = 0
    all_buckets = {}
    
    for result in all_results:
        if result['docs_with_gcs_urls'] > 0:
            print(f"\n{result['collection']}:")
            print(f"  Total documents: {result['total_docs']}")
            print(f"  Documents with GCS URLs: {result['docs_with_gcs_urls']}")
            print(f"  Buckets found:")
            
            for bucket, count in result['gcs_urls_by_bucket'].items():
                print(f"    - {bucket}: {count} URLs")
                if bucket not in all_buckets:
                    all_buckets[bucket] = 0
                all_buckets[bucket] += count
                total_gcs_urls += count
            
            if result['sample_urls']:
                print(f"  Sample URLs:")
                for sample in result['sample_urls'][:3]:
                    print(f"    - {sample.get('doc_id', sample.get('path', 'unknown'))}: {sample['url'][:100]}...")
    
    # Print overall summary
    print("\n" + "="*80)
    print("OVERALL SUMMARY")
    print("="*80)
    print(f"\nTotal GCS URLs to migrate: {total_gcs_urls}")
    print("\nAll GCS buckets found:")
    for bucket, count in sorted(all_buckets.items(), key=lambda x: x[1], reverse=True):
        print(f"  - {bucket}: {count} URLs")
    
    # Bucket mapping preview
    print("\n" + "="*80)
    print("RECOMMENDED BUCKET MAPPINGS")
    print("="*80)
    print("\nGCS Bucket → R2 Bucket:")
    
    bucket_mapping = {
        'zapull-achievement': 'achievement.zapull.fun',
        'zapull-packs': 'pack.zapull.fun',
        'zapull-cards': 'card.zapull.fun',
        'zapull-avatar': 'avatar.zapull.fun',
        'zapull-avator': 'avatar.zapull.fun',  # Fix typo
        'pack_covers': 'pack.zapull.fun',
        'pack_covers_production': 'pack.zapull.fun',
        'pokemon_cards_pull': 'card.zapull.fun',
        'pokemon_cards_pull_production': 'card.zapull.fun',
    }
    
    for gcs_bucket, r2_bucket in bucket_mapping.items():
        if gcs_bucket in all_buckets:
            print(f"  {gcs_bucket} → {r2_bucket} ({all_buckets[gcs_bucket]} URLs)")
    
    # Unknown buckets
    unknown_buckets = [b for b in all_buckets.keys() if b not in bucket_mapping and b != 'unknown']
    if unknown_buckets:
        print("\nUnmapped buckets (need mapping):")
        for bucket in unknown_buckets:
            print(f"  - {bucket}: {all_buckets[bucket]} URLs")

if __name__ == "__main__":
    asyncio.run(main())