#!/usr/bin/env python3
"""
Migrate URLs from Google Cloud Storage to Cloudflare R2.
This script updates URLs in various Firestore collections.
"""

import asyncio
import re
from typing import Dict, Any, Optional, List
from google.cloud import firestore
from google.oauth2 import service_account
import json
import os

# Initialize Firestore client
def get_firestore_client():
    # Check if running in production (Cloud Run)
    if os.getenv('K_SERVICE'):
        # Use default credentials in Cloud Run
        return firestore.Client()
    else:
        # Use service account for local development
        creds_path = os.getenv('GOOGLE_APPLICATION_CREDENTIALS')
        if creds_path and os.path.exists(creds_path):
            credentials = service_account.Credentials.from_service_account_file(creds_path)
            return firestore.Client(credentials=credentials)
        else:
            return firestore.Client()

db = get_firestore_client()

def extract_r2_url(url: str) -> Optional[str]:
    """
    Extract R2 URL from various URL formats.
    Returns None if the URL should not be migrated.
    """
    if not url:
        return None
    
    # Skip if already an R2 URL
    if 'zapull.fun' in url:
        return None
    
    # Extract bucket and path from GCS URLs
    # Pattern 1: gs://bucket/path
    gs_match = re.match(r'gs://([^/]+)/(.+)', url)
    if gs_match:
        bucket = gs_match.group(1)
        path = gs_match.group(2)
        
        # Map GCS buckets to R2 buckets (without -dev suffix)
        bucket_mapping = {
            'zapull-achievement': 'achievement.zapull.fun',
            'zapull-packs': 'pack.zapull.fun',
            'zapull-cards': 'card.zapull.fun',
            'zapull-avatar': 'avatar.zapull.fun',
            'zapull-avator': 'avatar.zapull.fun',  # Fix typo
            'pack_covers': 'pack.zapull.fun',
            'pack_covers_production': 'pack.zapull.fun',
            'pokemon_cards_pull': 'card.zapull.fun',
            'pokemon_cards_pull_production': 'card.zapull.fun',
        }
        
        r2_bucket = bucket_mapping.get(bucket)
        if r2_bucket:
            return f"https://{r2_bucket}/{path}"
    
    # Pattern 2: https://storage.googleapis.com/bucket/path (with or without query params)
    gcs_https_match = re.match(r'https://storage\.googleapis\.com/([^/]+)/([^?]+)', url)
    if gcs_https_match:
        bucket = gcs_https_match.group(1)
        path = gcs_https_match.group(2)
        
        # Map GCS buckets to R2 buckets (without -dev suffix)
        bucket_mapping = {
            'zapull-achievement': 'achievement.zapull.fun',
            'zapull-packs': 'pack.zapull.fun',
            'zapull-cards': 'card.zapull.fun',
            'zapull-avatar': 'avatar.zapull.fun',
            'zapull-avator': 'avatar.zapull.fun',  # Fix typo
            'pack_covers': 'pack.zapull.fun',
            'pack_covers_production': 'pack.zapull.fun',
            'pokemon_cards_pull': 'card.zapull.fun',
            'pokemon_cards_pull_production': 'card.zapull.fun',
        }
        
        r2_bucket = bucket_mapping.get(bucket)
        if r2_bucket:
            return f"https://{r2_bucket}/{path}"
    
    return None

async def migrate_collection(collection_name: str, field_names: List[str]) -> Dict[str, int]:
    """Migrate URLs in a collection."""
    stats = {
        'total_docs': 0,
        'migrated_docs': 0,
        'migrated_urls': 0,
        'errors': 0
    }
    
    print(f"\n{'='*50}")
    print(f"Migrating {collection_name}...")
    print(f"{'='*50}")
    
    collection_ref = db.collection(collection_name)
    docs = collection_ref.stream()
    
    for doc in docs:
        stats['total_docs'] += 1
        doc_data = doc.to_dict()
        updates = {}
        
        if doc_data:
            for field in field_names:
                if field in doc_data:
                    old_url = doc_data[field]
                    new_url = extract_r2_url(old_url)
                    
                    if new_url:
                        updates[field] = new_url
                        print(f"  Doc {doc.id} - {field}:")
                        print(f"    From: {old_url}")
                        print(f"    To: {new_url}")
        
        if updates:
            try:
                doc.reference.update(updates)
                stats['migrated_docs'] += 1
                stats['migrated_urls'] += len(updates)
            except Exception as e:
                print(f"  ERROR updating doc {doc.id}: {e}")
                stats['errors'] += 1
    
    print(f"\nMigrated {stats['migrated_urls']} URLs in {stats['migrated_docs']} documents")
    return stats

async def migrate_card_collections() -> Dict[str, int]:
    """Migrate pokemon, one_piece, and magic collections URLs."""
    stats = {
        'total_docs': 0,
        'migrated_docs': 0,
        'migrated_urls': 0,
        'errors': 0
    }
    
    collections_to_migrate = ['pokemon', 'one_piece', 'magic']
    
    for collection_name in collections_to_migrate:
        print(f"\n{'='*50}")
        print(f"Migrating {collection_name} collection...")
        print(f"{'='*50}")
        
        collection_ref = db.collection(collection_name)
        type_docs = collection_ref.stream()
        
        for type_doc in type_docs:
            type_id = type_doc.id
            type_data = type_doc.to_dict()
            
            # The type document itself is a card with image_url
            if type_data and 'image_url' in type_data:
                stats['total_docs'] += 1
                old_url = type_data['image_url']
                new_url = extract_r2_url(old_url)
                
                if new_url:
                    try:
                        type_doc.reference.update({'image_url': new_url})
                        print(f"  {collection_name}/{type_id}:")
                        print(f"    From: {old_url}")
                        print(f"    To: {new_url}")
                        stats['migrated_urls'] += 1
                        stats['migrated_docs'] += 1
                    except Exception as e:
                        print(f"  ERROR updating {collection_name}/{type_id}: {e}")
                        stats['errors'] += 1
            
            # Also check if there are cards in a subcollection
            cards_ref = type_doc.reference.collection('cards')
            cards = cards_ref.stream()
            
            cards_migrated = 0
            for card_doc in cards:
                stats['total_docs'] += 1
                card_id = card_doc.id
                card_data = card_doc.to_dict()
                
                if card_data and 'image_url' in card_data:
                    old_url = card_data['image_url']
                    new_url = extract_r2_url(old_url)
                    
                    if new_url:
                        try:
                            card_doc.reference.update({'image_url': new_url})
                            print(f"  {collection_name}/{type_id}/cards/{card_id}:")
                            print(f"    From: {old_url}")
                            print(f"    To: {new_url}")
                            stats['migrated_urls'] += 1
                            stats['migrated_docs'] += 1
                            cards_migrated += 1
                        except Exception as e:
                            print(f"  ERROR updating card {card_id}: {e}")
                            stats['errors'] += 1
                
            if cards_migrated > 0:
                print(f"  Migrated {cards_migrated} cards in {type_id} subcollection")
    
    print(f"\nTotal migrated: {stats['migrated_urls']} URLs in {stats['migrated_docs']} documents")
    return stats

async def migrate_achievements() -> Dict[str, int]:
    """Migrate achievements collection URLs."""
    stats = {
        'total_docs': 0,
        'migrated_docs': 0,
        'migrated_urls': 0,
        'errors': 0
    }
    
    print(f"\n{'='*50}")
    print(f"Migrating achievements...")
    print(f"{'='*50}")
    
    collection_ref = db.collection('achievements')
    docs = collection_ref.stream()
    
    for doc in docs:
        stats['total_docs'] += 1
        doc_data = doc.to_dict()
        updates = {}
        urls_migrated = 0
        
        if doc_data:
            # Check image_url
            if 'image_url' in doc_data:
                old_url = doc_data['image_url']
                new_url = extract_r2_url(old_url)
                
                if new_url:
                    updates['image_url'] = new_url
                    print(f"  Doc {doc.id} - image_url:")
                    print(f"    From: {old_url}")
                    print(f"    To: {new_url}")
                    urls_migrated += 1
            
            # Check reward array for emblem URLs
            if 'reward' in doc_data and isinstance(doc_data['reward'], list):
                new_rewards = []
                reward_updated = False
                
                for reward in doc_data['reward']:
                    if isinstance(reward, dict) and reward.get('type') == 'emblem':
                        emblem_url = reward.get('url')
                        if emblem_url:
                            new_url = extract_r2_url(emblem_url)
                            if new_url:
                                reward['url'] = new_url
                                print(f"  Doc {doc.id} - reward.emblem.url:")
                                print(f"    From: {emblem_url}")
                                print(f"    To: {new_url}")
                                urls_migrated += 1
                                reward_updated = True
                    new_rewards.append(reward)
                
                if reward_updated:
                    updates['reward'] = new_rewards
        
        if updates:
            try:
                doc.reference.update(updates)
                stats['migrated_docs'] += 1
                stats['migrated_urls'] += urls_migrated
            except Exception as e:
                print(f"  ERROR updating doc {doc.id}: {e}")
                stats['errors'] += 1
    
    print(f"\nMigrated {stats['migrated_urls']} URLs in {stats['migrated_docs']} documents")
    return stats

async def main():
    """Main migration function."""
    print("URL Migration Script")
    print("===================")
    print("\nThis script will migrate URLs from GCS to R2.")
    print("\nOptions:")
    print("1. Migrate all collections")
    print("2. Migrate specific collection")
    print("3. Dry run (show what would be migrated)")
    print("4. Exit")
    
    choice = input("\nEnter your choice (1-4): ")
    
    if choice == '1':
        print("\nStarting full migration...")
        
        all_stats = {
            'total_docs': 0,
            'migrated_docs': 0,
            'migrated_urls': 0,
            'errors': 0
        }
        
        # Migrate standard collections
        collections = [
            ('cards', ['image_url']),
            ('listings', ['image_url']),
            ('users', ['avatarUrl']),
        ]
        
        for collection_name, fields in collections:
            stats = await migrate_collection(collection_name, fields)
            for key in all_stats:
                all_stats[key] += stats[key]
        
        # Migrate pokemon, one_piece, and magic collections
        stats = await migrate_card_collections()
        for key in all_stats:
            all_stats[key] += stats[key]
        
        # Migrate achievements
        stats = await migrate_achievements()
        for key in all_stats:
            all_stats[key] += stats[key]
        
        print(f"\n{'='*50}")
        print("MIGRATION COMPLETE")
        print(f"{'='*50}")
        print(f"Total documents processed: {all_stats['total_docs']}")
        print(f"Documents migrated: {all_stats['migrated_docs']}")
        print(f"URLs migrated: {all_stats['migrated_urls']}")
        print(f"Errors: {all_stats['errors']}")
        
    elif choice == '2':
        print("\nAvailable collections:")
        print("1. cards")
        print("2. listings")
        print("3. users")
        print("4. pokemon, one_piece, magic (card collections)")
        print("5. achievements")
        
        coll_choice = input("\nSelect collection (1-5): ")
        
        if coll_choice == '1':
            await migrate_collection('cards', ['image_url'])
        elif coll_choice == '2':
            await migrate_collection('listings', ['image_url'])
        elif coll_choice == '3':
            await migrate_collection('users', ['avatarUrl'])
        elif coll_choice == '4':
            await migrate_card_collections()
        elif coll_choice == '5':
            await migrate_achievements()
        else:
            print("Invalid choice")
            
    elif choice == '3':
        print("\nRunning dry run verification...")
        # Run the verify script
        import subprocess
        subprocess.run(['python', 'verify_urls_to_migrate.py'])
        
    elif choice == '4':
        print("Exiting...")
    else:
        print("Invalid choice. Exiting...")

if __name__ == "__main__":
    asyncio.run(main())