name: Build Docker Image

on:
  push:
    branches: [ main, master ]
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm install
    
    - name: Build project
      run: npm run build:skip:prod
    
    - name: Prepare Docker context
      run: |
        cd boxed-admin-image
        cp -r ../dist .
    
    - name: Build Docker image
      run: |
        cd boxed-admin-image
        docker build -t boxed-admin:latest .
    
    - name: Save Docker image
      run: |
        docker save -o boxed-admin.tar boxed-admin:latest
    
    - name: Upload Docker image
      uses: actions/upload-artifact@v3
      with:
        name: docker-image
        path: boxed-admin.tar
        retention-days: 30
