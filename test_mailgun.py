import os
import requests
def send_simple_message():
  	return requests.post(
  		"https://api.mailgun.net/v3/sandbox8cfcd36a145642ff953f9280ab213285.mailgun.org/messages",
  		auth=("api", os.getenv('API_KEY', '**************************************************')),
  		data={"from": "Mailgun Sandbox <<EMAIL>>",
			"to": "<PERSON><PERSON><PERSON> <<EMAIL>>",
  			"subject": "Hello <PERSON><PERSON><PERSON> Qi",
  			"text": "Congratulations <PERSON><PERSON><PERSON>, you just sent an email with <PERSON><PERSON>! You are truly awesome!"})


response = send_simple_message()
print(response.status_code, response.text)