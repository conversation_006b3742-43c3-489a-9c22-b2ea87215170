#!/usr/bin/env python3
"""
Migrate pack image URLs and their cards subcollection image URLs from GCS signed URLs to R2 public URLs.

Structure:
- packs/
  - one_piece/
    - one_piece_test (document with image_url field)
      - cards/ (subcollection)
        - card1 (document with image_url field)
        - card2 (document with image_url field)
  - pokemon/
    - pokemon_pack1 (document with image_url field)
      - cards/ (subcollection)
        - card1 (document with image_url field)
"""

import asyncio
import re
from typing import Dict, Any, Optional
from google.cloud import firestore
from google.oauth2 import service_account
import json
import os

# Initialize Firestore client
def get_firestore_client():
    # Check if running in production (Cloud Run)
    if os.getenv('K_SERVICE'):
        # Use default credentials in Cloud Run
        return firestore.Client()
    else:
        # Use service account for local development
        creds_path = os.getenv('GOOGLE_APPLICATION_CREDENTIALS')
        if creds_path and os.path.exists(creds_path):
            credentials = service_account.Credentials.from_service_account_file(creds_path)
            return firestore.Client(credentials=credentials)
        else:
            return firestore.Client()

db = get_firestore_client()

def extract_r2_url(url: str) -> Optional[str]:
    """
    Extract R2 URL from various URL formats.
    Returns None if the URL should not be migrated.
    """
    if not url:
        return None
    
    # Skip if already an R2 URL
    if 'zapull.fun' in url:
        return None
    
    # Extract bucket and path from GCS URLs
    # Pattern 1: gs://bucket/path
    gs_match = re.match(r'gs://([^/]+)/(.+)', url)
    if gs_match:
        bucket = gs_match.group(1)
        path = gs_match.group(2)
        
        # Map GCS buckets to R2 buckets
        bucket_mapping = {
            'zapull-achievement': 'achievement.zapull.fun',
            'zapull-packs': 'pack.zapull.fun',
            'zapull-cards': 'card.zapull.fun',
            'zapull-avatar': 'avatar.zapull.fun',
            'zapull-avator': 'avatar.zapull.fun',  # Fix typo
            'pack_covers': 'pack.zapull.fun',
            'pack_covers_production': 'pack.zapull.fun',
            'pokemon_cards_pull': 'card.zapull.fun',
            'pokemon_cards_pull_production': 'card.zapull.fun',
        }
        
        r2_bucket = bucket_mapping.get(bucket)
        if r2_bucket:
            return f"https://{r2_bucket}/{path}"
    
    # Pattern 2: https://storage.googleapis.com/bucket/path
    gcs_https_match = re.match(r'https://storage\.googleapis\.com/([^/]+)/(.+)', url)
    if gcs_https_match:
        bucket = gcs_https_match.group(1)
        path = gcs_https_match.group(2)
        
        # Remove any query parameters (signed URL parameters)
        path = path.split('?')[0]
        
        # Map GCS buckets to R2 buckets
        bucket_mapping = {
            'zapull-achievement': 'achievement.zapull.fun',
            'zapull-packs': 'pack.zapull.fun',
            'zapull-cards': 'card.zapull.fun',
            'zapull-avatar': 'avatar.zapull.fun',
            'zapull-avator': 'avatar.zapull.fun',
            'pack_covers': 'pack.zapull.fun',
            'pack_covers_production': 'pack.zapull.fun',
            'pokemon_cards_pull': 'card.zapull.fun',
            'pokemon_cards_pull_production': 'card.zapull.fun',
        }
        
        r2_bucket = bucket_mapping.get(bucket)
        if r2_bucket:
            return f"https://{r2_bucket}/{path}"
    
    # Pattern 3: Signed URLs with query parameters (including GoogleAccessId)
    if 'storage.googleapis.com' in url:
        # Extract the bucket and path from signed URL
        match = re.match(r'https://storage\.googleapis\.com/([^/]+)/([^?]+)', url)
        if match:
            bucket = match.group(1)
            path = match.group(2)
            
            bucket_mapping = {
                'zapull-achievement': 'achievement.zapull.fun',
                'zapull-packs': 'pack.zapull.fun',
                'zapull-cards': 'card.zapull.fun',
                'zapull-avatar': 'avatar.zapull.fun',
                'zapull-avator': 'avatar.zapull.fun',
                'pack_covers': 'pack.zapull.fun',
                'pack_covers_production': 'pack.zapull.fun',
                'pokemon_cards_pull': 'card.zapull.fun',
                'pokemon_cards_pull_production': 'card.zapull.fun',
            }
            
            r2_bucket = bucket_mapping.get(bucket)
            if r2_bucket:
                return f"https://{r2_bucket}/{path}"
    
    return None

async def migrate_packs_collection():
    """Migrate all packs and their cards to R2 URLs."""
    
    packs_ref = db.collection('packs')
    pack_collections = ['magic', 'one_piece', 'pokemon']  # Updated to include magic
    
    total_packs_migrated = 0
    total_cards_migrated = 0
    
    for collection_name in pack_collections:
        print(f"\n{'='*50}")
        print(f"Processing packs/{collection_name}...")
        print(f"{'='*50}")
        
        # Get the document reference for this pack collection (e.g., /packs/pokemon)
        collection_doc_ref = packs_ref.document(collection_name)
        
        # Get all subcollections under this document (e.g., /packs/pokemon/pokemon, /packs/pokemon/one_piece)
        subcollections = collection_doc_ref.collections()
        
        for subcollection in subcollections:
            subcollection_name = subcollection.id
            print(f"\nProcessing subcollection: packs/{collection_name}/{subcollection_name}")
            
            # Get all pack documents in this subcollection
            pack_docs = subcollection.stream()
            
            for pack_doc in pack_docs:
                pack_data = pack_doc.to_dict()
                pack_doc_id = pack_doc.id
                print(f"  Processing pack: {pack_doc_id}")
                
                # Check if pack has image_url
                if pack_data and 'image_url' in pack_data:
                    old_url = pack_data['image_url']
                    new_url = extract_r2_url(old_url)
                    
                    if new_url:
                        print(f"    Migrating pack image_url:")
                        print(f"      From: {old_url}")
                        print(f"      To: {new_url}")
                        
                        # Update the pack document
                        pack_doc.reference.update({'image_url': new_url})
                        total_packs_migrated += 1
                    else:
                        print(f"    Pack image_url already migrated or invalid: {old_url}")
                
                # Now process the cards subcollection
                cards_ref = pack_doc.reference.collection('cards')
                cards = cards_ref.stream()
                
                cards_count = 0
                migrated_cards_count = 0
                for card_doc in cards:
                    card_data = card_doc.to_dict()
                    card_id = card_doc.id
                    cards_count += 1
                    
                    if card_data and 'image_url' in card_data:
                        old_url = card_data['image_url']
                        new_url = extract_r2_url(old_url)
                        
                        if new_url:
                            print(f"      Migrating card {card_id} image_url:")
                            print(f"        From: {old_url}")
                            print(f"        To: {new_url}")
                            
                            # Update the card document
                            card_doc.reference.update({'image_url': new_url})
                            migrated_cards_count += 1
                            total_cards_migrated += 1
                        else:
                            print(f"      Card {card_id} image_url already migrated or invalid: {old_url}")
                
                print(f"    Total cards in pack: {cards_count}, migrated: {migrated_cards_count}")
    
    print(f"\n{'='*50}")
    print(f"Migration Summary:")
    print(f"  Total packs migrated: {total_packs_migrated}")
    print(f"  Total cards migrated: {total_cards_migrated}")
    print(f"{'='*50}")

async def verify_migration():
    """Verify that all URLs have been migrated."""
    
    packs_ref = db.collection('packs')
    pack_collections = ['magic', 'one_piece', 'pokemon']
    
    unmigrated_packs = []
    unmigrated_cards = []
    
    for collection_name in pack_collections:
        collection_doc_ref = packs_ref.document(collection_name)
        subcollections = collection_doc_ref.collections()
        
        for subcollection in subcollections:
            subcollection_name = subcollection.id
            pack_docs = subcollection.stream()
            
            for pack_doc in pack_docs:
                pack_data = pack_doc.to_dict()
                pack_path = f"packs/{collection_name}/{subcollection_name}/{pack_doc.id}"
                
                # Check pack image_url
                if pack_data and 'image_url' in pack_data:
                    url = pack_data['image_url']
                    if url and ('storage.googleapis.com' in url or url.startswith('gs://')):
                        unmigrated_packs.append({
                            'path': pack_path,
                            'url': url
                        })
                
                # Check cards
                cards_ref = pack_doc.reference.collection('cards')
                cards = cards_ref.stream()
                
                for card_doc in cards:
                    card_data = card_doc.to_dict()
                    card_path = f"{pack_path}/cards/{card_doc.id}"
                    
                    if card_data and 'image_url' in card_data:
                        url = card_data['image_url']
                        if url and ('storage.googleapis.com' in url or url.startswith('gs://')):
                            unmigrated_cards.append({
                                'path': card_path,
                                'url': url
                            })
    
    print("\n" + "="*50)
    print("Verification Results:")
    print("="*50)
    
    if unmigrated_packs:
        print(f"\nUnmigrated packs ({len(unmigrated_packs)}):")
        for item in unmigrated_packs[:5]:  # Show first 5
            print(f"  {item['path']}: {item['url']}")
        if len(unmigrated_packs) > 5:
            print(f"  ... and {len(unmigrated_packs) - 5} more")
    else:
        print("\nAll packs have been migrated!")
    
    if unmigrated_cards:
        print(f"\nUnmigrated cards ({len(unmigrated_cards)}):")
        for item in unmigrated_cards[:5]:  # Show first 5
            print(f"  {item['path']}: {item['url']}")
        if len(unmigrated_cards) > 5:
            print(f"  ... and {len(unmigrated_cards) - 5} more")
    else:
        print("\nAll cards have been migrated!")

def main():
    print("Pack URL Migration Script")
    print("========================")
    print("\nThis script will migrate pack image URLs and their cards from GCS to R2.")
    print("\nOptions:")
    print("1. Migrate all packs and cards")
    print("2. Verify migration status")
    print("3. Exit")
    
    choice = input("\nEnter your choice (1-3): ")
    
    if choice == '1':
        print("\nStarting migration...")
        asyncio.run(migrate_packs_collection())
    elif choice == '2':
        print("\nVerifying migration status...")
        asyncio.run(verify_migration())
    elif choice == '3':
        print("Exiting...")
    else:
        print("Invalid choice. Exiting...")

if __name__ == "__main__":
    main()