# This example shows how to use Secret Manager with Cloud Build
availableSecrets:
  secretManager:
    - versionName: projects/$PROJECT_ID/secrets/firebase-api-key/versions/latest
      env: 'FIREBASE_API_KEY'
    - versionName: projects/$PROJECT_ID/secrets/firebase-auth-domain/versions/latest
      env: 'FIREBASE_AUTH_DOMAIN'
    # Add more secrets as needed

steps:
  # Build the Docker image with secrets
  - name: 'gcr.io/cloud-builders/docker'
    args: 
      - 'build'
      - '-t'
      - 'gcr.io/$PROJECT_ID/zapull-frontend'
      - '--build-arg'
      - 'NEXT_PUBLIC_FIREBASE_API_KEY=$$FIREBASE_API_KEY'
      - '--build-arg'
      - 'NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=$$FIREBASE_AUTH_DOMAIN'
      # Add more build args
      - '.'
    secretEnv: ['FIREBASE_API_KEY', 'FIREBASE_AUTH_DOMAIN']
    
  # Rest of your build steps...
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/zapull-frontend']
    
  - name: 'gcr.io/cloud-builders/gcloud'
    args:
      - 'run'
      - 'deploy'
      - 'zapull-frontend'
      - '--image=gcr.io/$PROJECT_ID/zapull-frontend'
      - '--platform=managed'
      - '--region=us-central1'
      - '--allow-unauthenticated'
      - '--port=3000'
      - '--memory=512Mi'
      - '--cpu=1'
      - '--min-instances=0'
      - '--max-instances=10'
      
images:
  - 'gcr.io/$PROJECT_ID/zapull-frontend'
  
timeout: '1200s'